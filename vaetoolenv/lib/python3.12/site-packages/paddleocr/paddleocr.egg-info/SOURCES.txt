LICENSE
MANIFEST.in
README.md
README_en.md
__init__.py
paddleocr.py
pyproject.toml
requirements.txt
setup.cfg
setup.py
paddleocr.egg-info/PKG-INFO
paddleocr.egg-info/SOURCES.txt
paddleocr.egg-info/dependency_links.txt
paddleocr.egg-info/entry_points.txt
paddleocr.egg-info/requires.txt
paddleocr.egg-info/top_level.txt
ppocr/__init__.py
ppocr/data/__init__.py
ppocr/data/collate_fn.py
ppocr/data/latexocr_dataset.py
ppocr/data/lmdb_dataset.py
ppocr/data/multi_scale_sampler.py
ppocr/data/pgnet_dataset.py
ppocr/data/pubtab_dataset.py
ppocr/data/simple_dataset.py
ppocr/data/imaug/ColorJitter.py
ppocr/data/imaug/__init__.py
ppocr/data/imaug/abinet_aug.py
ppocr/data/imaug/copy_paste.py
ppocr/data/imaug/ct_process.py
ppocr/data/imaug/drrg_targets.py
ppocr/data/imaug/east_process.py
ppocr/data/imaug/fce_aug.py
ppocr/data/imaug/fce_targets.py
ppocr/data/imaug/iaa_augment.py
ppocr/data/imaug/label_ops.py
ppocr/data/imaug/latex_ocr_aug.py
ppocr/data/imaug/make_border_map.py
ppocr/data/imaug/make_pse_gt.py
ppocr/data/imaug/make_shrink_map.py
ppocr/data/imaug/operators.py
ppocr/data/imaug/pg_process.py
ppocr/data/imaug/randaugment.py
ppocr/data/imaug/random_crop_data.py
ppocr/data/imaug/rec_img_aug.py
ppocr/data/imaug/sast_process.py
ppocr/data/imaug/ssl_img_aug.py
ppocr/data/imaug/table_ops.py
ppocr/data/imaug/unimernet_aug.py
ppocr/data/imaug/frost_img/frost1.jpg
ppocr/data/imaug/frost_img/frost2.png
ppocr/data/imaug/frost_img/frost3.png
ppocr/data/imaug/frost_img/frost4.jpg
ppocr/data/imaug/frost_img/frost5.jpg
ppocr/data/imaug/frost_img/frost6.jpg
ppocr/data/imaug/text_image_aug/__init__.py
ppocr/data/imaug/text_image_aug/augment.py
ppocr/data/imaug/text_image_aug/warp_mls.py
ppocr/data/imaug/vqa/__init__.py
ppocr/data/imaug/vqa/augment.py
ppocr/data/imaug/vqa/token/__init__.py
ppocr/data/imaug/vqa/token/vqa_re_convert.py
ppocr/data/imaug/vqa/token/vqa_token_chunk.py
ppocr/data/imaug/vqa/token/vqa_token_pad.py
ppocr/data/imaug/vqa/token/vqa_token_relation.py
ppocr/postprocess/__init__.py
ppocr/postprocess/cls_postprocess.py
ppocr/postprocess/ct_postprocess.py
ppocr/postprocess/db_postprocess.py
ppocr/postprocess/drrg_postprocess.py
ppocr/postprocess/east_postprocess.py
ppocr/postprocess/fce_postprocess.py
ppocr/postprocess/locality_aware_nms.py
ppocr/postprocess/pg_postprocess.py
ppocr/postprocess/picodet_postprocess.py
ppocr/postprocess/rec_postprocess.py
ppocr/postprocess/sast_postprocess.py
ppocr/postprocess/table_postprocess.py
ppocr/postprocess/vqa_token_re_layoutlm_postprocess.py
ppocr/postprocess/vqa_token_ser_layoutlm_postprocess.py
ppocr/postprocess/pse_postprocess/__init__.py
ppocr/postprocess/pse_postprocess/pse_postprocess.py
ppocr/postprocess/pse_postprocess/pse/README.md
ppocr/postprocess/pse_postprocess/pse/__init__.py
ppocr/postprocess/pse_postprocess/pse/pse.pyx
ppocr/postprocess/pse_postprocess/pse/setup.py
ppocr/utils/EN_symbol_dict.txt
ppocr/utils/__init__.py
ppocr/utils/dict90.txt
ppocr/utils/en_dict.txt
ppocr/utils/export_model.py
ppocr/utils/gen_label.py
ppocr/utils/ic15_dict.txt
ppocr/utils/iou.py
ppocr/utils/logging.py
ppocr/utils/network.py
ppocr/utils/poly_nms.py
ppocr/utils/ppocr_keys_v1.txt
ppocr/utils/profiler.py
ppocr/utils/save_load.py
ppocr/utils/stats.py
ppocr/utils/utility.py
ppocr/utils/visual.py
ppocr/utils/dict/README.md
ppocr/utils/dict/ar_dict.txt
ppocr/utils/dict/arabic_dict.txt
ppocr/utils/dict/be_dict.txt
ppocr/utils/dict/bengali_dict.txt
ppocr/utils/dict/bg_dict.txt
ppocr/utils/dict/bm_dict.txt
ppocr/utils/dict/bm_dict_add.txt
ppocr/utils/dict/bn_dict.txt
ppocr/utils/dict/chinese_cht_dict.txt
ppocr/utils/dict/confuse.pkl
ppocr/utils/dict/cyrillic_dict.txt
ppocr/utils/dict/devanagari_dict.txt
ppocr/utils/dict/en_dict.txt
ppocr/utils/dict/fa_dict.txt
ppocr/utils/dict/french_dict.txt
ppocr/utils/dict/german_dict.txt
ppocr/utils/dict/gujarati_dict.txt
ppocr/utils/dict/hebrew_dict.txt
ppocr/utils/dict/hi_dict.txt
ppocr/utils/dict/it_dict.txt
ppocr/utils/dict/japan_dict.txt
ppocr/utils/dict/ka_dict.txt
ppocr/utils/dict/kazakh_dict.txt
ppocr/utils/dict/korean_dict.txt
ppocr/utils/dict/latex_ocr_tokenizer.json
ppocr/utils/dict/latex_symbol_dict.txt
ppocr/utils/dict/latin_dict.txt
ppocr/utils/dict/mr_dict.txt
ppocr/utils/dict/ne_dict.txt
ppocr/utils/dict/oc_dict.txt
ppocr/utils/dict/parseq_dict.txt
ppocr/utils/dict/ppocrv4_doc_dict.txt
ppocr/utils/dict/pu_dict.txt
ppocr/utils/dict/rs_dict.txt
ppocr/utils/dict/rsc_dict.txt
ppocr/utils/dict/ru_dict.txt
ppocr/utils/dict/samaritan_dict.txt
ppocr/utils/dict/spin_dict.txt
ppocr/utils/dict/syriac_dict.txt
ppocr/utils/dict/ta_dict.txt
ppocr/utils/dict/table_dict.txt
ppocr/utils/dict/table_master_structure_dict.txt
ppocr/utils/dict/table_structure_dict.txt
ppocr/utils/dict/table_structure_dict_ch.txt
ppocr/utils/dict/te_dict.txt
ppocr/utils/dict/th_dict.txt
ppocr/utils/dict/ug_dict.txt
ppocr/utils/dict/uk_dict.txt
ppocr/utils/dict/ur_dict.txt
ppocr/utils/dict/vi_dict.txt
ppocr/utils/dict/xi_dict.txt
ppocr/utils/dict/kie_dict/xfund_class_list.txt
ppocr/utils/dict/layout_dict/layout_cdla_dict.txt
ppocr/utils/dict/layout_dict/layout_publaynet_dict.txt
ppocr/utils/dict/layout_dict/layout_table_dict.txt
ppocr/utils/dict/unimernet_tokenizer/tokenizer.json
ppocr/utils/dict/unimernet_tokenizer/tokenizer_config.json
ppocr/utils/e2e_metric/Deteval.py
ppocr/utils/e2e_metric/polygon_fast.py
ppocr/utils/e2e_utils/extract_batchsize.py
ppocr/utils/e2e_utils/extract_textpoint_fast.py
ppocr/utils/e2e_utils/extract_textpoint_slow.py
ppocr/utils/e2e_utils/pgnet_pp_utils.py
ppocr/utils/e2e_utils/visual.py
ppocr/utils/formula_utils/math_txt2pkl.py
ppocr/utils/formula_utils/unimernet_data_convert.py
ppocr/utils/loggers/__init__.py
ppocr/utils/loggers/base_logger.py
ppocr/utils/loggers/loggers.py
ppocr/utils/loggers/wandb_logger.py
ppstructure/README.md
ppstructure/__init__.py
ppstructure/predict_system.py
ppstructure/utility.py
ppstructure/kie/README.md
ppstructure/kie/README_ch.md
ppstructure/kie/how_to_do_kie.md
ppstructure/kie/how_to_do_kie_en.md
ppstructure/kie/predict_kie_token_ser.py
ppstructure/kie/predict_kie_token_ser_re.py
ppstructure/kie/requirements.txt
ppstructure/kie/tools/eval_with_label_end2end.py
ppstructure/kie/tools/trans_funsd_label.py
ppstructure/kie/tools/trans_xfun_data.py
ppstructure/layout/README.md
ppstructure/layout/README_ch.md
ppstructure/layout/__init__.py
ppstructure/layout/predict_layout.py
ppstructure/pdf2word/README.md
ppstructure/pdf2word/pdf2word.py
ppstructure/pdf2word/icons/chinese.png
ppstructure/pdf2word/icons/english.png
ppstructure/pdf2word/icons/folder-open.png
ppstructure/pdf2word/icons/folder-plus.png
ppstructure/recovery/README.md
ppstructure/recovery/README_ch.md
ppstructure/recovery/__init__.py
ppstructure/recovery/recovery_to_doc.py
ppstructure/recovery/recovery_to_markdown.py
ppstructure/recovery/requirements.txt
ppstructure/recovery/table_process.py
ppstructure/table/README.md
ppstructure/table/README_ch.md
ppstructure/table/__init__.py
ppstructure/table/convert_label2html.py
ppstructure/table/eval_table.py
ppstructure/table/matcher.py
ppstructure/table/predict_structure.py
ppstructure/table/predict_table.py
ppstructure/table/table_master_match.py
ppstructure/table/table_metric/__init__.py
ppstructure/table/table_metric/parallel.py
ppstructure/table/table_metric/table_metric.py
ppstructure/table/tablepyxl/__init__.py
ppstructure/table/tablepyxl/style.py
ppstructure/table/tablepyxl/tablepyxl.py
tools/__init__.py
tools/eval.py
tools/export_center.py
tools/export_model.py
tools/infer_cls.py
tools/infer_det.py
tools/infer_e2e.py
tools/infer_kie.py
tools/infer_kie_token_ser.py
tools/infer_kie_token_ser_re.py
tools/infer_rec.py
tools/infer_sr.py
tools/infer_table.py
tools/naive_sync_bn.py
tools/program.py
tools/test_hubserving.py
tools/train.py
tools/end2end/convert_ppocr_label.py
tools/end2end/draw_html.py
tools/end2end/eval_end2end.py
tools/end2end/readme.md
tools/infer/predict_cls.py
tools/infer/predict_det.py
tools/infer/predict_e2e.py
tools/infer/predict_rec.py
tools/infer/predict_sr.py
tools/infer/predict_system.py
tools/infer/utility.py