环境搭建：
```
python -m venv vaetoolenv
source vaetoolenv/bin/activate  # Linux/macOS
pip install -r requirements.txt
```

使用方法：
./run_check.sh oss://vlane/aonebug/record/20250703_100536/
数据结构：
├── 20250702_113807
│   ├── AutoSdkDemo
│   │   ├── online
│   │   │   └── vlane
│   │   │       ├── 20250702_113807
│   │   │       │   ├── data.txt
│   │   │       │   ├── data_GT.txt
│   │   │       │   └── descInfo
│   │   │       │       └── info.txt
│   │   │       ├── 20250702_113807_online_mhe_1751427487330
│   │   │       │   ├── afs.txt
│   │   │       │   └── mhe.txt
│   │   │       └── summary.txt
│   │   └── replay_record
│   │       └── 2025_07_02
│   │           ├── 2025_07_02_11_38_06
│   │           │   ├── 20250702113806.lanedata2
│   │           │   ├── 20250702113806.laneres
│   │           │   └── image
│   │           │       └── 2025_07_02_11_38_07
│   │           │           ├── 20250702113807315_0_2000030_1920_1080_35704461.jpeg
│   │           │           ├── 20250702113807821_1_2000030_1920_1080_35704961.jpeg
│   │           │           ├── 20250702113808315_2_2000030_1920_1080_35705460.jpeg
│   │           │           ├── 20250702113808817_3_2000030_1920_1080_35705960.jpeg
│   ├── CheckResult
│   │   └── C13GyroAccuracy
│   │       └── C13GyroAccuracy_20250703182439.log
│   └── GT_20250703_plaintext
│       ├── 20250306112045.TXT
│       ├── 20250306112045.gps
│       ├── 20250306112045.gps.zip
│       ├── 20250306112045log.txt
│       ├── city_info.json
│       ├── gt_correction_info
│       │   └── gt_correction_info.txt
│       ├── gt_refine_info_json.json
│       ├── lane_gt.json
│       ├── lane_gt.txt
│       ├── lane_gt_hq.txt
│       ├── lane_gt_ud.txt
│       ├── loc_gt.txt
│       ├── loc_gt_orig.txt
│       ├── loc_gt_raw.txt
│       ├── log.txt
│       ├── qianxun
│       │   ├── 124443_20250306112045.gps_qx.csv
│       │   ├── 20250306112045log.txt
│       │   └── log.txt
│       ├── qianxun_info.txt
│       ├── qx_gt.csv
│       └── qx_gt_gaotie.csv
