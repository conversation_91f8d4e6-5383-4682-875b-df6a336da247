#!/bin/bash

# 获取当前脚本所在目录并切换至该目录
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
cd "$SCRIPT_DIR" || { echo "无法进入脚本目录 $SCRIPT_DIR"; exit 1; }

if [ -z "$1" ]; then
    echo "❌ 请提供文件夹路径作为参数。用法：./your_script.sh /path/to/folder"
    exit 1
fi

# # 执行下载脚本 data_loader.py 并传入路径参数
python3 data_loader.py -i "$1" || { echo "data_loader.py 执行失败"; exit 1; }
echo "data_loader.py 执行成功,数据下载完成！！！！！！"
exit 0

date_part=$(basename "$1" | cut -d'/' -f1)

# 查找并排序 SignalChecerImpl 目录中的符合条件的 Python 脚本 
echo $SCRIPT_DIR
IFS=$'\n' read -r -d '' -a scripts < <(find SignalChecerImpl -type f -name 'c[0-1][0-9]_*.py' | sort -V)

# 检查是否有符合条件的脚本
if [ ${#scripts[@]} -eq 0 ]; then
  echo "未找到符合条件的脚本。"
else
  # 构造参数路径
  param_path="$SCRIPT_DIR/tests/demodata/$date_part"
  echo "正在执行以下脚本（按数字顺序）："
  failed_scripts=()

  for script in "${scripts[@]}"; do
    echo "▶ $script"
    if ! python3 "$script" -i "$param_path"; then
      echo "❌ 脚本 $script 执行失败"
      failed_scripts+=("$script")
    fi
  done

  # 汇总失败情况（可选）
  if [ ${#failed_scripts[@]} -gt 0 ]; then
    echo "以下脚本执行失败："
    for f in "${failed_scripts[@]}"; do
      echo "  - $f"
    done
    echo "部分任务失败，但已继续执行后续脚本。"
  fi
fi

# 执行 data_upload.py
python3 data_uploader.py -i "$1" || { echo "data_upload.py 执行失败"; exit 1; }

echo "所有任务完成。"
