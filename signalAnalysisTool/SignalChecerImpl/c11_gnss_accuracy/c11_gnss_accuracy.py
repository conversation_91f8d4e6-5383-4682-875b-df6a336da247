import logging
import sys
import os
import argparse
import json
from datetime import datetime
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from gnss_alignment_domain import LocGtRtk, AlignmentGnssRtk, \
    Gnss, CheckReport
from gnss_accuracy_utils import find_closest_number
from sorted_dict_operator import SortedDictOperator

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir)) # Up two levels to signalAnalysisTool
sys.path.insert(0, parent_dir)

try:
    from SignalCheckerInterface import SignalCheckerInterface
except ImportError:
    print("Could not import SignalCheckerInterface directly. Ensure signalAnalysisTool is in PYTHONPATH.")

class C11GnssAccuracy(SignalCheckerInterface):
    _checker_title= "GNSS精度检查"
    _checker_name= "C11GnssAccuracy"
    _data_input_path = ""
    _data_data_txt_path = ""
    _data_output_path = ""
    _data_output_check_result_json_path = ""
    _data_output_check_result_detail_json_path = ""
    _data_output_check_result_table_html_path = ""

    def init(self, input_path: str):
        """初始化传入数据路径"""
        self._data_input_path = input_path
        folder_name = os.path.basename(os.path.normpath(self._data_input_path))
        gt_folder = self.find_gt_folder()
        self._loc_gt_txt_path = os.path.join(self._data_input_path, f"{gt_folder}/loc_gt.txt") if gt_folder else None
        self._data_data_txt_path = os.path.join(self._data_input_path, f"AutoSdkDemo/online/vlane/{folder_name}/data.txt")
        self._data_output_path = os.path.join(self._data_input_path, "CheckResult/" + self.get_checker_name())
        self._data_output_check_result_json_path = os.path.join(self._data_output_path, "check_result.json")
        self._data_output_check_result_detail_json_path = os.path.join(self._data_output_path, "check_result_detail.json")
        self._data_output_check_result_table_html_path = os.path.join(self._data_output_path, "check_result_table.html")
        self._data_output_check_result_histogram_html_path = os.path.join(self._data_output_path, "check_result_histogram.html")
        self._data_output_check_result_scatter_html_path = os.path.join(self._data_output_path, "check_result_scatter.html")
        self.prepare_dirs()
        self._logger = self.setup_logger()
        self._logger.info("init")
        pass

    def check(self) -> str:
        """进行信号检查"""
        prepare_result, prepare_result_msg = self.prepare_dirs()
        if not prepare_result:
            return prepare_result_msg

        # 定义检查结果头
        cur_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        signals_check_result= {
            "checker": "C11GnssAccuracy",
            "title": f"{self._checker_title}",
            "datatxt": self._data_data_txt_path,
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResult"
        }
        signals_check_result_detail = {
            "checker": "C11GnssAccuracy",
            "title": f"{self._checker_title}详情",
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResultDetail"
        }
        signals_check_result_items = []
        signals_check_result_detail_items = []
        signals_check_result_columns = {"检查项":[], "统计数值":[],  "检查规则":[], "检查结果":[],"描述":[]}
        signals_check_result_detail_columns = {"检查项":[], "检查详情":[]}

        # 解析文件
        gnss_list = self.parse_file(self._data_data_txt_path)
        # 无效 'V' = 86， 有效 ‘A' = 65
        valid_gnss_list = [item for item in gnss_list if item.status==65]
        rtk_sorted_dict_operator = self.parse_loc_gt_file(self._loc_gt_txt_path)

        # 数据对齐
        alignment_data_list, unmatched_gnss_data_list = self.alignment_data(valid_gnss_list, rtk_sorted_dict_operator)
        if len(alignment_data_list) == 0:
            return "failed, no alignment data"

        for alignment in alignment_data_list:
            alignment.calc_rkt_gnss_lon_lat_diff()
            alignment.calc_rkt_gnss_alt_diff()
            alignment.calc_rkt_gnss_speed_diff()
            alignment.calc_rkt_gnss_course_diff()
            alignment.calc_rkt_gnss_accuracy_radius_diff(alignment.rtk_gnss_lon_lat_diff)
            alignment.calc_rtk_gnss_fail_point_one(alignment.rtk_gnss_lon_lat_diff, alignment.rtk_gnss_course_diff, alignment.gnss.accuracy)
            alignment.calc_rtk_gnss_fail_point_two(alignment.rtk_gnss_lon_lat_diff, alignment.rtk_gnss_course_diff, alignment.gnss.accuracy)

        # 真值覆盖度
        true_coverage_check_result = self.calc_true_converage_check_result(gnss_list, rtk_sorted_dict_operator)

        # 统计量-平面位置精度
        lon_lat_diff_check_result = self.calc_lon_lat_diff_check_result(alignment_data_list)

        # 统计量-高程精度
        alt_diff_check_result = self.calc_alt_diff_check_result(alignment_data_list)

        # 统计量-速度精度
        speed_diff_check_result = self.calc_speed_diff_check_result(alignment_data_list)

        # 统计量-航向精度
        course_diff_check_result = self.calc_course_diff_check_result(alignment_data_list)

        # 统计量-非低速航向精度
        course_diff_non_low_speed_check_result = self.calc_course_diff_non_low_speed_check_result(alignment_data_list)

        # 统计量-平面精度半径精度
        accuracy_radius_diff_check_result = self.calc_accuracy_radius_diff_check_result(alignment_data_list)

        # 假真点
        fail_point_check_result = self.calc_fail_point_check_result(alignment_data_list)

        check_report_list = [true_coverage_check_result, lon_lat_diff_check_result, alt_diff_check_result,
                             speed_diff_check_result, course_diff_check_result, course_diff_non_low_speed_check_result,
                             accuracy_radius_diff_check_result, fail_point_check_result]

        for item in check_report_list:
            current_check_result = {
                "checkItem": item.check_item,
                "checkResult": item.check_result,
                "checkValue": item.check_value,
                "checkRules": item.check_rules,
                "description": item.description
            }
            signals_check_result_items.append(current_check_result)

            signals_check_result_columns["检查项"].append(item.check_item)
            signals_check_result_columns["检查结果"].append(item.check_result)
            signals_check_result_columns["统计数值"].append(item.check_value)
            signals_check_result_columns["检查规则"].append(item.check_rules)
            signals_check_result_columns["描述"].append(item.description)

        # 组装完整检查结果
        signals_check_result["checkResult"] = signals_check_result_items
        signals_check_result_detail["checkResultDetail"] = signals_check_result_detail_items

        # 保存结果
        with open(self._data_output_check_result_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result, f, ensure_ascii=False, indent=4)
        with open(self._data_output_check_result_detail_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result_detail, f, ensure_ascii=False, indent=4)

        valid_check_report_list = [item for item in check_report_list if item.data_list is not None]

        # 生成表格
        self.generate_table(signals_check_result_columns, signals_check_result_detail_columns, cur_date)
        self.generate_histogram(valid_check_report_list, cur_date)
        self.generate_scatter(valid_check_report_list, cur_date)

        # 处理完成
        return "success"

    def calc_true_converage_check_result(self, gnss_list, rtk_sorted_dict_operator):
        gnss_source_tick_nano_time_list = [item.sourceTickTime * 1000 * 1000 for item in gnss_list]
        min_gnss_source_nano_tick_time = min(gnss_source_tick_nano_time_list)
        max_gnss_source_nano_tick_time = max(gnss_source_tick_nano_time_list)

        rtk_source_tick_nano_time_list = [source_tick_nano_time for source_tick_nano_time in rtk_sorted_dict_operator.get_keys()]
        min_rtk_source_nano_tick_time = min(rtk_source_tick_nano_time_list)
        max_rtk_source_nano_tick_time = max(rtk_source_tick_nano_time_list)

        closest_min_rtk_source_nano_tick_time = find_closest_number(gnss_source_tick_nano_time_list, min_rtk_source_nano_tick_time)
        closest_max_rtk_source_nano_tick_time = find_closest_number(gnss_source_tick_nano_time_list, max_rtk_source_nano_tick_time)

        diff_rtk_source_nano_tick_time = closest_max_rtk_source_nano_tick_time - closest_min_rtk_source_nano_tick_time
        diff_gnss_source_nano_tick_time = max_gnss_source_nano_tick_time - min_gnss_source_nano_tick_time
        # 小于0.95算失败
        rtk_source_nano_tick_time_coverage = diff_rtk_source_nano_tick_time / diff_gnss_source_nano_tick_time

        check_report = CheckReport()
        check_report.set_check_item("真值覆盖度")
        check_report.set_check_result("Pass" if rtk_source_nano_tick_time_coverage > 0.95 else "Failed")
        check_report.set_check_value(f"真值覆盖度:{rtk_source_nano_tick_time_coverage}<br>rtk时间戳范围:[{closest_min_rtk_source_nano_tick_time},{closest_max_rtk_source_nano_tick_time}]<br>gnss时间戳范围:[{min_gnss_source_nano_tick_time},{max_gnss_source_nano_tick_time}]")
        check_report.set_check_rules("rtk和gnss时间戳交集范围/gnss时间戳范围>0.95")
        check_report.set_description("不通过需手动检查")
        return check_report

    def calc_lon_lat_diff_check_result(self, alignment_data_list):
        source_tick_time_list = [item.gnss.sourceTickTime for item in alignment_data_list]
        rkt_gnss_lon_lat_diff_list = [item.rtk_gnss_lon_lat_diff for item in alignment_data_list]
        rkt_gnss_lon_lat_diff_median = np.median(rkt_gnss_lon_lat_diff_list)
        rkt_gnss_lon_lat_diff_quantiles_84 = np.percentile(rkt_gnss_lon_lat_diff_list, 84)
        success = self.hit_range(rkt_gnss_lon_lat_diff_median, 3) and self.hit_range(rkt_gnss_lon_lat_diff_quantiles_84,5)

        check_report = CheckReport()
        check_report.set_check_item("平面位置精度")
        check_report.set_check_result("Pass" if success else "Failed")
        check_report.set_check_value(f"median: {rkt_gnss_lon_lat_diff_median}<br>quantiles_84:{rkt_gnss_lon_lat_diff_quantiles_84}")
        check_report.set_check_rules("中位数小于3米 且 84%分位数小于5米")
        check_report.set_description("不通过需手动检查")
        check_report.set_source_tick_time_list(source_tick_time_list)
        check_report.set_data_list(rkt_gnss_lon_lat_diff_list)
        return check_report

    def calc_alt_diff_check_result(self, alignment_data_list):
        source_tick_time_list = [item.gnss.sourceTickTime for item in alignment_data_list]
        rtk_gnss_alt_diff_list = [item.rtk_gnss_alt_diff for item in alignment_data_list]
        rtk_gnss_alt_diff_median = np.median(rtk_gnss_alt_diff_list)
        rtk_gnss_alt_diff_quantiles_16 = np.percentile(rtk_gnss_alt_diff_list, 16)
        rtk_gnss_alt_diff_quantiles_84 = np.percentile(rtk_gnss_alt_diff_list, 84)
        success = self.hit_range(rtk_gnss_alt_diff_median, 15) and self.hit_range(rtk_gnss_alt_diff_quantiles_16, 15) and self.hit_range(rtk_gnss_alt_diff_quantiles_84, 15)

        check_report = CheckReport()
        check_report.set_check_item("高程精度")
        check_report.set_check_result("Pass" if success else "Warning")
        check_report.set_check_value(f"median:{rtk_gnss_alt_diff_median}<br>quantiles_16:{rtk_gnss_alt_diff_quantiles_16}<br>quantiles_84:{rtk_gnss_alt_diff_quantiles_84}")
        check_report.set_check_rules("WARNING规则：中位数/16%分位数/84%分位数都小于15米")
        check_report.set_description("不通过需手动检查")
        check_report.set_source_tick_time_list(source_tick_time_list)
        check_report.set_data_list(rtk_gnss_alt_diff_list)
        return check_report

    def calc_speed_diff_check_result(self, alignment_data_list):
        source_tick_time_list = [item.gnss.sourceTickTime for item in alignment_data_list]
        rtk_gnss_speed_diff_list = [item.rtk_gnss_speed_diff for item in alignment_data_list]
        rtk_gnss_speed_diff_mean = np.mean(rtk_gnss_speed_diff_list)  # 均值
        rtk_gnss_speed_diff_median = np.median(rtk_gnss_speed_diff_list)  # 中位数
        rtk_gnss_speed_diff_quantiles_16 = np.percentile(rtk_gnss_speed_diff_list, 16)
        rtk_gnss_speed_diff_quantiles_84 = np.percentile(rtk_gnss_speed_diff_list, 84)
        success = (self.hit_range(rtk_gnss_speed_diff_mean, 0.1) and self.hit_range(rtk_gnss_speed_diff_median, 0.1)
                   and self.hit_range(rtk_gnss_speed_diff_quantiles_16, 0.5) and self.hit_range(rtk_gnss_speed_diff_quantiles_84, 0.5))

        check_report = CheckReport()
        check_report.set_check_item("速度精度")
        check_report.set_check_result("Pass" if success else "Failed")
        check_report.set_check_value(f"mean:{rtk_gnss_speed_diff_mean}<br>median:{rtk_gnss_speed_diff_median}<br>quantiles_16:{rtk_gnss_speed_diff_quantiles_16}<br>quantiles_84:{rtk_gnss_speed_diff_quantiles_84}")
        check_report.set_check_rules("1. 均值 和 中位数都小于0.1km/h <br>2. 16%分位数/84%分位数都小于0.5km/h")
        check_report.set_description("不通过需手动检查")
        check_report.set_source_tick_time_list(source_tick_time_list)
        check_report.set_data_list(rtk_gnss_speed_diff_list)
        return check_report

    def calc_course_diff_check_result(self, alignment_data_list):
        source_tick_time_list = [item.gnss.sourceTickTime for item in alignment_data_list]
        rtk_gnss_course_diff_list = [item.rtk_gnss_course_diff for item in alignment_data_list]
        rtk_gnss_course_diff_median = np.median(rtk_gnss_course_diff_list)
        rtk_gnss_course_diff_quantiles_16 = np.percentile(rtk_gnss_course_diff_list, 16)
        rtk_gnss_course_diff_quantiles_84 = np.percentile(rtk_gnss_course_diff_list, 84)
        success = (self.hit_range(rtk_gnss_course_diff_median, 0.5) and self.hit_range(rtk_gnss_course_diff_quantiles_16, 1)
                   and self.hit_range(rtk_gnss_course_diff_quantiles_84, 1))

        check_report = CheckReport()
        check_report.set_check_item("航向精度")
        check_report.set_check_result("Pass" if success else "Warning")
        check_report.set_check_value(f"median:{rtk_gnss_course_diff_median}<br>quantiles_16:{rtk_gnss_course_diff_quantiles_16}<br>quantiles_84:{rtk_gnss_course_diff_quantiles_84}")
        check_report.set_check_rules("WARNING规则：航向误差中位数在[-0.5°,0.5°]<br>且16%分位数和84%分位数都在[-1°,1°]之内")
        check_report.set_description("不通过需手动检查")
        check_report.set_source_tick_time_list(source_tick_time_list)
        check_report.set_data_list(rtk_gnss_course_diff_list)
        return check_report

    def calc_course_diff_non_low_speed_check_result(self, alignment_data_list):
        source_tick_time_list = [item.gnss.sourceTickTime for item in alignment_data_list]
        rtk_gnss_course_diff_non_low_speed_list = [item.rtk_gnss_course_diff for item in alignment_data_list if item.gnss.speed > 5]
        rtk_gnss_course_diff_non_low_speed_median = np.median(rtk_gnss_course_diff_non_low_speed_list)
        rtk_gnss_course_diff_non_low_speed_quantiles_16 = np.percentile(rtk_gnss_course_diff_non_low_speed_list, 16)
        rtk_gnss_course_diff_non_low_speed_quantiles_84 = np.percentile(rtk_gnss_course_diff_non_low_speed_list, 84)
        success = (self.hit_range(rtk_gnss_course_diff_non_low_speed_median, 0.5) and self.hit_range(rtk_gnss_course_diff_non_low_speed_quantiles_16, 1)
                   and self.hit_range(rtk_gnss_course_diff_non_low_speed_quantiles_84, 1))

        check_report = CheckReport()
        check_report.set_check_item("非低速航向精度")
        check_report.set_check_result("Pass" if success else "Failed")
        check_report.set_check_value(f"median:{rtk_gnss_course_diff_non_low_speed_median}<br>quantiles_16:{rtk_gnss_course_diff_non_low_speed_quantiles_16}<br>quantiles_84:{rtk_gnss_course_diff_non_low_speed_quantiles_84}")
        check_report.set_check_rules("FAIL规则：非低速航向误差中位数小于[-0.5°,0.5°]<br>且16%分位数和84%分位数都在[-1°,1°]之内")
        check_report.set_description("不通过需手动检查")
        check_report.set_source_tick_time_list(source_tick_time_list)
        check_report.set_data_list(rtk_gnss_course_diff_non_low_speed_list)
        return check_report

    def calc_accuracy_radius_diff_check_result(self, alignment_data_list):
        source_tick_time_list = [item.gnss.sourceTickTime for item in alignment_data_list]
        rtk_gnss_accuracy_radius_diff_list = [item.rtk_gnss_accuracy_radius_diff for item in alignment_data_list]
        rtk_gnss_accuracy_radius_diff_mean = np.mean(rtk_gnss_accuracy_radius_diff_list)
        rtk_gnss_accuracy_radius_diff_median = np.median(rtk_gnss_accuracy_radius_diff_list)
        rtk_gnss_accuracy_radius_diff_quantiles_16 = np.percentile(rtk_gnss_accuracy_radius_diff_list, 16)
        rtk_gnss_accuracy_radius_diff_quantiles_84 = np.percentile(rtk_gnss_accuracy_radius_diff_list, 84)
        success = (self.hit_range(rtk_gnss_accuracy_radius_diff_mean, 5) and self.hit_range(rtk_gnss_accuracy_radius_diff_median, 5)
                   and self.hit_range(rtk_gnss_accuracy_radius_diff_quantiles_16, 5) and self.hit_range(rtk_gnss_accuracy_radius_diff_quantiles_84, 5))

        check_report = CheckReport()
        check_report.set_check_item("平面精度半径精度-WARNING")
        check_report.set_check_result("Pass" if success else "Warning")
        check_report.set_check_value(f"mean:{rtk_gnss_accuracy_radius_diff_mean}<br>median:{rtk_gnss_accuracy_radius_diff_median}<br>quantiles_16:{rtk_gnss_accuracy_radius_diff_quantiles_16}<br>quantiles_84:{rtk_gnss_accuracy_radius_diff_quantiles_84}")
        check_report.set_check_rules("WARNING规则：均值/中位数/16%分位数/84%分位数都小于5米")
        check_report.set_description("不通过需手动检查")
        check_report.set_source_tick_time_list(source_tick_time_list)
        check_report.set_data_list(rtk_gnss_accuracy_radius_diff_list)
        return check_report

    def calc_fail_point_check_result(self, alignment_data_list):
        fail_point_one_cnt = len([item for item in alignment_data_list if item.rtk_gnss_fail_point_one])
        fail_point_two_cnt = len([item for item in alignment_data_list if item.rtk_gnss_fail_point_two])
        total_point_cnt = len(alignment_data_list)

        fail_point_one_ratio = fail_point_one_cnt / total_point_cnt
        fail_point_two_ratio = fail_point_two_cnt / total_point_cnt

        fail_point_one_repeat_groups = self.build_repeat_frame_groups(alignment_data_list, lambda item: item.rtk_gnss_fail_point_one)
        fail_point_two_repeat_groups = self.build_repeat_frame_groups(alignment_data_list, lambda item: item.rtk_gnss_fail_point_two)

        # 最长连续假真1的时间超过5秒
        five_second_nano_time = 5 * 1000 * 1000 * 1000
        fail_point_one_repeat_groups_longest_time_over_5_seconds = 0
        for groups in fail_point_one_repeat_groups.values():
            rtk_source_tick_nano_time_list = [item.rtk.source_tick_nano_time for item in groups]
            min_rtk_source_nano_tick_time = min(rtk_source_tick_nano_time_list)
            max_rtk_source_nano_tick_time = max(rtk_source_tick_nano_time_list)
            diff_rtk_source_nano_tick_time = max_rtk_source_nano_tick_time - min_rtk_source_nano_tick_time
            if diff_rtk_source_nano_tick_time > five_second_nano_time:
                fail_point_one_repeat_groups_longest_time_over_5_seconds = diff_rtk_source_nano_tick_time

        # 最长连续假真2的时间超过10秒
        ten_second_nano_time = 10 * 1000 * 1000 * 1000
        fail_point_two_repeat_groups_longest_time_over_10_seconds = 0
        for groups in fail_point_two_repeat_groups.values():
            rtk_source_tick_nano_time_list = [item.rtk.source_tick_nano_time for item in groups]
            min_rtk_source_nano_tick_time = min(rtk_source_tick_nano_time_list)
            max_rtk_source_nano_tick_time = max(rtk_source_tick_nano_time_list)
            diff_rtk_source_nano_tick_time = max_rtk_source_nano_tick_time - min_rtk_source_nano_tick_time
            if diff_rtk_source_nano_tick_time > ten_second_nano_time:
                fail_point_two_repeat_groups_longest_time_over_10_seconds = diff_rtk_source_nano_tick_time

        success = fail_point_one_ratio < 0.005 and fail_point_two_ratio < 0.01 and fail_point_one_repeat_groups_longest_time_over_5_seconds == 0 and fail_point_two_repeat_groups_longest_time_over_10_seconds <= 0

        check_report = CheckReport()
        check_report.set_check_item("平面精度半径精度-FAIL")
        check_report.set_check_result("Pass" if success else "Failed")
        check_report.set_check_value(f"假真率1:{fail_point_one_ratio}<br>假真率2:{fail_point_two_ratio}<br>最长连续假真1的时间(ms):{fail_point_one_repeat_groups_longest_time_over_5_seconds}<br>最长连续假真2的时间(ms):{fail_point_two_repeat_groups_longest_time_over_10_seconds}")
        check_report.set_check_rules("最长连续假真1的时间小于5秒<br>且假真率1小于千分之五<br>且最长连续假真2的时间小于10秒<br>且假真率2小于百分之一")
        check_report.set_description("不通过需手动检查")
        return check_report

    def hit_range(self, target, boundary):
        return -boundary <= target <= boundary

    def build_repeat_frame_groups(self, alignment_data_list, get_property_func):
        # 重复帧: key是分组号，value是重复帧数组
        groups = {}
        group_index = 0
        current_group = []

        for item in alignment_data_list:
            is_fail_point = get_property_func(item)
            # 如果当前元素是目标值，加入当前组
            if is_fail_point:
                current_group.append(item)
            else:
                # 如果当前元素不是目标值，且当前组不为空，保存当前组
                if len(current_group) > 0:
                    groups[group_index] = current_group
                    group_index += 1
                    current_group = []

        # 处理数组末尾的连续元素
        if len(current_group) > 0:
            groups[group_index] = current_group
            group_index += 1

        return groups

    def find_gt_folder(self):
        # 获取所有子文件夹
        subfolders = [f for f in os.listdir(self._data_input_path)]
        # 过滤出符合条件的文件夹
        gt_folders = [f for f in subfolders if f.startswith('GT_') and f.endswith('_plaintext')]
        # 强制校验
        assert len(gt_folders) <= 1, f"Found {len(gt_folders)} GT folders, expected 0 or 1"
        return gt_folders[0] if len(gt_folders)==1 else None

    # 根据source_tick_time对齐gnss和rkt。如果最贴近的rtk有多个，随便取一个即可
    def alignment_data(self, valid_gnss_list, rtk_sorted_dict_operator):
        alignment_data_list = []
        unmatched_gnss_data_list = []

        # 最大误差100ms
        max_diff_nano_time = 100 * 1000 * 1000

        for gnss in valid_gnss_list:
            gnss_source_tick_nano_time = gnss.sourceTickTime * 1000 * 1000
            if gnss_source_tick_nano_time == 2817569 * 1000 * 1000:
                print(f"========> gnss source_tick_time: {gnss.sourceTickTime}")
            closest_rtk = rtk_sorted_dict_operator.find_closest(gnss_source_tick_nano_time)
            if gnss_source_tick_nano_time == 2817569 * 1000 * 1000:
                print(f"===========>rtk time {closest_rtk.source_tick_nano_time}")

            if closest_rtk is None:
                unmatched_gnss_data_list.append(gnss)
            else:
                abs_diff_nano_time = abs(closest_rtk.source_tick_nano_time - gnss_source_tick_nano_time)
                if abs_diff_nano_time >= max_diff_nano_time:
                    unmatched_gnss_data_list.append(gnss)
                else:
                    alignment_data_list.append(AlignmentGnssRtk(gnss, closest_rtk))

        return alignment_data_list, unmatched_gnss_data_list

    def parse_loc_gt_file(self, file_path):
        dict = SortedDictOperator()

        with (open(file_path, 'r') as lines):
            for line in lines:
                rtk = LocGtRtk(line)
                dict.add_item(rtk.source_tick_nano_time, rtk)

        return dict
    
    def parse_file(self, file_path):
        gnss_data = []

        with (open(file_path, 'r') as file):
            for line in file:
                data = json.loads(line.strip())
                value = data["value"]
                type = value["type"]

                if type == "gnss":
                    gnss = Gnss.model_validate(value)
                    gnss_data.append(gnss)

        return gnss_data

    # 构建表格样式函数
    def generate_table(self, signals_check_result_columns, signals_check_result_detail_columns, cur_date):
        # 生成表格
        cell_colors = []
        for _ in range(len(signals_check_result_columns)):
            cell_colors.append(['lavender'] * len(signals_check_result_columns["检查结果"]))  # 默认颜色

        # 不通过的整行标红
        for i in range(len(signals_check_result_columns["检查结果"])):
            if signals_check_result_columns["检查结果"][i] == "Failed":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'lightcoral'
            elif signals_check_result_columns["检查结果"][i] == "Warning":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'orange'
        # 绘制表格
        table_result = go.Table(
                header=dict(values=list(signals_check_result_columns.keys()), fill_color='paleturquoise',
                            align='center'),
                cells=dict(values=[signals_check_result_columns[k] for k in signals_check_result_columns],
                           fill_color=cell_colors,
                           align='left',
                           height=50) # 增加单元格行高
        )
        table_result_title = f"{self._checker_title}结果"

        table_result_detail = go.Table(
                header=dict(values=list(signals_check_result_detail_columns.keys()), fill_color='paleturquoise',
                            align='center'),
                cells=dict(values=[signals_check_result_detail_columns[k] for k in signals_check_result_detail_columns],
                           fill_color=cell_colors,
                           align='left',
                           height=200) # 增加单元格行高
        )
        table_result_detail_title = f"{self._checker_title}详情"
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=1,  # 三行
            cols=1,  # 一列
            specs=[[{'type': 'table'}]  # 概览-表格
                   ],
            subplot_titles=(table_result_title),  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        # 将表格添加到对应的子图位置
        fig.add_trace(table_result, row=1, col=1)

        # --- 更新整体布局 ---
        fig.update_layout(
            height=1000,  # 可能需要调整高度以容纳所有表格
            title_text=f"<b>{self._checker_title}结果汇总-{cur_date}</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50),  # 调整边距
            # 添加悬停模式配置
            hovermode='x unified',  # 统一显示同一x坐标的所有y值
            hoverdistance=100,      # 鼠标距离数据点的最大距离
            spikedistance=1000,     # 显示垂直参考线的距离
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_table_html_path)

    def generate_histogram(self, valid_check_report_list, cur_date):
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=len(valid_check_report_list),
            cols=1,
            specs=[[{'type': 'scatter'}] for _ in valid_check_report_list],
            subplot_titles=[item.check_item for item in valid_check_report_list],  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        for i, item in enumerate(valid_check_report_list):
            abs_max = max(item.data_list)
            abs_min = min(item.data_list)
            abs_range = abs_max - abs_min
            real_nbinsx = int(abs_range + 1) * 100

            fig.add_trace(
                go.Histogram(
                    x=item.data_list,
                    name=item.check_item,
                    nbinsx=real_nbinsx,  # 设置直方图的bin数量
                    marker_color='blue',
                    marker_line_color='black',  # 边缘线颜色
                    marker_line_width=2,  # 边缘线宽度
                    opacity=0.7
                ),
                row=i + 1, col=1
            )

            # 更新横轴标题
            fig.update_xaxes(
                title_text="数值",
                row=i + 1, col=1
            )

            # 更新纵轴标题
            fig.update_yaxes(
                title_text="数量",
                row=i + 1, col=1
            )

        # --- 更新整体布局 ---
        fig.update_layout(
            height=300 * len(valid_check_report_list),  # 根据子图数量动态调整高度
            title_text=f"<b>{self._checker_title}结果汇总-{cur_date}</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50),  # 调整边距
            # 添加悬停模式配置
            hovermode='x unified',  # 统一显示同一x坐标的所有y值
            hoverdistance=100,  # 鼠标距离数据点的最大距离
            spikedistance=1000,  # 显示垂直参考线的距离
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_histogram_html_path)

    def generate_scatter(self, valid_check_report_list, cur_date):
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=len(valid_check_report_list),
            cols=1,
            specs=[[{'type': 'scatter'}] for _ in valid_check_report_list],
            subplot_titles=[item.check_item for item in valid_check_report_list],  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        for i, item in enumerate(valid_check_report_list):
            fig.add_trace(
                go.Scatter(
                    x=item.source_tick_time_list,
                    y=item.data_list,
                    name=item.check_item,
                    line=dict(color='blue', width=2),
                    mode='lines',
                    showlegend=True,  # 是否在图例中显示
                    legendgroup="default",  # 图例分组
                    hovertemplate="source_tick_time: %{x}<br>值: %{y:.2f}<extra></extra>"  # 鼠标悬停时显示的提示信息
                ),
                row=i + 1, col=1
            )

            # 更新横轴标题
            fig.update_xaxes(
                title_text="source_tick_time",
                row=i + 1, col=1
            )

            # 更新纵轴标题
            fig.update_yaxes(
                title_text="数值",
                row=i + 1, col=1
            )

        # --- 更新整体布局 ---
        fig.update_layout(
            height=300 * len(valid_check_report_list),  # 根据子图数量动态调整高度
            title_text=f"<b>{self._checker_title}结果汇总-{cur_date}</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50),  # 调整边距
            # 添加悬停模式配置
            hovermode='x unified',  # 统一显示同一x坐标的所有y值
            hoverdistance=100,  # 鼠标距离数据点的最大距离
            spikedistance=1000,  # 显示垂直参考线的距离
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_scatter_html_path)

    def deinit(self):
        """销毁检查实例"""
        pass

    def get_checker_name(self)-> str:
        """获取检查项名称"""
        return "C11GnssAccuracy"

    def prepare_dirs(self):
        if not os.path.exists(self._data_input_path):
            print(f"File not found: {self._data_input_path}")
            return False, "File not found"

        if self._loc_gt_txt_path is None or not os.path.exists(self._loc_gt_txt_path):
            return False, "loc_gt.txt not found"

        if not os.path.exists(self._data_output_path):
            print(f"output folder not found, try create: {self._data_output_path}")
            os.makedirs(self._data_output_path)
            if not os.path.exists(self._data_output_path):
                return False, "output folder not found, try create failed"
            else:
                print(f"output folder created: {self._data_output_path}")
                return True, ""
        else:
            print(f"output folder already exists: {self._data_output_path}")
        return True, ""

    def setup_logger(self, level=logging.INFO):
        """设置日志器，同时输出到控制台和文件"""
        date_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        log_file = os.path.join(self._data_output_path, f"{self._checker_name}_{date_time_str}.log")

        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 文件 handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)

        # 控制台 handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # 创建 logger
        logger = logging.getLogger(self._checker_name)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

# Example usage (for testing this file directly)
if __name__ == "__main__":
    # 添加可选参数（带默认值）
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", "-i", help="数据根路径")
    args = parser.parse_args()

    checker = C11GnssAccuracy()
    print(f"Checker Name: {checker.get_checker_name()}")
    checker.init(args.input)
    checker.check()
    checker.deinit()
