from pydantic import BaseModel

import gnss_accuracy_utils

"""
{
        "type": "gnss",
        "currentTickTime": 176319564,
        "sourType": 0,
        "mode": -1,
        "status": 65,
        "isEncrypted": 1,
        "lon": 116114079,
        "lat": 30179158,
        "z": 0,
        "isNS": 78,
        "isEW": 69,
        "speed": 9.564000129699707,
        "course": 323.79998779296875,
        "alt": 50.400001525878906,
        "num": 9,
        "hdop": -1,
        "vdop": -1,
        "pdop": -1,
        "year": 2025,
        "month": 4,
        "day": 9,
        "hour": 12,
        "minute": 36,
        "second": 50,
        "mSecond": 0,
        "accuracy": 1.9499999284744263,
        "spdAccuracy": -1,
        "courseAccuracy": -1,
        "verticalAccuracy": -1,
        "subSourType": -999,
        "dataType": 5,
        "sourceTickTime": "176319564",
        "localTickTime": "0"
    }
"""
class Gnss(BaseModel):
    type:str
    sourceTickTime:int
    lon:int
    lat:int
    alt:float
    speed:float
    course:float
    accuracy:float
    status:int

# 689211000000,689211000000,689211000000,4,39.9657543413,116.4344970516,0.022777433349699432,0.004406474100684129,309.397710155461,33.292891,0.01709000000011582
class LocGtRtk:
    """
    定义位置模型类，用于存储和解析位置信息。
    Attributes:
        source_tick_time (int): 时间戳（单位：纳秒）
        utctime (int): UTC时间（单位：纳秒）
        ggatime (int): GGA时间（单位：纳秒）
        status (int): 状态码
        latitude (float): 纬度
        longitude (float): 经度
        accuracy (float): 准确度
        speed (float): 速度（单位：m/s）,已转为km/h
        course (float): 航向角
        altitude (float): 海拔高度
        yaw_rate (float): 偏航率
    """

    def __init__(self, line: str):
        """
        解析位置信息行。
        :param line: 位置信息行
        """
        fields = line.strip().split(",")
        self.source_tick_nano_time = int(fields[0])
        self.utctime = int(fields[1])
        self.ggatime = int(fields[2])
        self.status = int(fields[3])
        self.latitude = float(fields[4])
        self.longitude = float(fields[5])
        self.accuracy = float(fields[6])
        self.speed = float(fields[7]) * 3.6
        self.course = float(fields[8])
        self.altitude = float(fields[9])
        self.yaw_rate = float(fields[10])

        gcj_lon_lat = gnss_accuracy_utils.wgs84_to_gcj02(self.longitude, self.latitude)
        self.gcj_lon = gcj_lon_lat[0]
        self.gcj_lat = gcj_lon_lat[1]


class AlignmentGnssRtk:
    """
    对齐GNSS和RTK
    """
    def __init__(self, gnss:Gnss, rtk:LocGtRtk):
        self.gnss = gnss
        self.rtk = rtk
        self.rtk_gnss_lon_lat_diff = None
        self.rtk_gnss_alt_diff = None
        self.rtk_gnss_speed_diff = None
        self.rtk_gnss_course_diff = None
        self.rtk_gnss_accuracy_radius_diff = None
        self.rtk_gnss_fail_point_one:bool = False
        self.rtk_gnss_fail_point_two:bool = False

    # 平面位置误差 获取经纬度(单位：m)
    def calc_rkt_gnss_lon_lat_diff(self):
        source_tick_nano_time = self.rtk.source_tick_nano_time

        rtk_lat = self.rtk.gcj_lat
        rtk_lon = self.rtk.gcj_lon
        
        if source_tick_nano_time ==2817578000000:
            print(f"rtk_lat: {rtk_lat}, rtk_lon: {rtk_lon}")

        source_tick_time =  self.gnss.sourceTickTime
        gnss_lat = self.gnss.lat / 1000000
        gnss_lon = self.gnss.lon / 1000000
        if source_tick_time == 2817569:
            print(f"gnss_lat: {gnss_lat}, gnss_lon: {gnss_lon}")



        self.rtk_gnss_lon_lat_diff = gnss_accuracy_utils.calc_distance_meters(rtk_lat, rtk_lon, gnss_lat, gnss_lon)

    # 高程误差
    def calc_rkt_gnss_alt_diff(self):
        rtk_alt = self.rtk.altitude
        gnss_alt = self.gnss.alt
        self.rtk_gnss_alt_diff = rtk_alt - gnss_alt

    # 速度误差
    def calc_rkt_gnss_speed_diff(self):
        rtk_speed = self.rtk.speed
        gnss_speed = self.gnss.speed
        self.rtk_gnss_speed_diff = rtk_speed - gnss_speed

    # 航向误差
    def calc_rkt_gnss_course_diff(self):
        rtk_course = 360 - self.rtk.course
        gnss_course = 360 - self.gnss.course

        diff_course = (rtk_course - gnss_course) % 360
        # 调整到 [-180, 180] 范围内
        if diff_course > 180:
            diff_course -= 360

        self.rtk_gnss_course_diff = diff_course

    # 平均精度半径误差（单位：m)
    def calc_rkt_gnss_accuracy_radius_diff(self, rtk_gnss_lon_lat_diff):
        self.rtk_gnss_accuracy_radius_diff = rtk_gnss_lon_lat_diff - self.gnss.accuracy

    # 假真点数量1=符合条件((平面位置误差大于10.0米或航向误差大于3deg) && GNSS的平面精度半径小于0.1米)的点数
    def calc_rtk_gnss_fail_point_one(self, rtk_gnss_lon_lat_diff, rtk_gnss_course_diff, gnss_accuracy):
        if (rtk_gnss_lon_lat_diff > 10.0 or rtk_gnss_course_diff > 3) and 0 <= gnss_accuracy < 0.1:
            self.rtk_gnss_fail_point_one = True

    # 假真点数量2=符合条件((平面位置误差大于3.5米或航向误差大于3deg) && GNSS的平面精度半径小于0.1米)的点数
    def calc_rtk_gnss_fail_point_two(self, rtk_gnss_lon_lat_diff, rtk_gnss_course_diff, gnss_accuracy):
        if (rtk_gnss_lon_lat_diff > 3.5 or rtk_gnss_course_diff > 3) and 0 <= gnss_accuracy < 0.1:
            self.rtk_gnss_fail_point_two = True

class CheckReport:
    def __init__(self):
        self.check_item = None
        self.check_result = None
        self.check_value = None
        self.check_rules = None
        self.description = None
        self.source_tick_time_list = None
        self.data_list = None

    def set_check_item(self, check_item):
        self.check_item = check_item

    def set_check_result(self, check_result):
        self.check_result = check_result

    def set_check_value(self, check_value):
        self.check_value = check_value

    def set_check_rules(self, check_rules):
        self.check_rules = check_rules

    def set_description(self, description):
        self.description = description

    def set_source_tick_time_list(self, source_tick_time_list):
        self.source_tick_time_list = source_tick_time_list

    def set_data_list(self, data_list):
        self.data_list = data_list