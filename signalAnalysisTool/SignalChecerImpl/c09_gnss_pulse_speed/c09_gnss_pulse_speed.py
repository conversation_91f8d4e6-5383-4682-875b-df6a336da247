import argparse
from datetime import datetime
import json
import logging
import os
from scipy import signal
import sys
import pandas as pd

import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))  # Up two levels to signalAnalysisTool
sys.path.insert(0, parent_dir)
try:
    from SignalCheckerInterface import SignalCheckerInterface
except ImportError:
    print("Could not import SignalCheckerInterface directly. Ensure signalAnalysisTool is in PYTHONPATH.")


class C09GnssPulseSpeed(SignalCheckerInterface):
    _checker_title = "GNSS速度和轮速不同步"
    _checker_name = "C09GnssPulseSpeed"
    _data_input_path = ""
    _data_data_txt_path = ""
    _data_output_path = ""
    _data_output_check_result_json_path = ""
    _data_output_check_result_detail_json_path = ""
    _data_output_check_result_table_html_path = ""
    _data_output_check_result_scatter_html_path = ""
    _data_output_check_result_hist_html_path = ""

    def init(self, input_path: str):
        self._data_input_path = input_path
        folder_name = os.path.basename(os.path.normpath(self._data_input_path))
        self._data_data_txt_path = os.path.join(self._data_input_path,
                                                f"AutoSdkDemo/online/vlane/{folder_name}/data.txt")
        self._data_output_path = os.path.join(self._data_input_path, "CheckResult/" + self.get_checker_name())
        self._data_output_check_result_json_path = os.path.join(self._data_output_path, "check_result.json")
        self._data_output_check_result_detail_json_path = os.path.join(self._data_output_path,
                                                                       "check_result_detail.json")
        self._data_output_check_result_table_html_path = os.path.join(self._data_output_path, "check_result_table.html")
        self._data_output_check_result_scatter_html_path = os.path.join(self._data_output_path,
                                                                        "check_result_scatter.html")
        self._data_output_check_result_hist_html_path = os.path.join(self._data_output_path, "check_result_hist.html")
        self._logger = self.setup_logger()
        self._logger.info("init")

    def check(self):
        # 进行信号检查
        self._logger.info("check")
        # 定义 GNSS速度和轮速不同步 相关性函数阈值
        signals_config = {
            "GNSS速度和轮速不同步相关性函数": {"max_lag": 0.2, "description": "不通过需手动排查"}
        }

        # 取值阈值(如下为 速度>20且持续5分钟)
        self.speed_threshold = 20
        self.time_gap_threshold = 5 * 60 * 1000

        # 定义检查结果头
        cur_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        signals_check_result = {
            "checker": "gnssPulseSpeedChecker",
            "title": f"{self._checker_title}",
            "datatxt": self._data_data_txt_path,
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResult"
        }
        signals_check_result_detail = {
            "checker": "gnssPulseSpeedChecker",
            "title": f"{self._checker_title}详情",
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResultDetail"
        }

        signals_check_result_items = []
        signals_check_result_detail_items = []
        signals_check_result_columns = {"检查项": [], "统计数值": [], "检查规则": [], "检查结果": [], "描述": []}
        signals_check_result_detail_columns = {"检查项": [], "检查详情": []}

        # 读取并解析data.txt文件
        gnss_data, pulse_data = self.parse_file(self._data_data_txt_path)

        # 获取gnss, pulse 数据
        gnss_last_list = self.extract_continuous_speed_gt_20(gnss_data)
        pulse_result_list = self.extract_continuous_speed_gt_20(pulse_data)

        pulse_last_list = []
        # 根据gnss范围获取对应的pulse范围
        for gnss_result in gnss_last_list:
            pulse_result = self.get_pulse_list_form_gnss(gnss_result, pulse_result_list)
            if pulse_result:
                pulse_last_list.append(pulse_result)

        if len(gnss_last_list) != len(pulse_last_list) or not gnss_last_list:
            # 如果gnss与pulse所获集合不同, 那么则使用最出的数据
            gnss_last_list = [gnss_data]
            pulse_last_list = [pulse_data]

        lag_times_list, correlation_list, max_lag_list, check_data_list, check_result_detail_list = [], [], [], [], []
        # 检查并组装结果
        for index, value in enumerate(gnss_last_list):
            gnss_data = value
            pulse_data = pulse_last_list[index]
            # 计算 GNSS速度和轮速不同步 相关函数
            for signal_name, config in signals_config.items():
                lag_times, correlation, max_lag, check_result = self.calculate_lag(gnss_data, pulse_data,
                                                                                   config["max_lag"])

                lag_times_list.append(lag_times)
                correlation_list.append(correlation)
                max_lag_list.append(max_lag)

                check_data_result = {
                    "checkDataTime": f"{gnss_data[0]["time"]} - {gnss_data[-1]["time"]}",
                    "checkResultStatistic": f"最大延迟{max_lag}s",
                    "checkResult": check_result
                }
                check_data_list.append(check_data_result)

                current_check_result = {
                    "checkItem": f"{signal_name}",
                    "checkList": check_data_list,
                    # "checkResult": check_result,
                    # "checkResultStatistic": f"最大延迟{max_lag}s",
                    "checkRules": f"最大延迟<={config["max_lag"]}s",
                    "description": "不通过需手动排查"
                }

                signals_check_result_items.append(current_check_result)
                signals_check_result_columns["检查项"].append(f"{signal_name}<br>{gnss_data[0]["time"]} ~ {gnss_data[-1]["time"]}")
                signals_check_result_columns["统计数值"].append(f"最大延迟{max_lag}s")
                signals_check_result_columns["检查规则"].append(f"最大延迟<{config["max_lag"]}s")
                signals_check_result_columns["检查结果"].append(check_result)
                signals_check_result_columns["描述"].append("不通过需手动排查")

                check_result_detail = {
                    "time": f"{gnss_data[0]["time"]} - {gnss_data[-1]["time"]}",
                    "max_lag": max_lag
                }
                check_result_detail_list.append(check_result_detail)

                signals_check_result_detail_items.append(check_result_detail)
                signals_check_result_detail_columns["检查项"].append(f"{signal_name} {gnss_data[0]["time"]} ~ {gnss_data[-1]["time"]}")
                signals_check_result_detail_columns["检查详情"].append(f"{max_lag}")

            # 组装完整检查结果
            signals_check_result["checkResult"] = current_check_result
            signals_check_result_detail["checkResult"] = check_result_detail_list
            # 保存检查结果
            print(f"check_result: {signals_check_result}")
            self._logger.info(f"check_result: {signals_check_result}")
            with open(self._data_output_check_result_json_path, 'w', encoding='utf-8') as file:
                json.dump(signals_check_result, file, ensure_ascii=False, indent=4)
            with open(self._data_output_check_result_detail_json_path, 'w') as file:
                json.dump(signals_check_result_detail, file, ensure_ascii=False, indent=4)

            # 生成表格
            self.generate_table(signals_check_result_columns, signals_check_result_detail_columns)

        #  生成直方图 和 生成曲线图
        self.generate_histogram(lag_times_list, correlation_list, max_lag_list, gnss_last_list)

        # self.generate_histogram(lag_times, correlation, max_lag)
        # self.generate_scatter(lag_times, correlation, max_lag)

        return "success"

    def deinit(self):
        _data_input_path = ""
        _data_data_txt_path = ""
        _data_output_path = ""
        _data_output_check_result_json_path = ""
        _data_output_check_result_detail_json_path = ""
        _data_output_check_result_table_html_path = ""

    def get_checker_name(self):
        return "C09GnssPulseSpeed"

    def prepare_dirs(self):
        if not os.path.exists(self._data_input_path):
            print(f"File not found: {self._data_input_path}")
            return False, "File not found"
        if not os.path.exists(self._data_output_path):
            print(f"output folder not found, try create: {self._data_output_path}")
            os.makedirs(self._data_output_path)
            if not os.path.exists(self._data_output_path):
                return False, "output folder not found, try create failed"
            else:
                print(f"output folder created: {self._data_output_path}")
                return True, ""
        else:
            print(f"output folder already exists: {self._data_output_path}")
        return True, ""

    def setup_logger(self, level=logging.INFO):
        # 创建输出目录
        prepare_result, prepare_result_msg = self.prepare_dirs()
        if not prepare_result:
            return prepare_result_msg
        """设置日志器，同时输出到控制台和文件"""
        date_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        log_file = os.path.join(self._data_output_path, f"{self._checker_name}_{date_time_str}.log")

        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 文件 handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)

        # 控制台 handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # 创建 logger
        logger = logging.getLogger(self._checker_name)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def parse_file(self, file_path):
        gnss_data = []
        pulse_data = []
        with open(file_path, 'r') as file:
            for line in file:
                try:
                    data_json = json.loads(line.strip())
                    signal_type = data_json["value"]["type"]
                    

                    if signal_type == "gnss":
                        time = int(data_json["value"]["sourceTickTime"])
                        if time < 1989558 or time > 2500000:
                            continue
                        speed = data_json["value"]["speed"]
                        gnss_data.append({"time": time, "speed": speed})
                    elif signal_type == "pulse":
                        time = int(data_json["value"]["sourceTickTime"])
                        if time < 1989558 or time > 2500000:
                            continue
                        speed = data_json["value"]["speed"]
                        pulse_data.append({"time": time, "speed": speed})

                except Exception as e:
                    print(f"Error parsing line: {line}, error: {e}")
                    self._logger.error(f"Error parsing line: {line}, error: {e}")
                    continue
        print(f"gnss_data length: {len(gnss_data)}, pulse_data length: {len(pulse_data)}")

        return gnss_data, pulse_data

    def calculate_lag(self, gnss_data, pulse_data, threshold_max_lag):
        df_gnss = pd.DataFrame(gnss_data)
        df_pulse = pd.DataFrame(pulse_data)

        print(f"cross_correlation_function, gnss length : {len(df_gnss)} , pulse length : {len(df_pulse)}")
        self._logger.info(f"cross_correlation_function, gnss length : {len(df_gnss)} , pulse length : {len(df_pulse)}")

        # 时间戳转换为秒（相对于起始时间）
        base_time = min(df_gnss['time'].min(), df_pulse['time'].min())
        df_gnss['t'] = (df_gnss['time'] - base_time) / 1000.0  # 转换为秒
        df_pulse['t'] = (df_pulse['time'] - base_time) / 1000.0

        # 创建统一时间轴（10Hz采样率）
        t_start = max(df_gnss['t'].min(), df_pulse['t'].min())
        t_end = min(df_gnss['t'].max(), df_pulse['t'].max())
        t_10hz = np.arange(t_start, t_end, 0.1)  # 10Hz时间轴

        # 信号插值
        gnss_interp = np.interp(t_10hz, df_gnss['t'], df_gnss['speed'])
        pulse_interp = np.interp(t_10hz, df_pulse['t'], df_pulse['speed'])

        # 去均值处理
        gnss_centered = gnss_interp - np.mean(gnss_interp)
        pulse_centered = pulse_interp - np.mean(pulse_interp)

        # 计算交叉相关
        correlation = signal.correlate(gnss_centered, pulse_centered, mode='full')
        lags = signal.correlation_lags(len(gnss_centered), len(pulse_centered), mode='full')
        lag_times = lags * 0.1  # 转换为秒（10Hz间隔）

        # 找到最大相关值
        max_idx = np.argmax(correlation)
        max_lag = lag_times[max_idx]
        print(f"最大延迟 max_lag = {max_lag} s")
        self._logger.info(f"最大延迟 max_lag = {max_lag} s")

        if max_lag <= threshold_max_lag:
            check_result = "Pass"
        else:
            check_result = "Fail"

        return lag_times, correlation, max_lag, check_result

    # 绘制相关性函数的直方图(多个直方图)
    def generate_histogram(self, lag_times_list, correlation_list, max_lag_list, gnss_last_list):
        fig = make_subplots(rows=len(lag_times_list), cols=1, shared_xaxes=False, vertical_spacing=0.12)

        for index, lag_times in enumerate(lag_times_list):
            correlation = correlation_list[index]
            max_lag = max_lag_list[index]

            # 添加子图
            fig.add_trace(
                go.Scatter(x=lag_times, y=correlation, mode='lines', name=f'Correlation {index + 1}'),
                row=index + 1, col=1
            )

            # 添加注释文本
            fig.add_annotation(
                x=1, y=1,
                text=f"最大延迟 max_lag = {max_lag:.2f}s",
                showarrow=False,
                font=dict(size=12, color='black'),
                xref='x domain', yref='y domain',
                xanchor='right', yanchor='top',
                row=index + 1, col=1
            )

            # 添加子图标题
            fig.add_annotation(
                x=0.5,  # 标题居中于x轴
                y=1.15,  # 在子图顶部之上
                text=f"对应time范围: {gnss_last_list[index][0]['time']} - {gnss_last_list[index][-1]['time']}",
                showarrow=False,
                font=dict(size=14, color="blue"),
                xref=f"x domain", yref=f"y domain",
                xanchor='center', yanchor='top',
                row=index + 1, col=1
            )

            # 单独设置每个子图的x轴标题
            fig.update_xaxes(title_text="Lag (s)", row=index + 1, col=1)
            fig.update_yaxes(title_text="Correlation", row=index + 1, col=1)

            # 更新整体布局
        fig.update_layout(
            title_text="Cross-correlation Functions",
            height=350 * len(lag_times_list),  # 根据子图数量调整高度
            showlegend=False
        )

        # 一次性写入 HTML
        fig.write_html(self._data_output_check_result_hist_html_path, include_plotlyjs='cdn')

    # 绘制相关性函数的直方图(单个直方图, 不使用)
    # def generate_histogram(self, lag_times, correlation, max_lag):
        # for index, value in enumerate(lag_times_list):
        #     lag_times = value
        #     correlation = correlation_list[index]
        #     max_lag = max_lag_list[index]
        #
        #     fig = go.Figure()
        #     fig.add_trace(go.Scatter(x=lag_times, y=correlation, mode='lines', name='Correlation Curve'))
        #
        #     text = f"最大延迟 max_lag = {max_lag} s"
        #     fig.add_annotation(
        #         x=1, y=1,
        #         text=text,
        #         showarrow=False,
        #         font=dict(size=12, color='black'),
        #         xref='paper', yref='paper',
        #         xanchor='right', yanchor='top')
        #
        #     fig.update_layout(title="Cross-correlation Function", xaxis=dict(title='Lag (s)'), yaxis=dict(title='Correlation'))
        #     fig.write_html(self._data_output_check_result_hist_html_path)

    # 绘制曲线图
    def generate_scatter(self, lag_times, correlation, max_lag):
        fig = go.Figure()
        fig.add_trace(go.Histogram(x=correlation, nbinsx=50, name='Correlation Histogram', marker_color='lightblue',
                                   marker_line=dict(color='gray', width=1)))

        text = f"最大延迟 max_lag = {max_lag} s"
        fig.add_annotation(
            x=1, y=1,
            text=text,
            showarrow=False,
            font=dict(size=12, color='black'),
            xref='paper', yref='paper',
            xanchor='right', yanchor='top')

        fig.update_layout(title="Cross-correlation Histogram", xaxis=dict(title='Correlation'),
                          yaxis=dict(title='Frequency'))
        fig.write_html(self._data_output_check_result_scatter_html_path)

    # 构建表格样式函数
    def generate_table(self, signals_check_result_columns, signals_check_result_detail_columns):
        # 生成表格
        cell_colors = []
        for _ in range(len(signals_check_result_columns)):
            cell_colors.append(['lavender'] * len(signals_check_result_columns["检查结果"]))  # 默认颜色

        # 对于延迟大于 0.2s 的行，整行标红
        for i in range(len(signals_check_result_columns["检查结果"])):
            if signals_check_result_columns["检查结果"][i] != "Pass":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'lightcoral'
        # 绘制表格
        table_result = go.Table(
            header=dict(values=list(signals_check_result_columns.keys()), fill_color='paleturquoise',
                        align='center'),
            cells=dict(values=[signals_check_result_columns[k] for k in signals_check_result_columns],
                       fill_color=cell_colors, align='center')
        )
        table_result_title = f"{self._checker_title}检查结果"

        table_result_detail = go.Table(
            header=dict(values=list(signals_check_result_detail_columns.keys()), fill_color='paleturquoise',
                        align='center'),
            cells=dict(values=[signals_check_result_detail_columns[k] for k in signals_check_result_detail_columns],
                       fill_color=cell_colors, align='center')
        )
        table_result_detail_title = f"{self._checker_title}检查详情"
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=2,  # 三行
            cols=1,  # 一列
            specs=[[{'type': 'table'}],  # 第一行是一个表格
                   [{'type': 'table'}]],  # 第二行是一个表格
            subplot_titles=(table_result_title, table_result_detail_title),  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        # 将表格添加到对应的子图位置
        fig.add_trace(table_result, row=1, col=1)
        fig.add_trace(table_result_detail, row=2, col=1)

        # --- 更新整体布局 ---
        fig.update_layout(
            height=700,  # 可能需要调整高度以容纳所有表格
            title_text=f"<b>{self._checker_title}检查结果汇总</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50)  # 调整边距
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_table_html_path)

    # 获取符合条件的gnss和pulse的范围
    def extract_continuous_speed_gt_20(self, data_list):
        result = []
        current_segment = []

        for entry in data_list:
            try:
                time = int(entry["time"])
                speed = float(entry["speed"])
            except (ValueError, TypeError):
                continue

            if speed > self.speed_threshold:
                current_segment.append({"time": time, "speed": speed})
            else:
                if current_segment and (current_segment[-1]["time"] - current_segment[0]["time"] >= self.time_gap_threshold):
                    result.append(current_segment)
                current_segment = []

        return result

    # 根据gnss列表获取对应pulse列表
    def get_pulse_list_form_gnss(self, gnss_list, pulse_preliminary_list):
        pulse_last = []

        gnss_start_time = gnss_list[0]["time"]
        gnss_end_time = gnss_list[-1]["time"]
        print(f"gnss_start_time: {gnss_start_time}, gnss_end_time: {gnss_end_time}")
        for pulse_result in pulse_preliminary_list:
            for entry in pulse_result:
                try:
                    time = int(entry["time"])
                    speed = float(entry["speed"])
                except (ValueError, TypeError):
                    continue

                if int(time) > int(gnss_start_time) and int(time) < int(gnss_end_time):
                    pulse_last.append({"time": time, "speed": speed})

        return pulse_last


if __name__ == "__main__":
    # 添加可选参数（带默认值）
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", "-i", help="数据根路径")
    args = parser.parse_args()

    checker = C09GnssPulseSpeed()
    print(f"Checker Name: {checker.get_checker_name()}")
    checker.init(args.input)
    checker.check()
    checker.deinit()

