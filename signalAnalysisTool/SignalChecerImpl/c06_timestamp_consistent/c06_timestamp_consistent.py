import decimal
import logging
import sys
import os
import argparse
import json
from datetime import datetime
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from decimal import Decimal
import bisect
import csv

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir)) # Up two levels to signalAnalysisTool
sys.path.insert(0, parent_dir)

try:
    from SignalCheckerInterface import SignalCheckerInterface
except ImportError:
    print("Could not import SignalCheckerInterface directly. Ensure signalAnalysisTool is in PYTHONPATH.")

class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, Decimal):
            return str(o)  # 保留原始精度
        return super().default(o)


class C06TimestampConsistent(SignalCheckerInterface):
    _checker_title= "时间戳匹配检查"
    _checker_name = ""
    _data_input_path = ""
    _data_data_txt_path = ""
    _data_output_path = ""
    _data_output_check_result_json_path = ""
    _data_output_check_result_detail_json_path = ""
    _data_output_check_result_table_html_path = ""

    def init(self, input_path: str):
        _checker_name= self.get_checker_name()
        """初始化传入数据路径"""
        self._data_input_path = input_path
        folder_name = os.path.basename(os.path.normpath(self._data_input_path))
        self._data_data_txt_path = os.path.join(self._data_input_path, f"AutoSdkDemo/online/vlane/{folder_name}/data.txt")
        self._data_output_path = os.path.join(self._data_input_path, "CheckResult/" + self.get_checker_name())
        self._data_output_check_result_json_path = os.path.join(self._data_output_path, "check_result.json")
        self._data_output_check_result_detail_json_path = os.path.join(self._data_output_path, "check_result_detail.json")
        self._data_output_check_result_table_html_path = os.path.join(self._data_output_path, "check_result_table.html")
        
        self.prepare_dirs()
        self._logger = self.setup_logger()
        self._logger.info("init")
        pass

    def check(self) -> str:
        """进行信号检查"""
        prepare_result, prepare_result_msg = self.prepare_dirs()
        if not prepare_result:
            return prepare_result_msg

        #sourceTicktime开始时间大于100ms
        sourceTickTime_start_thresh = 100
        
        #GNSS 前后500ms 有图像数据
        gnss_img_time_thresh = 667
        
        #GNSS前后100ms要有IMU数据/车速数据
        gnss_imu_time_thresh = 100
        
        #图像前后100ms要有IMU数据/车速数据
        image_imu_time_thresh = 100

        signals_config = {
            "acce3d": {f"field": "sourceTickTime","description": "sourceTicktime开始时间大于{sourceTickTime_start_thresh}s\n"},
            "gyro": {"field": "sourceTickTime","description": "sourceTicktime开始时间大于{sourceTickTime_start_thresh}ms\n"},
            "pulse": {"field": "sourceTickTime","description": "sourceTicktime开始时间大于{sourceTickTime_start_thresh}ms\n" },
            "gnss": {"field": "sourceTickTime","description":\
                     '''sourceTicktime开始时间大于{sourceTickTime_start_thresh}ms\n
                     GNSS前后{gnss_img_time_thresh}ms有图像数据\n
                     GNSS前后{gnss_imu_time_thresh}ms要有IMU数据/车速数''' },
            "image": {"field": "timestampExposure","description":
                '''sourceTicktime开始时间大于{sourceTickTime_start_thresh}ms\n
                图像前后100ms要有IMU数据/车速数据'''},
        }

        # 定义检查结果头
        cur_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        signals_check_result= {
            "checker": self.get_checker_name(),
            "title": f"{self._checker_title}",
            "datatxt": self._data_data_txt_path,
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResult"
        }
        signals_check_result_detail = {
            "checker": self.get_checker_name(),
            "title": f"{self._checker_title}详情",
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResultDetail"
        }
        signals_check_result_items = []
        signals_check_result_detail_items = []
        signals_check_result_columns = {"检查项":[], "统计数值":[],  "检查规则":[], "检查结果":[],"描述":[]}
        signals_check_result_detail_columns = {"检查项":[], "检查详情":[]}

        # 解析文件
        acce3d_data, gyro_data, pulse_data, gnss_data, image_data = self.parse_file(self._data_data_txt_path)

        for signal_name, config in signals_config.items():
            signal_datas = eval(f"{signal_name}_data")
            time_field = config["field"]
            check_result = "Pass"
            check_result_sourceTickTime, check_info_sourceTickTime = self.sourceTickTime_check(signal_name,signal_datas,sourceTickTime_start_thresh,time_field)
            check_result = check_result if check_result_sourceTickTime == "Pass" else "Fail"
            fail_info = ""
            fail_description = ""    
            frame_cnt = len(signal_datas)
            fail_frame_cnt = 0
            unique_fail_frames = []
            if check_result == "Fail":
                fail_frame_cnt = 1
                fail_description = "sourceTickTime开始时间不超过100ms<br>"
            ##gnss多检测几个
            if signal_name == "gnss":
                check_result_gnss,fail_frame_cnt,fail_info,unique_fail_frames = self.gnss_check(gnss_data,image_data,gyro_data,acce3d_data,pulse_data,gnss_img_time_thresh,gnss_imu_time_thresh)
                check_result = "Pass" if check_result_gnss else "Fail"
                
            # 图像前后100ms要有IMU数据/车速数据
            elif signal_name == "image":
                check_result_image,fail_frame_cnt,fail_info,unique_fail_frames = self.image_check(image_data,gyro_data,acce3d_data,pulse_data,image_imu_time_thresh)
                check_result = "Pass" if check_result_image else "Fail"
                if  check_result == "Fail":
                    fail_description += fail_info
            
            fail_ratio = (fail_frame_cnt/frame_cnt) if frame_cnt > 0 else 0    
            current_check_result = {
                "checkItem": f"{signal_name}时间戳匹配检查",
                "checkResult": check_result,
                "checkResultStatistic": fail_ratio,
                "checkRules": config["description"],
                "description": fail_description
            }
            signals_check_result_items.append(current_check_result)

            signals_check_result_columns["检查项"].append(f"{signal_name}时间戳匹配检查")
            signals_check_result_columns["统计数值"].append(f"总帧数:{frame_cnt}\n 异常帧数:{fail_frame_cnt}\n 异常帧率:{fail_ratio*100:.3f}%")
            signals_check_result_columns["检查规则"].append(config["description"])
            signals_check_result_columns["检查结果"].append(check_result)
            signals_check_result_columns["描述"].append(fail_description)

            check_result_detail = {
                "checkItem": f"{signal_name}时间戳匹配检查",
                "total_frames": frame_cnt,
                "fail_frames": unique_fail_frames,
                "fail_frame_rate": f"{fail_ratio*100:.3f}%",
            }
            detail_list = [{time_field: item[time_field]} for item in unique_fail_frames]
            detail_list_str = json.dumps(detail_list, ensure_ascii=False, indent=2)
            signals_check_result_detail_items.append(check_result_detail)

            signals_check_result_detail_columns["检查项"].append(f"{signal_name}时间戳匹配检查")
            signals_check_result_detail_columns["检查详情"].append(detail_list_str)
            
            if check_result == "Fail":
                csv_file = os.path.join(self._data_output_path, f"{signal_name}_checkfailed.csv")
                with open(csv_file, mode='w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow([time_field])
                    for item in unique_fail_frames:
                        writer.writerow([
                            item.get(time_field, ""),  # 如果字段不存在，默认为空字符串
                        ])

        
        # 组装完整检查结果
        signals_check_result["checkResult"] = signals_check_result_items
        signals_check_result_detail["checkResultDetail"] = signals_check_result_detail_items

        # 保存结果
        with open(self._data_output_check_result_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result, f, ensure_ascii=False, indent=4, cls=DecimalEncoder)

        with open(self._data_output_check_result_detail_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result_detail, f, ensure_ascii=False, indent=4, cls=DecimalEncoder)

        # 生成表格
        self.generate_table(signals_check_result_columns, signals_check_result_detail_columns, cur_date)

        # 处理完成
        return "success"


    def deinit(self):
        """销毁检查实例"""
        pass

    def get_checker_name(self)-> str:
        """获取检查项名称"""
        return self.__class__.__name__

    def prepare_dirs(self):
        if not os.path.exists(self._data_input_path):
            print(f"File not found: {self._data_input_path}")
            return False, "File not found"
        if not os.path.exists(self._data_output_path):
            print(f"output folder not found, try create: {self._data_output_path}")
            os.makedirs(self._data_output_path)
            if not os.path.exists(self._data_output_path):
                return False, "output folder not found, try create failed"
            else:
                print(f"output folder created: {self._data_output_path}")
                return True, ""
        else:
            print(f"output folder already exists: {self._data_output_path}")
        return True, ""

    def setup_logger(self, level=logging.INFO):
        """设置日志器，同时输出到控制台和文件"""
        date_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        log_file = os.path.join(self._data_output_path, f"{self._checker_name}_{date_time_str}.log")

        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 文件 handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)

        # 控制台 handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # 创建 logger
        logger = logging.getLogger(self._checker_name)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def parse_file(self, file_path):
        acce3d_data = []
        gyro_data = []
        pulse_data = []
        gnss_data = []
        image_data = []

        with open(file_path, 'r') as file:
            for line in file:
                try:
                    data = json.loads(line.strip(), parse_float=decimal.Decimal)
                    signal_value = data["value"]
                    signal_type = signal_value["type"]

                    if signal_type == "acce3d":
                        acce3d_data.append(signal_value)

                    elif signal_type == "gyro":
                        gyro_data.append(signal_value)

                    elif signal_type == "pulse":
                        pulse_data.append(signal_value)

                    elif signal_type == "gnss":
                        gnss_data.append(signal_value)
                    
                    elif signal_type == "image":
                        image_data.append(signal_value)

                except json.JSONDecodeError as e:
                    self._logger.error(f"Error parsing line: {line}. Error: {e}")

        return acce3d_data, gyro_data, pulse_data, gnss_data, image_data

    def image_check(self,image_data,gyro_data,acce3d_data,pulse_data,image_imu_time_thresh):
        fail_info = ""
        ## 图像前后100ms要有gyro
        check_result_image_gyro, fail_frames_info_image_gyro,fail_frames_image_gyro = self.sensor_time_diff_check("image",image_data,"timestampExposure","gyro",gyro_data,"sourceTickTime",image_imu_time_thresh)
        if check_result_image_gyro == False:
            fail_info += f"图像在{image_imu_time_thresh}ms内搜索不到陀螺仪数据<br>"
        ## 图像前后100ms要有acce3d
        check_result_image_acce3d, fail_frames_info_image_acce3d,fail_frames_image_acce3d = self.sensor_time_diff_check("image",image_data,"timestampExposure","acce3d",acce3d_data,"sourceTickTime",image_imu_time_thresh)
        if check_result_image_acce3d == False:
            fail_info += f"图像在{image_imu_time_thresh}ms内搜索不到加速度计数据<br>"
        
        ## 图像前后100ms要有车速数据
        check_result_image_pulse, fail_frames_info_image_pulse,fail_frames_image_pulse = self.sensor_time_diff_check("image",image_data,"timestampExposure","pulse",pulse_data,"sourceTickTime",image_imu_time_thresh)
        if  check_result_image_pulse == False:
            fail_info += f"图像在{image_imu_time_thresh}ms内搜索不到车速数据<br>"
        
        check_result = (check_result_image_gyro and check_result_image_acce3d and check_result_image_pulse)
            
        fail_frame_cnt = max(len(fail_frames_image_gyro), len(fail_frames_image_acce3d), len(fail_frames_image_pulse))

        # fail_info = (fail_frames_info_image_gyro+fail_frames_info_image_acce3d+fail_frames_info_image_pulse)
        
        ## 将失败的帧合并到一起，并去重复
        seen = set()
        unique_fail_frames = []
        for fail_list in [fail_frames_image_gyro, fail_frames_image_acce3d, fail_frames_image_pulse]:
            for frame in fail_list:
                # if frame not in seen:
                #     seen.add(frame)
                unique_fail_frames.append(frame)
        return check_result,fail_frame_cnt,fail_info,unique_fail_frames
        

    def gnss_check(self,gnss_data,image_data,gyro_data,acce3d_data,pulse_data,gnss_img_time_thresh,gnss_imu_time_thresh):
        fail_info = ""
        ##GNSS 前后500ms 有图像数据
        check_result_gnss_image, fail_frames_info_gnss_image,fail_frames_gnss_image = self.sensor_time_diff_check("gnss",gnss_data,"sourceTickTime","image",image_data,"timestampExposure",gnss_img_time_thresh)
        if check_result_gnss_image ==  False:
            fail_info += f"GNSS在{gnss_img_time_thresh}ms时间内搜不到图像数据"
        
        ##GNSS前后100ms要有gyro
        check_result_gnss_gyro, fail_frames_info_gnss_gyro,fail_frames_gnss_gyro = self.sensor_time_diff_check("gnss",gnss_data,"sourceTickTime","gyro",gyro_data,"sourceTickTime",gnss_imu_time_thresh)
        if check_result_gnss_gyro ==  False:
            fail_info += f"GNSS在{gnss_imu_time_thresh}ms时间内搜不到陀螺仪数据"

        ## GNSS前后100ms要有acce3d
        check_result_gnss_acce3d, fail_frames_info_gnss_acce3d,fail_frames_gnss_acce3d = self.sensor_time_diff_check("gnss",gnss_data,"sourceTickTime","acce3d",acce3d_data,"sourceTickTime",gnss_imu_time_thresh)
        if check_result_gnss_acce3d ==  False:
            fail_info += f"GNSS在{gnss_imu_time_thresh}ms时间内搜不到加速度数据"
        
        ## GNSS前后100ms要有车速数据
        check_result_gnss_pulse, fail_frames_info_gnss_pulse,fail_frames_gnss_pulse = self.sensor_time_diff_check("gnss",gnss_data,"sourceTickTime","pulse",pulse_data,"sourceTickTime",gnss_imu_time_thresh)
        if check_result_gnss_pulse ==  False:
            fail_info += f"GNSS在{gnss_imu_time_thresh}ms时间内搜不到车速数据"
        
        check_result = (check_result_gnss_acce3d and check_result_gnss_pulse and check_result_gnss_image and check_result_gnss_gyro)
        
        ##失败的帧数取最大
        fail_frame_cnt = max(len(fail_frames_gnss_image), len(fail_frames_gnss_gyro), len(fail_frames_gnss_acce3d), len(fail_frames_gnss_pulse))

        ## 失败的细节信息
        # fail_info = (fail_frames_info_gnss_image+fail_frames_info_gnss_gyro+fail_frames_info_gnss_acce3d+fail_frames_info_gnss_pulse)
        ## 将失败的帧合并到一起，并去重复
        seen = set()
        unique_fail_frames = []
        for fail_list in [fail_frames_gnss_image, fail_frames_gnss_gyro, fail_frames_gnss_acce3d, fail_frames_gnss_pulse]:
            for frame in fail_list:
                # if frame not in seen:
                #     seen.add(frame)
                unique_fail_frames.append(frame) 
        return check_result,fail_frame_cnt,fail_info,unique_fail_frames
    ## sourceTickTime 开始时间大于100ms
    def sourceTickTime_check(self,signal_name, sensor_datas, thresh,field):
        check_result = "Pass"
        fail_frames_info = ""
        if len(sensor_datas) < 1:
            check_result = "Fail"
            fail_frames_info = "数据为空"
            self._logger.error(f"{signal_name}数据为空")
            return check_result, fail_frames_info
        first_frame = sensor_datas[0]
        
        if field not in first_frame:
            check_result = "Fail"
            fail_frames_info = f"数据缺少{field}字段"
            self._logger.error(f"{signal_name}数据缺少{field}字段")
            return check_result, fail_frames_info
        
        sourceTickTime = first_frame[field]
        if int(sourceTickTime) < thresh:
            check_result = "Fail"
            fail_frames_info = f"{signal_name}第一帧{field}小于100ms,{field}:{sourceTickTime}"
            self._logger.error(f"{signal_name}第一帧{field}小于100ms,{field}:{sourceTickTime}")
            return check_result, fail_frames_info
        return check_result, fail_frames_info
    
    ## 获取指定时间范围内的数据
    def query_data_by_time(self, target_time_series_sorted, time_start, time_end):
        result = []
        for time in target_time_series_sorted:
            time = int(time)
            if time < time_start:
                continue
            if time > time_end:
                break
            if time_start <= time <= time_end:
                result.append(time)
            
        return result
    
    def find_closest_timestamp(self,target_timestamps, target):
        """
        在已排序的时间戳列表中找到与 target 最接近的时间戳
        :param target_timestamps: 已排序的时间戳列表 (升序)
        :param target: 目标时间戳 (int 或 float)
        :return: 最接近的时间戳
        """
        if not target_timestamps:
            return None  # 空列表处理

        pos = bisect.bisect_left(target_timestamps, int(target))

        # 边界情况处理
        if pos == 0:
            return target_timestamps[0]
        if pos == len(target_timestamps):
            return target_timestamps[-1]

        # 比较 pos 和 pos - 1 处的两个候选值
        before = target_timestamps[pos - 1]
        after = target_timestamps[pos]
        
        if abs(after - target) < abs(before - target):
            return after
        else:
            return before
    
    ##传感器前后数据检查
    def sensor_time_diff_check(self,query_name, query_sensor_datas, query_time_field, target_name,target_sensor_datas, target_time_field, thresh):
        check_result = True
        fail_frames_info = ""
        fail_frames = []
        ## 取出所有图像的时间戳
        target_timestamps = [int(target_frame[target_time_field]) for target_frame in target_sensor_datas]
        ## 对图像时间戳排序
        target_timestamps.sort()
        
        if len(query_sensor_datas) < 1:
            check_result = False
            fail_frames_info = "数据为空"
            self._logger.error(f"{query_name}数据为空")
            return check_result, fail_frames_info
        for i in range(len(query_sensor_datas)):
            query_frame = query_sensor_datas[i]
            if query_time_field not in query_frame:
                check_result = False
                fail_frames_info = f"数据缺少{query_time_field}字段"
                self._logger.info(f"{query_name}数据缺少{query_time_field}字段")
                
                return check_result, fail_frames_info
            time = int(query_frame[query_time_field])
            time_start = time - thresh
            time_end = time + thresh
            # result = self.query_data_by_time(target_timestamps, time_start, time_end)
            target_time = self.find_closest_timestamp(target_timestamps,time)
            if abs(target_time - time)  > thresh:
                fail_frames_info += "时间戳不一致，目标时间戳为{}，实际时间戳为{}".format(target_time,time)
                check_result = False
                fail_frames.append(query_frame)
                self._logger.error(query_name+"时间戳:"+str(time)+",在前后"+str(thresh)+"ms范围内没有找到"+target_name+"数据,找到的最近的时间戳为:"+str(target_time))

        if len(fail_frames) > 0:
            fail_frames_info = f"{query_name}前后{thresh}ms无{target_name}数据,异常帧:"
            for fail_frame in fail_frames: 
                time = int(fail_frame[query_time_field])
                fail_frames_info += f"{time}\n"
                fail_frames_info +=","
        return check_result, fail_frames_info,fail_frames


    # 构建表格样式函数
    def generate_table(self, signals_check_result_columns, signals_check_result_detail_columns, cur_date):
        # 生成表格
        cell_colors = []
        for _ in range(len(signals_check_result_columns)):
            cell_colors.append(['lavender'] * len(signals_check_result_columns["检查结果"]))  # 默认颜色

        for i in range(len(signals_check_result_columns["检查结果"])):
            if signals_check_result_columns["检查结果"][i] == "Fail":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'lightcoral'
            elif signals_check_result_columns["检查结果"][i] == "Warnning":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = '#fffacd'
        # 绘制表格
        table_result = go.Table(
            header=dict(values=list(signals_check_result_columns.keys()), fill_color='paleturquoise', align='center'),
            cells=dict(
                values=[signals_check_result_columns[k] for k in signals_check_result_columns],
                fill_color=cell_colors,
                align=['left', 'center', 'left', 'center', 'center'],
                format=[None, None, 'html', None, None]  # 假设“检查规则”是第3列，用 'html' 表示该列支持HTML
            )
        )
        table_result_title = f"{self._checker_title}结果"

        table_result_detail = go.Table(
                header=dict(values=list(signals_check_result_detail_columns.keys()), fill_color='paleturquoise',
                            align='center'),
                cells=dict(values=[signals_check_result_detail_columns[k] for k in signals_check_result_detail_columns],
                           fill_color=cell_colors, align='center')
        )
        table_result_detail_title = f"{self._checker_title}详情"
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=2,  # 三行
            cols=1,  # 一列
            specs=[[{'type': 'table'}],  # 第一行是一个表格
                   [{'type': 'table'}]],  # 第二行是一个表格
            subplot_titles=(table_result_title, table_result_detail_title),  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        # 将表格添加到对应的子图位置
        fig.add_trace(table_result, row=1, col=1)
        fig.add_trace(table_result_detail, row=2, col=1)

        # --- 更新整体布局 ---
        fig.update_layout(
            height=900,  # 可能需要调整高度以容纳所有表格
            title_text=f"<b>{self._checker_title}结果汇总</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50),  # 调整边距
            showlegend=False
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_table_html_path,
                       full_html=True,
                       include_plotlyjs='cdn')

# Example usage (for testing this file directly)
if __name__ == "__main__":
    # 添加可选参数（带默认值）
    default_input_path = "/Users/<USER>/code/vae_issue_tools/signalAnalysisTool/tests/demodata/20250409_113851"  # 替换为你想设置的默认路径

    parser = argparse.ArgumentParser()
    parser.add_argument("--input", "-i", help="数据根路径", default=default_input_path)
    args = parser.parse_args()

    checker = C06TimestampConsistent()
    print(f"Checker Name: {checker.get_checker_name()}")
    checker.init(args.input)
    checker.check()
    checker.deinit()
