import decimal
import logging
import sys
import os
import argparse
import json
from datetime import datetime
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from decimal import Decimal
import bisect
import statistics
import csv
import numpy as np
import re
import copy


current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir)) # Up two levels to signalAnalysisTool
sys.path.insert(0, parent_dir)

try:
    from SignalCheckerInterface import SignalCheckerInterface
except ImportError:
    print("Could not import SignalCheckerInterface directly. Ensure signalAnalysisTool is in PYTHONPATH.")

class DecimalEncoder(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, Decimal):
            return str(o)  # 保留原始精度
        return super().default(o)


class C12PulseAccuracy(SignalCheckerInterface):
    _checker_title= "轮速精度检查"
    _checker_name = ""
    _data_input_path = ""
    _data_data_txt_path = ""
    _data_output_path = ""
    _data_output_check_result_json_path = ""
    _data_output_check_result_detail_json_path = ""
    _data_output_check_result_table_html_path = ""

    def init(self, input_path: str):
        _checker_name= self.get_checker_name()
        """初始化传入数据路径"""
        self._data_input_path = input_path
        folder_name = os.path.basename(os.path.normpath(self._data_input_path))
        self._data_data_txt_path = os.path.join(self._data_input_path, f"AutoSdkDemo/online/vlane/{folder_name}/data.txt")
        self._summary_txt_path = os.path.join(self._data_input_path, f"AutoSdkDemo/online/vlane/summary.txt")
        self._data_output_path = os.path.join(self._data_input_path, "CheckResult/" + self.get_checker_name())
        self._data_output_check_result_json_path = os.path.join(self._data_output_path, "check_result.json")
        self._data_output_check_result_detail_json_path = os.path.join(self._data_output_path, "check_result_detail.json")
        self._data_output_check_result_table_html_path = os.path.join(self._data_output_path, "check_result_table.html")
        self._data_output_check_result_scatter_html_path = os.path.join(self._data_output_path, "check_result_scatter.html")
        self._ground_truth_path = ""
        self.prepare_dirs()
        self._logger = self.setup_logger()
        self._logger.info("init")
        pass
    
    def is_ground_truth_exist(self):
        find_folder = False
        find_ground_truth = False
        input_path = self._data_input_path
        folder_path = ""
        for folder in os.listdir(input_path):
            full_path = os.path.join(input_path, folder)
            if os.path.isdir(full_path) and folder.startswith("GT_") and folder.endswith("_plaintext"):
                find_folder = True
                folder_path = full_path
        if not find_folder:
            self._logger.error("未找到真值文件夹")
            return False, "未找到真值文件夹"
        ground_truth_path = os.path.join(folder_path, "loc_gt.txt")
        if os.path.exists(ground_truth_path):
            find_ground_truth = True
            self._ground_truth_path = ground_truth_path
            return True, ""
        else:
            self._logger.error("未找到真值文件")
            return False, "未找到真值文件"
    def check(self) -> str:
        """进行信号检查"""
        prepare_result, prepare_result_msg = self.prepare_dirs()
        if not prepare_result:
            return prepare_result_msg
        
        self._logger.info("====check====")
        speed_diff_thresh = 1.0
        #超过此阈值就记录数据
        speed_diff_record_thresh = 3.6
        scale_thresh = [0.95,1.05]
        signals_config = {
            "原始轮速误差检查": {"check_rules": "误差均值或中位数在正负1km/h之内","threshold": speed_diff_thresh,"is_turn":False,"is_scale":False},
            "轮速尺度因子检查": {"check_rules": "误均值或中位数在0.95~1.05范围<br>尺度因子的16%或84%分位数在0.95~1.05范围之内","threshold_list": scale_thresh,"is_turn":False,"is_scale":False},
            "标定轮速误差检查（全场景）": {"check_rules": "误差均值或中位数在正负1km/h之内","threshold": speed_diff_thresh,"is_turn":False,"is_scale":True},
            "标定轮速误差检查(转弯或掉头)": {"check_rules": "误差均值或中位数在正负1km/h之内","threshold": speed_diff_thresh,"is_turn":True,"is_scale":False},
            "误差超限积分检查": {"check_rules": "误差超限区间内轮速误差积分小于3.5米（全场景）","threshold": speed_diff_thresh,"is_turn":False,"is_scale":False},
        }
        # 定义检查结果头
        cur_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        signals_check_result= {
            "checker": self.get_checker_name(),
            "title": f"{self._checker_title}",
            "datatxt": self._data_data_txt_path,
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResult"
        }
        signals_check_result_detail = {
            "checker": self.get_checker_name(),
            "title": f"{self._checker_title}详情",
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResultDetail"
        }
        signals_check_result_items = []
        signals_check_result_detail_items = []
        signals_check_result_columns = {"检查项":[], "统计数值":[],  "检查规则":[], "检查结果":[],"描述":[]}
        signals_check_result_detail_columns = {"检查项":[], "检查详情":[]}
        gt_datas = self.parse_ground_truth_file()
        pulse_datas = self.get_pulse_data()
        sensor_datas = pulse_datas
        pulse_datas_processed = self.get_cal_pulse_data(pulse_datas)
        if pulse_datas_processed is None:
            return "未找到标定数据"
        self._logger.info(f"轮速帧数:{len(pulse_datas)}")
        self._logger.info(f"标定成功的轮速帧数:{len(pulse_datas_processed)}")
        self._logger.info(f"真值帧数:{len(gt_datas)}")
        mean_speed_diff = 0
        median_speed_diff = 0
        mean_scale_diff = 0
        median_scale_diff = 0
        scale_16 = 0
        scale_84 = 0
        max_sum_diff = 0
        mean_speed_diff_turn = 0
        median_speed_diff_turn = 0
        for check_name, config in signals_config.items():
            is_turn = config["is_turn"]
            is_scale = config["is_scale"]
            fail_frames = []
            self._logger.info(f"检查项:{check_name}")
            ##替换标定后的轮速
            if check_name in ["标定轮速误差检查（全场景）" , "标定轮速误差检查(转弯或掉头)"]:
                sensor_datas = pulse_datas_processed
            check_result = "Pass"
            checkResultStatistic = ""
            if len(sensor_datas) > 0:
                if(check_name in ["原始轮速误差检查" , "标定轮速误差检查（全场景）"]):
                    mean_speed_diff,median_speed_diff,mean_speed_diff_turn,median_speed_diff_turn,mean_scale_diff,median_scale_diff,scale_16,scale_84,max_sum_diff,bad_speed_list,bad_speed_list_turn,bad_max_time_interval,bad_scale_list,gnss_rtk_speed_list = self.pulse_speed_cal(sensor_datas,gt_datas,speed_diff_record_thresh,scale_thresh,is_turn,is_scale)

                if check_name in ["原始轮速误差检查" , "标定轮速误差检查（全场景）" , "标定轮速误差检查(转弯或掉头)"]:
                    mean_speed_check_result =  True if (abs(mean_speed_diff) < config["threshold"]) else False
                    media_speed_check_result =  True if (abs(median_speed_diff) < config["threshold"]) else False
                    mean_speed_diff_turn_result =  True if (abs(mean_speed_diff) < config["threshold"]) else False
                    media_speed_check_result =  True if (abs(median_speed_diff) < config["threshold"]) else False
                    if check_name == "标定轮速误差检查(转弯或掉头)":
                        mean_speed_check_result =  True if (abs(mean_speed_diff_turn) < config["threshold"]) else False
                        media_speed_check_result =  True if (abs(median_speed_diff_turn) < config["threshold"]) else False 
                        mean_speed_diff = mean_speed_diff_turn
                        median_speed_diff = median_speed_diff_turn
                    
                    checkResultStatistic = f"<br>轮速误差均值:{mean_speed_diff:.2f}<br>轮速误差中位数:{median_speed_diff:.2f}"
                    if not mean_speed_check_result:
                        if check_name == "原始轮速误差检查":
                            check_result = "WARNING"
                        else:
                            check_result = "Fail" 
                    if not media_speed_check_result:
                        if check_name == "原始轮速误差检查":
                            check_result = "WARNING"
                        else:
                            check_result = "Fail" 
                if check_name == "轮速尺度因子检查":
                    thresh = config["threshold_list"]
                    mean_scale_diff_check_result = True if mean_scale_diff >= thresh[0] and mean_scale_diff <= thresh[1] else False
                    media_scale_diff_check_result = True if median_scale_diff >= thresh[0] and median_scale_diff <= thresh[1] else False
                    scale_16_check_result = True if scale_16 >= thresh[0] and scale_16 <= thresh[1] else False
                    scale_84_check_result = True if scale_84 >= thresh[0] and scale_84 <= thresh[1] else False
                    result = mean_scale_diff_check_result and media_scale_diff_check_result and scale_16_check_result and scale_84_check_result
                    checkResultStatistic = f"<br>轮速尺度因子均值:{mean_scale_diff:.2f}\
                    <br>轮速尺度因子中位数:{mean_scale_diff:.2f}\
                    <br>16%分位轮速尺度因子:{scale_16:.2f}\
                    <br>84%分位轮速尺度因子:{scale_84:.2f}\
                    <br>轮速尺度因子均值检查结果:{mean_scale_diff_check_result}\
                    <br>轮速尺度因子中位数检查结果:{media_scale_diff_check_result}\
                    <br>16%分位轮速尺度因子检查结果:{scale_16_check_result}\
                    <br>84%分位轮速尺度因子检查结果:{scale_16_check_result}"    
                    if not result:
                        check_result = "WARNING"
                        
                if check_name == "误差超限积分检查":
                    checkResultStatistic = f"<br>超限区间误差积分最大累计值:{max_sum_diff:.2f}"
                    if max_sum_diff > config["threshold"]:
                        check_result = "Fail"
            else:
                check_result = "Fail"
                if check_name in ["标定轮速误差检查（全场景）" , "标定轮速误差检查(转弯或掉头)"]:
                    checkResultStatistic = "<br>找不到标定成功区间内的轮速数据"
                else:
                    checkResultStatistic = "<br>轮速数据为空"
                    
            self._logger.info(checkResultStatistic)        
            current_check_result = {
            "checkItem": check_name,
            "checkResult": check_result,
            "checkResultStatistic": checkResultStatistic,
            "checkRules": config["check_rules"],
            "description": ""
            }
            signals_check_result_items.append(current_check_result)
            signals_check_result_columns["检查项"].append(check_name)
            signals_check_result_columns["统计数值"].append(checkResultStatistic)
            signals_check_result_columns["检查规则"].append(config["check_rules"])
            signals_check_result_columns["检查结果"].append(check_result)
            signals_check_result_columns["描述"].append("")
            
            if check_name in ["原始轮速误差检查","标定轮速误差检查（全场景）"]:
                fail_frames = bad_speed_list
            elif check_name =="轮速尺度因子检查":
                fail_frames = bad_scale_list
            elif check_name == "误差超限积分检查":
                fail_frames = bad_max_time_interval
            elif check_name == "标定轮速误差检查(转弯或掉头)":
                fail_frames = bad_speed_list_turn
            
            check_result_detail = {
            "checkItem": check_name,
            "total_frames": len(sensor_datas),
            "fail_frames": fail_frames,
            }
            
            ## 将失败的写入csv
            if check_result == "Fail":
                csv_file = os.path.join(self._data_output_path, f"{check_name}_checkfailed.csv")
                self.write_csv(csv_file, fail_frames)
            
            ## 只在表格里显示前5帧，不然会显示不下
            fail_frames_first_10 = fail_frames[:5] if len(fail_frames) > 5 else fail_frames
        # detail_list = [{current_time_field: item[current_time_field], source_time_field: item[source_time_field]} for item in delay_frame]
            detail_list_str = json.dumps(fail_frames_first_10, ensure_ascii=False, indent=2)

            signals_check_result_detail_items.append(check_result_detail)

            signals_check_result_detail_columns["检查项"].append(check_name)
            signals_check_result_detail_columns["检查详情"].append(detail_list_str)

        # 组装完整检查结果
        signals_check_result["checkResult"] = signals_check_result_items
        signals_check_result_detail["checkResultDetail"] = signals_check_result_detail_items

        # 保存结果
        with open(self._data_output_check_result_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result, f, ensure_ascii=False, indent=4, cls=DecimalEncoder)

        with open(self._data_output_check_result_detail_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result_detail, f, ensure_ascii=False, indent=4, cls=DecimalEncoder)

        # 生成表格
        self.generate_table(signals_check_result_columns, signals_check_result_detail_columns, cur_date)
        self.generate_scatter(gnss_rtk_speed_list, cur_date)

        # 处理完成
        return "success"

    def write_csv(self, csv_file, fail_frames):
        os.makedirs(os.path.dirname(csv_file), exist_ok=True)
        # 根据 fail_frames 的类型判断应该写入哪些字段
        with open(csv_file, mode='w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            first_item = fail_frames[0] if len(fail_frames) > 0 else {}
            
            # 动态写入表头和内容
            if isinstance(first_item, dict):
                headers = list(first_item.keys())
                writer.writerow(headers)
                
                for item in fail_frames:
                    row = [item.get(header, "") for header in headers]
                    writer.writerow(row)
            else:
                # 如果不是字典格式，按原始方式处理
                writer.writerow(["frame"])
                for item in fail_frames:
                    writer.writerow([item])
    
    def generate_scatter(self, alignment_data_list, cur_date):
        """
        生成散点图，显示真值速度和轮速对比
        alignment_data_list: 包含对齐数据的列表，每个元素应包含时间戳、真值速度、轮速等信息
        """
        if not alignment_data_list or len(alignment_data_list) == 0:
            print("没有数据可以绘制")
            return
        
        # 提取数据
        tick_times = []
        true_speeds = []
        pulse_speeds = []
        
        for data in alignment_data_list:
            # 根据实际数据结构调整字段名
            if isinstance(data, dict):
                tick_times.append(data.get("pulse_sourceTickTime", 0))
                true_speeds.append(data.get("gt_speed", 0))
                pulse_speeds.append(data.get("pulse_speed", 0))
            else:
                # 如果是对象，根据实际属性名调整
                tick_times.append(getattr(data, 'pulse_sourceTickTime', 0))
                true_speeds.append(getattr(data, 'gt_speed', 0))
                pulse_speeds.append(getattr(data, 'pulse_speed', 0))
        
        # 创建图表
        fig = go.Figure()
        
        # 添加真值速度线
        fig.add_trace(
            go.Scatter(
                x=tick_times,
                y=true_speeds,
                name='真值速度',
                line=dict(color='blue', width=2),
                mode='lines+markers',
                marker=dict(size=4),
                hovertemplate="时间: %{x}<br>真值速度: %{y:.2f} km/h<extra></extra>"
            )
        )
        
        # 添加轮速线
        fig.add_trace(
            go.Scatter(
                x=tick_times,
                y=pulse_speeds,
                name='轮速',
                line=dict(color='red', width=2),
                mode='lines+markers',
                marker=dict(size=4),
                hovertemplate="时间: %{x}<br>轮速: %{y:.2f} km/h<extra></extra>"
            )
        )
        
        # 更新布局
        fig.update_layout(
            title=f"<b>{self._checker_title}速度对比图-{cur_date}</b>",
            title_x=0.5,
            xaxis_title="时间戳 (ticktime)",
            yaxis_title="速度 (km/h)",
            height=600,
            margin=dict(t=100, b=50, l=50, r=50),
            hovermode='x unified',
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            ),
            grid=dict(rows=1, columns=1),
            showlegend=True
        )
        
        # 添加网格线
        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
        
        # 保存图表
        fig.write_html(self._data_output_check_result_scatter_html_path)
        print(f"散点图已保存到: {self._data_output_check_result_scatter_html_path}")
        
    def deinit(self):
        """销毁检查实例"""
        pass

    def get_checker_name(self)-> str:
        """获取检查项名称"""
        return self.__class__.__name__

    def prepare_dirs(self):
        if not os.path.exists(self._data_input_path):
            print(f"File not found: {self._data_input_path}")
            return False, "File not found"
        if not os.path.exists(self._summary_txt_path):
            print(f"File not found: {self._summary_txt_path}")
            return False, "File not found"
        ground_exist,_ = self.is_ground_truth_exist()
        if not ground_exist:
            print(f"File not found: {self._ground_truth_path}")
            return False, "File not found"
        if not os.path.exists(self._data_output_path):
            print(f"output folder not found, try create: {self._data_output_path}")
            os.makedirs(self._data_output_path)
            if not os.path.exists(self._data_output_path):
                return False, "output folder not found, try create failed"
            else:
                print(f"output folder created: {self._data_output_path}")
                return True, ""
        else:
            print(f"output folder already exists: {self._data_output_path}")
        return True, ""

    def setup_logger(self, level=logging.INFO):
        """设置日志器，同时输出到控制台和文件"""
        date_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        log_file = os.path.join(self._data_output_path, f"{self._checker_name}_{date_time_str}.log")

        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 文件 handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)

        # 控制台 handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # 创建 logger
        logger = logging.getLogger(self._checker_name)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def get_pulse_data(self):
        acce3d_data, gyro_data, pulse_data, gnss_data, image_data = self.parse_file(self._data_data_txt_path)
        return pulse_data
    def parse_file(self, file_path):
        acce3d_data = []
        gyro_data = []
        pulse_data = []
        gnss_data = []
        image_data = []

        with open(file_path, 'r') as file:
            for line in file:
                try:
                    data = json.loads(line.strip(), parse_float=decimal.Decimal)
                    signal_value = data["value"]
                    signal_type = signal_value["type"]

                    if signal_type == "acce3d":
                        acce3d_data.append(signal_value)

                    elif signal_type == "gyro":
                        gyro_data.append(signal_value)

                    elif signal_type == "pulse":
                        pulse_data.append(signal_value)

                    elif signal_type == "gnss":
                        gnss_data.append(signal_value)
                    
                    elif signal_type == "image":
                        image_data.append(signal_value)

                except json.JSONDecodeError as e:
                    self._logger.error(f"Error parsing line: {line}. Error: {e}")

        return acce3d_data, gyro_data, pulse_data, gnss_data, image_data

    def parse_ground_truth_file(self):
        m_s_to_km_h = 3.6
        time_scale = 1e6
        file_path = self._ground_truth_path
        """
        解析真值文件 loc_gt.txt，每列的含义如下：
        
        ticktime (int): 时间戳（单位：tick）
        utctime (float): UTC时间
        ggatime (float): GGA时间
        status (int): 状态码
        latitude (float): 纬度
        longitude (float): 经度
        accuracy (float): 准确度
        speed (float): 速度
        course (float): 航向角
        altitude (float): 海拔高度
        yaw_rate (float): 偏航率
        
        :param file_path: 文件路径
        :return: 解析后的数据列表，每个元素是一个字典
        """

        ground_truth_data = []

        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:
                    continue  # 跳过空行

                parts = line.split(',')

                if len(parts) != 11:
                    print(f"警告：跳过格式错误的行：{line}")
                    continue

                try:
                    data = {
                        "ticktime": int(parts[0])/time_scale,
                        "utctime": float(parts[1]),
                        "ggatime": float(parts[2]),
                        "status": int(parts[3]),
                        "latitude": float(parts[4]),
                        "longitude": float(parts[5]),
                        "accuracy": float(parts[6]),
                        "speed": float(parts[7])*m_s_to_km_h,
                        "course": float(parts[8]),
                        "altitude": float(parts[9]),
                        "yaw_rate": float(parts[10])
                    }
                    ground_truth_data.append(data)
                except ValueError as e:
                    print(f"解析错误：{e}，行内容：{line}")

        return ground_truth_data
    ##找到所在的时间区间
    def find_time_interval(self,query_time, gt_time_list):
        """
        在有序的时间列表中，使用二分查找找到 query_time 所在的区间。
        
        :param query_time: 查询时间
        :param gt_time_list: 已排序的时间戳列表
        :return: 包含两个元素的列表 [start_time, end_time] 表示所在区间；
                如果 query_time 小于所有时间，则返回 [None, gt_time_list[0]]
                如果 query_time 大于等于最后一个时间，则返回 [gt_time_list[-1], None]
        """
        index = bisect.bisect_right(gt_time_list, query_time)

        if index == 0:
            return None, 0  # query_time < 最小时间
        elif index == len(gt_time_list):
            return len(gt_time_list)-1, None  # query_time >= 最大时间
        else:
            return index - 1, index

    ## 计算rtk天向角速度
    def calculate_rtk_course_speed(self,start_time_idx,end_time_idx,gt_data):
        start_time = gt_data[start_time_idx]["ticktime"]
        end_time = gt_data[end_time_idx]["ticktime"]
        ticktime_diff = end_time - start_time
        start_course = gt_data[start_time_idx]["course"]
        endcourse = gt_data[end_time_idx]["course"]
        course_diff = endcourse - start_course
        if course_diff > 180:
            course_diff -= 360
        elif course_diff < -180:
            course_diff += 360
        angular_velocity = course_diff / (ticktime_diff / 1000)
        return angular_velocity
        
    ##通过线性插值获取速度
    def get_gt_speed(self,query_time,gt_data, gt_time_list):
        
        start_time_idx,end_time_idx = self.find_time_interval(query_time, gt_time_list)
        ##头尾不统计
        if start_time_idx is None or end_time_idx is None:
            return None,None
        
        start_time = gt_data[start_time_idx]["ticktime"]
        end_time = gt_data[end_time_idx]["ticktime"]
        dt = query_time - start_time
        gt_dt = end_time - start_time
        if gt_dt <= 0 > 150:
            self._logger.error(f"真值时间间隔异常:{gt_dt},start_time:{start_time},end_time:{end_time}")
            return None,None
        
        start_gt_speed = gt_data[start_time_idx]["speed"]
        end_gt_speed = gt_data[end_time_idx]["speed"]
        gt_speed = start_gt_speed + (end_gt_speed - start_gt_speed) * dt/gt_dt
        angular_velocity = self.calculate_rtk_course_speed(start_time_idx,end_time_idx,gt_data)
        
        return gt_speed,angular_velocity
    
    ## 从summary.txt中获取标定的轮速因子，将其修正到data.txt的轮速中
    def get_cal_pulse_data(self,pulse_datas):
        pulse_datas_processed = []
        cal_time_info_list = self.get_cal_time_from_summary_txt()
        if len(cal_time_info_list) == 0:
            self._logger.error("No calibration time info found in summary.txt")
            return None
        cal_time_list = [int(info["current_time"]) for info in cal_time_info_list]
        
        start_time = cal_time_info_list[0]["current_time"]
        ##修正轮速
        for pulse_data in pulse_datas:
            source_time = int(pulse_data["sourceTickTime"])
            #标定之前的不处理
            if source_time < start_time:
                continue  
            pulse_speed = float(pulse_data["speed"])
            last_index,_ = self.find_time_interval(source_time, cal_time_list)
            if last_index is None:
                continue
            scale = cal_time_info_list[last_index]["scale_after"]
            copied_pulse_data = copy.deepcopy(pulse_data)

            pulse_speed_cal = pulse_speed * scale
            copied_pulse_data["speed"] = pulse_speed_cal
            pulse_datas_processed.append(copied_pulse_data)
        return pulse_datas_processed
            
            
                    
    def get_cal_time_from_summary_txt(self):
        summary_txt_url = self._summary_txt_path
        # pattern = r'time: (\d+), before: ([\d.]+), after: ([\d.]+), gpsSum: ([\d.]+), pulseSum: ([\d.]+)'
        pattern = r"time:\s*(\d+),\s*before:\s*([\d.]+),\s*after:\s*([\d.]+),\s*gpsSum:\s*([\d.]+),\s*pulseSum:\s*([\d.]+)"
        cal_time_info_list = []
        with open(summary_txt_url, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()  # 去除行尾换行符等
                match = re.search(pattern, line)
                if match:
                    current_time = int(match.group(1))
                    scale_before = float(match.group(2))
                    scale_after = float(match.group(3))
                    pulse_sum = float(match.group(5))
                    ##只保留累计距离500m以上的标定结果，认为这个时候才是标定成功
                    if pulse_sum > 500:
                        current_info = \
                        {
                            "current_time": current_time,
                            "scale_before": scale_before,
                            "scale_after": scale_after,
                            "pulse_sum": pulse_sum
                        }
                        cal_time_info_list.append(current_info)
            
            return cal_time_info_list




    ##轮速检查
    def pulse_speed_cal(self,pulse_datas,gt_datas,speed_diff_thresh,scale_thresh,is_turn = False,is_scale = False):
        self._logger.info("开始循环进行轮速检查")
        speed_diff_list = []
        speed_diff_list_turn = []
        scale_list = []
        sum_diff = 0
        max_sum_diff = 0
        mean_speed_diff = 0
        median_speed_diff = 0
        mean_speed_diff_turn = 0
        median_speed_diff_turn = 0
        mean_scale_diff = 0
        median_scale_diff = 0
        scale_16 = 0
        scale_84 = 0
        
        bad_speed_list =[]
        bad_max_time_interval = []
        time_interval_tmp = []
        bad_scale_list = []
        gt_start_time = gt_datas[0]["ticktime"]
        gt_end_time = gt_datas[-1]["ticktime"]
        gnss_rtk_speed_list = []
        self._logger.info(f"pulse data len is {len(pulse_datas)}")

        gt_time_list = [data["ticktime"] for data in gt_datas]

        for i in range(len(pulse_datas)):
            pulse_data = pulse_datas[i]
            current_time = int(pulse_data["sourceTickTime"])
            last_time = int(pulse_datas[i-1]["sourceTickTime"]) if i > 0 else current_time

            dt = current_time - last_time
            if current_time < gt_start_time or current_time > gt_end_time:
                continue
            if dt <= 0:
                self._logger.error(f"轮速时间戳回退,上一帧时间:{last_time},当前时间:{current_time}")
                continue
            pulse_speed = float(pulse_data["speed"])
            gt_speed,angular_velocity = self.get_gt_speed(current_time,gt_datas,gt_time_list)

            if gt_speed is None:
                # self._logger.error(f"没有找到对应时间戳的真值：{current_time}")
                continue
            speed_diff = gt_speed - abs(pulse_speed)

            gnss_rtk_speed_list.append({"pulse_sourceTickTime":current_time,"pulse_speed":pulse_speed,"gt_speed":gt_speed})
            
            if abs(pulse_speed) > 0:
                scale = gt_speed/abs(pulse_speed)
                scale_list.append(scale)
                if scale < scale_thresh[0] or scale > scale_thresh[1]:
                    bad_scale_list.append({"pulse_sourceTickTime":current_time,"pulse_speed":pulse_speed,"gt_speed":gt_speed,"scale":scale})
                
            if abs(speed_diff) > speed_diff_thresh:
                time_interval_tmp.append(current_time)
                sum_diff += abs(speed_diff/3.6*dt/1000)
                bad_speed_list.append({"pulse_sourceTickTime":current_time,"pulse_speed":pulse_speed,"gt_speed":gt_speed,"speed_diff":speed_diff,"angular_velocity":angular_velocity})
                # self._logger.error(f"速度误差过大,pulse_sourceTickTime:{current_time},pulse_speed:{pulse_speed},gt_speed:{gt_speed},speed_diff:{speed_diff},angular_velocity:{angular_velocity}")
            else:
                if len(time_interval_tmp) > 1:
                    bad_max_time_interval = [{"start_time":time_interval_tmp[0],"end_time":time_interval_tmp[-1]}]
                
                max_sum_diff = max(max_sum_diff,sum_diff)
                sum_diff = 0  
                time_interval_tmp.clear()              
            ## 转弯场景
            if abs(angular_velocity) > 10:
                speed_diff_list_turn.append(speed_diff)
                
                
            speed_diff_list.append(speed_diff)


        if speed_diff_list:
            mean_speed_diff = np.mean(speed_diff_list)
            median_speed_diff = np.median(speed_diff_list)   
        if scale_list:
            mean_scale_diff = np.mean(scale_list)
            median_scale_diff = np.median(scale_list)     
            scale_16 = np.percentile(scale_list, 16)
            scale_84 = np.percentile(scale_list, 84)
        if speed_diff_list_turn:
            mean_speed_diff_turn = np.mean(speed_diff_list_turn)
            median_speed_diff_turn = np.median(speed_diff_list_turn)
        self._logger.info(f"转弯掉头帧数:{len(speed_diff_list_turn)}")
        ##转弯的速度误差过大的帧
        bad_speed_list_turn = [k for k in bad_speed_list if k["angular_velocity"] > 10]
        self._logger.info("结束循环进行轮速检查")
        return mean_speed_diff,median_speed_diff,mean_speed_diff_turn,median_speed_diff_turn,mean_scale_diff,median_scale_diff,scale_16,scale_84,max_sum_diff,bad_speed_list,bad_speed_list_turn,bad_max_time_interval,bad_scale_list,gnss_rtk_speed_list
 
            
    # 构建表格样式函数
    def generate_table(self, signals_check_result_columns, signals_check_result_detail_columns, cur_date):
        # 生成表格
        cell_colors = []
        for _ in range(len(signals_check_result_columns)):
            cell_colors.append(['lavender'] * len(signals_check_result_columns["检查结果"]))  # 默认颜色

        for i in range(len(signals_check_result_columns["检查结果"])):
            if signals_check_result_columns["检查结果"][i] == "Fail":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'lightcoral'
            elif signals_check_result_columns["检查结果"][i] == "Warnning":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = '#fffacd'
        # 绘制表格
        table_result = go.Table(
            header=dict(values=list(signals_check_result_columns.keys()), fill_color='paleturquoise', align='center'),
            cells=dict(
                values=[signals_check_result_columns[k] for k in signals_check_result_columns],
                fill_color=cell_colors,
                align=['left', 'center', 'left', 'center', 'center'],
                format=[None, None, 'html', None, None]  # 假设“检查规则”是第3列，用 'html' 表示该列支持HTML
            )
        )
        table_result_title = f"{self._checker_title}结果"

        table_result_detail = go.Table(
                header=dict(values=list(signals_check_result_detail_columns.keys()), fill_color='paleturquoise',
                            align='center'),
                cells=dict(values=[signals_check_result_detail_columns[k] for k in signals_check_result_detail_columns],
                           fill_color=cell_colors, align='center')
        )
        table_result_detail_title = f"{self._checker_title}详情"
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=2,  # 三行
            cols=1,  # 一列
            specs=[[{'type': 'table'}],  # 第一行是一个表格
                   [{'type': 'table'}]],  # 第二行是一个表格
            subplot_titles=(table_result_title, table_result_detail_title),  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        # 将表格添加到对应的子图位置
        fig.add_trace(table_result, row=1, col=1)
        fig.add_trace(table_result_detail, row=2, col=1)

        # --- 更新整体布局 ---
        fig.update_layout(
            height=1000,  # 可能需要调整高度以容纳所有表格
            title_text=f"<b>{self._checker_title}结果汇总</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50),  # 调整边距
            showlegend=False
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_table_html_path,
                       full_html=True,
                       include_plotlyjs='cdn')

# Example usage (for testing this file directly)
if __name__ == "__main__":
    # 添加可选参数（带默认值）
    default_input_path = "/Users/<USER>/code/data/检查项测试真值/20250609_155224"  # 替换为你想设置的默认路径

    parser = argparse.ArgumentParser()
    parser.add_argument("--input", "-i", help="数据根路径", default=default_input_path)
    args = parser.parse_args()

    checker = C12PulseAccuracy()
    print(f"Checker Name: {checker.get_checker_name()}")
    checker.init(args.input)
    checker.check()
    checker.deinit()
