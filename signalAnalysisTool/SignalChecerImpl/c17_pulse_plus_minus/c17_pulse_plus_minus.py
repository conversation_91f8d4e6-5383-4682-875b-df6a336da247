import csv

import re
import logging
import sys
import os
import argparse
import json
from datetime import datetime
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from get_reverse_list import get_reverse_speed_list

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir)) # Up two levels to signalAnalysisTool
sys.path.insert(0, parent_dir)

try:
    from SignalCheckerInterface import SignalCheckerInterface
except ImportError:
    print("Could not import SignalCheckerInterface directly. Ensure signalAnalysisTool is in PYTHONPATH.")


class C17PulsePlusMinusImpl(SignalCheckerInterface):

    _checker_title = "轮速正负号检查"
    _checker_name = "C17PulsePlusMinus"
    _data_input_path = ""
    _data_data_txt_path = ""
    _data_summary_txt_path = ""
    _data_loc_gt_txt_path = ""
    _data_output_path = ""
    _data_output_check_result_json_path = ""
    _data_output_check_result_detail_json_path = ""
    _data_output_check_result_table_html_path = ""

    def init(self, input_path: str):
        """初始化传入数据路径"""
        self._data_input_path = input_path
        folder_name = os.path.basename(os.path.normpath(self._data_input_path))
        gt_folder = self.find_gt_folder()
        self._data_loc_gt_txt_path = os.path.join(self._data_input_path, f"{gt_folder}/loc_gt.txt") if gt_folder else None
        self._data_data_txt_path = os.path.join(self._data_input_path,
                                                f"AutoSdkDemo/online/vlane/{folder_name}/data.txt")
        self._data_summary_txt_path = os.path.join(self._data_input_path, "AutoSdkDemo/online/vlane/summary.txt")
        # self._data_loc_gt_txt_path = os.path.join(self._data_input_path, "GT_20250402_plaintext/loc_gt.txt")
        self._data_output_path = os.path.join(self._data_input_path, "CheckResult/" + self.get_checker_name())
        self._data_output_check_result_json_path = os.path.join(self._data_output_path, "check_result.json")
        self._data_output_check_result_detail_json_path = os.path.join(self._data_output_path,
                                                                       "check_result_detail.json")
        self._data_output_check_result_table_html_path = os.path.join(self._data_output_path, "check_result_table.html")
        self.prepare_dirs()
        self._logger = self.setup_logger()
        self._logger.info("init")
        pass

    def check(self) -> str:
        """进行信号检查"""
        prepare_result, prepare_result_msg = self.prepare_dirs()
        if not prepare_result:
            return prepare_result_msg

        # 定义信号丢帧率阈值
        signals_config = {
            "pulse": {"frequency_hz": 10, "speedField": "speed", "sourceTimeField": "sourceTickTime", "description": "轮速正负号检查"}
        }
        gear_config = {
            "D档": {"dict_num": 1, "dict_char": "D", "sign": "<", "threshold": "-1e-6", "speedField": "speed", "sourceTimeField": "sourceTickTime", "description": "D档轮速检查"},
            "N档": {"dict_num": 2, "dict_char": "N", "sign": ">", "threshold": "1e-6", "speedField": "speed", "sourceTimeField": "sourceTickTime", "description": "N档轮速检查"},
            "R档": {"dict_num": 3, "dict_char": "R", "sign": ">", "threshold": "1e-6", "speedField": "speed", "sourceTimeField": "sourceTickTime", "description": "R档轮速检查"},
            "P档": {"dict_num": 4, "dict_char": "P", "sign": ">", "threshold": "1e-6", "speedField": "speed", "sourceTimeField": "sourceTickTime", "description": "P档轮速检查"}
        }

        # 定义检查结果头
        cur_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        signals_check_result = {
            "checker": "lostRateChecker",
            "title": f"{self._checker_title}",
            "datatxt": self._data_data_txt_path,
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResult"
        }
        signals_check_result_detail = {
            "checker": "lostRateChecker",
            "title": f"{self._checker_title}详情",
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResultDetail"
        }
        signals_check_result_items = []
        signals_check_result_detail_items = []
        signals_check_result_columns = {"检查项": [], "统计数值": [], "检查规则": [], "检查结果": [], "描述": []}
        signals_check_result_detail_columns = {"检查项": [], "检查详情": []}
        check_failed_sourceticktime_intervals = {}

        # 解析文件
        pulse_data = self.parse_file(self._data_data_txt_path)
        #  计算所有倒车的时间段
        locs, models, true_data_dict = get_reverse_speed_list(self._data_loc_gt_txt_path)
        # 获取档位变化信息
        print("get_data_from_summary start")
        gear_change_info = self.get_data_from_summary(self._data_summary_txt_path)
        print("get_data_from_summary end")

        # 轮速正负检查
        for signal_name, config in signals_config.items():
            abnormal_pulse_data, check_result = self.check_pulse_sign(signal_name, pulse_data, config, locs, models, true_data_dict)
            abnormal_count = len(abnormal_pulse_data)
            pulse_count = len(pulse_data)
            current_check_result = {
                "checkItem": "轮速正负号检查",
                "checkResult": check_result,
                "checkResultStatistic": abnormal_count,
                "checkRules": "倒车时轮速为负，前进时轮速为正",
                "description": "不通过需手动排查"
            }
            signals_check_result_items.append(current_check_result)

            signals_check_result_columns["检查项"].append("轮速正负号检查")
            signals_check_result_columns["统计数值"].append(f"总帧数:{pulse_count}\n 异常次数:{abnormal_count}")
            signals_check_result_columns["检查规则"].append("倒车时轮速为负，前进时轮速为正")
            signals_check_result_columns["检查结果"].append(check_result)
            description = config["description"] if check_result == "Pass" else "不通过需手动排查"
            signals_check_result_columns["描述"].append(description)

            abnormal_data = []
            for pulse_data in abnormal_pulse_data:
                item = (pulse_data["tick_time"], pulse_data["speed"], pulse_data["status"], pulse_data["x"], pulse_data["y"])
                abnormal_data.append(item)
            check_failed_sourceticktime_intervals[signal_name] = abnormal_data

            check_result_detail = {
                "checkItem": "轮速正负号检查",
                "total_frames": pulse_count,
                "fail_frames": abnormal_pulse_data,
                "frame_loss_rate": "",
            }
            signals_check_result_detail_items.append(check_result_detail)
            signals_check_result_detail_columns["检查项"].append("轮速正负号检查")
            signals_check_result_detail_columns["检查详情"].append(check_failed_sourceticktime_intervals[signal_name])

        for signal_name, config in gear_config.items():
            abnormal_gear_data, check_result = self.check_gear_status(signal_name, pulse_data, config, true_data_dict, gear_change_info)
            abnormal_count = len(abnormal_gear_data)
            pulse_count = len(pulse_data)
            description = config["description"] if check_result == "Pass" else "不通过需手动排查"
            current_check_result = {
                "checkItem": f"{signal_name}轮速检查",
                "checkResult": check_result,
                "checkResultStatistic": abnormal_count,
                "checkRules": f"{signal_name}时，轮速{config["sign"]}{config["threshold"]}",
                "description": description
            }
            signals_check_result_items.append(current_check_result)

            signals_check_result_columns["检查项"].append(f"{signal_name}轮速检查")
            signals_check_result_columns["统计数值"].append(f"总帧数:{pulse_count}\n 异常次数:{abnormal_count}")
            signals_check_result_columns["检查规则"].append(f"{signal_name}时，轮速{config["sign"]}{config["threshold"]}")
            signals_check_result_columns["检查结果"].append(check_result)
            signals_check_result_columns["描述"].append(description)

            if abnormal_count > 0:
                abnormal_data = []
                for gear_data in abnormal_gear_data:
                    item = (gear_data["tick_time"], gear_data["speed"], gear_data["status"], gear_data["x"], gear_data["y"])
                    abnormal_data.append(item)
                check_failed_sourceticktime_intervals[signal_name] = abnormal_data
            else:
                check_failed_sourceticktime_intervals[signal_name] = [""]

            check_result_detail = {
                "checkItem": f"{signal_name}轮速检查",
                "total_frames": pulse_count,
                "fail_frames": abnormal_gear_data,
                "frame_loss_rate": "",
            }
            signals_check_result_detail_items.append(check_result_detail)
            signals_check_result_detail_columns["检查项"].append(f"{signal_name}轮速检查")
            signals_check_result_detail_columns["检查详情"].append(f"{check_failed_sourceticktime_intervals[signal_name]}")
        # 组装完整检查结果
        signals_check_result["checkResult"] = signals_check_result_items
        signals_check_result_detail["checkResultDetail"] = signals_check_result_detail_items

        # 保存结果
        with open(self._data_output_check_result_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result, f, ensure_ascii=False, indent=4)
        with open(self._data_output_check_result_detail_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result_detail, f, ensure_ascii=False, indent=4)
        for signal_name in check_failed_sourceticktime_intervals.keys():
            signal_data = check_failed_sourceticktime_intervals[signal_name]
            if len(signal_data) <= 0:
                continue
            csv_file = os.path.join(self._data_output_path, f"{signal_name}_checkfailed.csv")
            with open(csv_file, mode='w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(["tick_time", "speed", "status", "longitude", "latitude"])
                writer.writerows(signal_data)
            pass

        # 生成表格
        self.generate_table(signals_check_result_columns, signals_check_result_detail_columns, cur_date)

        # 处理完成
        return "success"

    def deinit(self):
        """销毁检查实例"""
        pass

    def get_checker_name(self) -> str:
        """获取检查项名称"""
        return "C17PulsePlusMinus"

    def prepare_dirs(self):
        if not os.path.exists(self._data_input_path):
            print(f"File not found: {self._data_input_path}")
            return False, "File not found"
        if not os.path.exists(self._data_output_path):
            print(f"output folder not found, try create: {self._data_output_path}")
            os.makedirs(self._data_output_path)
            if not os.path.exists(self._data_output_path):
                return False, "output folder not found, try create failed"
            else:
                print(f"output folder created: {self._data_output_path}")
                return True, ""
        else:
            print(f"output folder already exists: {self._data_output_path}")
        return True, ""

    def setup_logger(self, level=logging.INFO):
        """设置日志器，同时输出到控制台和文件"""
        date_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        log_file = os.path.join(self._data_output_path, f"{self._checker_name}_{date_time_str}.log")

        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 文件 handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)

        # 控制台 handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # 创建 logger
        logger = logging.getLogger(self._checker_name)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def parse_file(self, file_path):
        pulse_data = []

        with open(file_path, 'r') as file:
            for line in file:
                try:
                    data = json.loads(line.strip())
                    signal_type = data["value"]["type"]
                    time = data["time"]

                    if signal_type == "pulse":
                        data["value"]["time"] = time
                        pulse_data.append(data["value"])

                except json.JSONDecodeError as e:
                    print(f"Error parsing line: {line}. Error: {e}")

        return pulse_data

    # 轮速正负号检查
    def check_pulse_sign(self, signal_name, pulse_datas, config, locs, models, true_data_dict):
        check_result = "Pass"
        abnormal_pulse_data = []

        reverse_times = []

        if locs:
            ans = [[locs[0]]]
            for i in range(1, len(locs)):
                loc = locs[i]
                pre_loc = locs[i - 1]
                if models[loc.ind1].ticktime - models[pre_loc.ind1].ticktime < 1000:
                    ans[-1].append(loc)
                else:
                    ans.append([loc])
            for ele in ans:
                st = ele[0]
                end = ele[-1]
                print(models[st.ind1].ticktime, models[end.ind2].ticktime,
                      models[st.ind1].loc[0], models[st.ind1].loc[1], models[end.ind2].loc[0],
                      models[end.ind2].loc[1])
                record = {
                    "start_time": models[st.ind1].ticktime,
                    "end_time": models[end.ind2].ticktime
                }
                reverse_times.append(record)
        speedField = config["speedField"]
        tickTimeFeild = config["sourceTimeField"]
        for pulse_data in pulse_datas:
            speed = float(pulse_data[speedField])
            tick_time = int(pulse_data[tickTimeFeild])
            time = int(pulse_data["time"])
            # 遍历对比，若时间落在倒车区间轮速为正或非倒车区间轮速为负则异常，
            for reverse_time in reverse_times:
                start_time = reverse_time["start_time"]
                end_time = reverse_time["end_time"]
                if start_time <= tick_time <= end_time:
                    if speed > 0:
                        close_time, close_model = self.time_alignment(tick_time, true_data_dict)
                        abnormal_record = {
                            "tick_time": tick_time,
                            "speed": speed,
                            "status": "倒车",
                            "x": close_model["x"] if close_model else "",
                            "y": close_model["y"] if close_model else ""
                        }
                        abnormal_pulse_data.append(abnormal_record)
                else:
                    if speed < 0:
                        close_time, close_model = self.time_alignment(tick_time, true_data_dict)
                        abnormal_record = {
                            "tick_time": tick_time,
                            "speed": speed,
                            "status": "前进",
                            "x": close_model["x"] if close_model else "",
                            "y": close_model["y"] if close_model else ""
                        }
                        abnormal_pulse_data.append(abnormal_record)

        if len(abnormal_pulse_data) > 0:
            check_result = "Fail"

        return abnormal_pulse_data, check_result

    def check_gear_status(self, signal, pulse_datas, config, true_data_dict, gear_change_info):
        check_result = "Pass"
        abnormal_gear_data = []
        speedField = config["speedField"]
        tickTimeFeild = config["sourceTimeField"]
        gear_num = int(config["dict_num"])
        gear_str = config["dict_char"]
        sign = config["sign"]
        threshold = float(config["threshold"])
        for pulse_data in pulse_datas:
            speed = float(pulse_data[speedField])
            tick_time = int(pulse_data[tickTimeFeild])
            time = int(pulse_data["time"])
            for gear_info in gear_change_info:
                # 时间放宽1秒
                start_time = gear_info["start"] + 1000
                end_time = gear_info["end"] - 1000
                gear = int(gear_info["gear"])
                if start_time <= time <= end_time:
                    if gear == gear_num and self.compare(sign, speed, threshold):
                        close_time, close_model = self.time_alignment(tick_time, true_data_dict)
                        abnormal_record = {
                            "tick_time": tick_time,
                            "speed": speed,
                            "status": gear_str,
                            "x": close_model["x"] if close_model else "",
                            "y": close_model["y"] if close_model else ""
                        }
                        abnormal_gear_data.append(abnormal_record)
        if len(abnormal_gear_data) > 0:
            check_result = "Fail"
        return abnormal_gear_data, check_result

    def compare(self, sign, num_a, num_b):
        if sign == "<":
            return num_a < num_b
        elif sign == ">":
            return num_a > num_b
    # 构建表格样式函数
    def generate_table(self, signals_check_result_columns, signals_check_result_detail_columns, cur_date):
        # 生成表格
        cell_colors = []
        for _ in range(len(signals_check_result_columns)):
            cell_colors.append(['lavender'] * len(signals_check_result_columns["检查结果"]))  # 默认颜色

        # 对于丢帧率大于 0 的行，整行标红
        for i in range(len(signals_check_result_columns["检查结果"])):
            if signals_check_result_columns["检查结果"][i] != "Pass":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'lightcoral'
        # 绘制表格
        table_result = go.Table(
            header=dict(values=list(signals_check_result_columns.keys()), fill_color='paleturquoise',
                        align='center'),
            cells=dict(values=[signals_check_result_columns[k] for k in signals_check_result_columns],
                       fill_color=cell_colors, align='center')
        )
        table_result_title = f"{self._checker_title}结果"

        table_result_detail = go.Table(
            header=dict(values=list(signals_check_result_detail_columns.keys()), fill_color='paleturquoise',
                        align='center'),
            cells=dict(values=[signals_check_result_detail_columns[k] for k in signals_check_result_detail_columns],
                       fill_color=cell_colors, align='center')
        )
        table_result_detail_title = f"{self._checker_title}详情"
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=2,  # 三行
            cols=1,  # 一列
            specs=[[{'type': 'table'}],  # 第一行是一个表格
                   [{'type': 'table'}]],  # 第二行是一个表格
            subplot_titles=(table_result_title, table_result_detail_title),  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        # 将表格添加到对应的子图位置
        fig.add_trace(table_result, row=1, col=1)
        fig.add_trace(table_result_detail, row=2, col=1)

        # --- 更新整体布局 ---
        fig.update_layout(
            height=700,  # 可能需要调整高度以容纳所有表格
            title_text=f"<b>{self._checker_title}结果汇总</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50)  # 调整边距
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_table_html_path)

    def get_data_from_summary(self, summary_txt_url):
        file_start = None
        file_end = None
        file_end_line = None
        last_gear = None
        events = []
        gear_change_info = []
        # 预编译正则表达式
        time_pattern = re.compile(r'^\[([^\]]+)\]')
        gear_pattern = re.compile(r'\(new\)(\d+).*\(old\)(\d+)')

        # 打开文件并读取内容
        with open(summary_txt_url, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()  # 去除行尾换行符等
                if file_start is None:
                    # "从行首提取时间戳"
                    match = time_pattern.search(line)
                    time_str = match.group(1) if match else None
                    if not time_str:
                        continue
                    try:
                        dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                        timestamp = int(dt.timestamp() * 1000)
                    except Exception as e:
                        print(f"Error parsing time: {time_str}, error: {e}")
                        continue
                    file_start = timestamp
                file_end_line = line

                if 'gear state change' in line:
                    match = time_pattern.search(line)
                    time_str = match.group(1) if match else None
                    if not time_str:
                        continue
                    try:
                        dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                        timestamp = int(dt.timestamp() * 1000)
                    except Exception as e:
                        print(f"Error parsing time: {time_str}, error: {e}")
                        continue
                    gear_match = gear_pattern.search(line)
                    if gear_match:
                        gear_new = int(gear_match.group(1))
                        gear_old = int(gear_match.group(2))
                        events.append({'t': timestamp, 'new': gear_new, 'old': gear_old})
        if file_end_line:
            match = time_pattern.search(line)
            time_str = match.group(1) if match else None
            if time_str:
                dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                file_end = int(dt.timestamp() * 1000)
        if events:
            first_event = events[0]
            gear_change_info.append({
                'gear': first_event['old'],
                'start': file_start,
                'end': first_event['t']
            })

            prev_t = first_event['t']
            prev_new = first_event['new']

            for event in events[1:]:
                gear_change_info.append({
                    'gear': prev_new,
                    'start': prev_t,
                    'end': event['t']
                })
                prev_t = event['t']
                prev_new = event['new']

            gear_change_info.append({
                'gear': prev_new,
                'start': prev_t,
                'end': file_end
            })

        return gear_change_info

    def find_gt_folder(self):
        # 获取所有子文件夹
        subfolders = [f for f in os.listdir(self._data_input_path)]
        # 过滤出符合条件的文件夹
        gt_folders = [f for f in subfolders if f.startswith('GT_') and f.endswith('_plaintext')]
        # 强制校验
        assert len(gt_folders) <= 1, f"Found {len(gt_folders)} GT folders, expected 0 or 1"
        return gt_folders[0] if len(gt_folders) == 1 else None

    # 时间对齐方法
    def time_alignment(self, input_time, input_dict):
        if input_dict is not None and len(input_dict) > 0:
            higher_time, higher_data = self.higher_entry(input_dict, input_time)
            if higher_time is None or higher_data is None:
                print("higher_time or higher_data is None, return ")
                return None, None
            lower_time, lower_data = self.lower_entry(input_dict, input_time)
            if lower_time is None or lower_data is None:
                print("lower_time or lower_data is None, return ")
                return None, None

            higher_diff = abs(int(higher_time) - input_time)
            lower_diff = abs(int(lower_time) - input_time)

            # 如果时间戳相差100ms，说明时间对不上，直接返回
            if higher_diff > 100 or lower_diff > 100:
                print(f"sourceTrickTime and rtk time is too different")
                return None, None

            if higher_diff > lower_diff:
                return lower_time, lower_data
            else:
                return higher_time, higher_data

        return None, None

    # 实现类似 TreeMap.higherEntry 的功能
    def higher_entry(self, sorted_dict, input_timestamp):
        index = sorted_dict.bisect_right(input_timestamp)
        if index < len(sorted_dict):
            key = sorted_dict.iloc[index]
            return key, sorted_dict[key]
        else:
            print(f"higher_entry input_timestamp = {input_timestamp}, index = {index}, len(sorted_dict) = {len(sorted_dict)}")
            print(sorted_dict[sorted_dict.iloc[index - 1]])
            return None, None

    # 实现类似 TreeMap.lowerEntry 的功能
    def lower_entry(self, sorted_dict, input_timestamp):
        index = sorted_dict.bisect_left(input_timestamp)
        if index > 0:
            key = sorted_dict.iloc[index - 1]
            return key, sorted_dict[key]
        else:
            print(f"lower_entry input_timestamp = {input_timestamp}")
            return None, None

# Example usage (for testing this file directly)
if __name__ == "__main__":
    # 添加可选参数（带默认值）
    default_input_path = "/Users/<USER>/Downloads/sourceCode/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_154816"  # 替换为你想设置的默认路径
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", "-i", help="数据根路径", default=default_input_path)
    args = parser.parse_args()

    checker = C17PulsePlusMinusImpl()
    print(f"Checker Name: {checker.get_checker_name()}")
    checker.init(args.input)
    checker.check()
    checker.deinit()
