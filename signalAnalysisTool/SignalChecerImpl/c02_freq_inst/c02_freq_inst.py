import csv
import logging
import sys
import os
import argparse
import numpy as np
import json
from datetime import datetime
import plotly.graph_objects as go
from plotly.subplots import make_subplots

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir)) # Up two levels to signalAnalysisTool
sys.path.insert(0, parent_dir)

try:
    from SignalCheckerInterface import SignalCheckerInterface
except ImportError:
    print("Could not import SignalCheckerInterface directly. Ensure signalAnalysisTool is in PYTHONPATH.")

class C02FreqInstImpl(SignalCheckerInterface):
    _checker_title= "不稳定性检查"
    _checker_name= "C02FreqInst"
    _data_input_path = ""
    _data_data_txt_path = ""
    _data_output_path = ""
    _data_output_check_result_json_path = ""
    _data_output_check_result_detail_json_path = ""
    _data_output_check_result_table_html_path = ""
    _data_output_check_result_scatter_html_path = ""
    _data_output_check_result_hist_html_path = ""
    def init(self, input_path: str):
        """初始化传入数据路径"""
        self._data_input_path = input_path
        folder_name = os.path.basename(os.path.normpath(self._data_input_path))
        self._data_data_txt_path = os.path.join(self._data_input_path, f"AutoSdkDemo/online/vlane/{folder_name}/data.txt")
        self._data_output_path = os.path.join(self._data_input_path, "CheckResult/" + self.get_checker_name())
        self._data_output_check_result_json_path = os.path.join(self._data_output_path, "check_result.json")
        self._data_output_check_result_detail_json_path = os.path.join(self._data_output_path, "check_result_detail.json")
        self._data_output_check_result_table_html_path = os.path.join(self._data_output_path, "check_result_table.html")
        self._data_output_check_result_scatter_html_path = os.path.join(self._data_output_path, "check_result_scatter.html")
        self._data_output_check_result_hist_html_path = os.path.join(self._data_output_path, "check_result_hist.html")
        self.prepare_dirs()
        self._logger = self.setup_logger()
        self._logger.info("init")
        pass

    def check(self) -> str:
        """进行信号检查"""
        self._logger.info("check")
        prepare_result, prepare_result_msg = self.prepare_dirs()
        if not prepare_result:
            return prepare_result_msg

        # 定义信号丢帧率阈值
        image_frequency_hz = 1000.0 / 667
        signals_config = {
            "acce3d": {"frequency_hz": 10, "freq_inst_max":0.0005, "min_interval": 50, "max_interval": 150, "description": "不通过需手动排查"},
            "gyro": {"frequency_hz": 10, "freq_inst_max":0.0005, "min_interval": 50, "max_interval": 150, "description": "陀螺丢帧率"},
            "pulse": {"frequency_hz": 10, "freq_inst_max":0.0005, "min_interval": 50, "max_interval": 150, "description": "车速(轮速)丢帧率"},
            "image": {"frequency_hz": image_frequency_hz, "freq_inst_max":0.0005, "min_interval": 0.5*float(1000.0/image_frequency_hz), "max_interval": 1.5*float(1000.0/image_frequency_hz), "description": "image丢帧率"},
            # "image": {"frequency_hz": 5, "freq_inst_max":0.0005, "min_interval": 150, "max_interval": 250, "description": "image丢帧率"},
            "gnss": {"frequency_hz": 1, "freq_inst_max":0.0005, "min_interval": 500, "max_interval": 1500, "description": "gnss丢帧率"}
        }

        # 定义检查结果头
        cur_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        signals_check_result= {
            "checker": self.get_checker_name(),
            "title": f"{self._checker_title}检查",
            "datatxt": self._data_data_txt_path,
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResult"
        }
        signals_check_result_detail = {
            "checker": self.get_checker_name(),
            "title": f"{self._checker_title}检查详情",
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResultDetail"
        }
        signals_check_result_items = []
        signals_check_result_detail_items = []
        signals_check_result_columns = {"检查项":[], "统计数值":[],  "检查规则":[], "检查结果":[],"描述":[]}
        signals_check_result_detail_columns = {"检查项":[], "检查详情":[]}
        plot_sourceticktime_intervals = {}
        check_failed_sourceticktime_intervals = {}

        # 解析文件
        acce3d_data, gyro_data, pulse_data, image_data, gnss_data = self.parse_file(self._data_data_txt_path)

        # 丢帧检查
        for signal_name, config in signals_config.items():
            source_tick_times = eval(f"{signal_name}_data['sourceTickTime']")
            total_frames, unstable_count, instability_rate, sourceticktime_intervals, check_result = self.check_frequency_instability(signal_name,
                                                                                                         source_tick_times,
                                                                                                         config["min_interval"],
                                                                                                         config["max_interval"],
                                                                                                         config["freq_inst_max"])
            current_check_result = {
                "checkItem": f"{signal_name}频率不稳定性",
                "checkResult": check_result,
                "checkResultStatistic": instability_rate,
                "checkRules": f"频率不稳定率<{signals_config[signal_name]["freq_inst_max"]}",
                "description": "不通过需手动排查"
            }
            signals_check_result_items.append(current_check_result)

            signals_check_result_columns["检查项"].append(f"{signal_name}频率不稳定率")
            signals_check_result_columns["统计数值"].append(f"总帧数:{total_frames}\n 不稳定帧数:{unstable_count}\n 不稳定率%:{instability_rate*100:.3f}")
            signals_check_result_columns["检查规则"].append(f"频率不稳定率<{signals_config[signal_name]['freq_inst_max']}")
            signals_check_result_columns["检查结果"].append(check_result)
            signals_check_result_columns["描述"].append("不通过需手动排查")

            check_failed_sourceticktime_intervals[signal_name] = [
                (st, iv) for st, iv in zip(sourceticktime_intervals["sourceTicktime"],sourceticktime_intervals["intervals"]
                                           ) if iv < config["min_interval"] or iv > config["max_interval"]
            ]

            check_result_detail = {
                "checkItem": f"{signal_name}频率不稳定率",
                "total_frames": total_frames,
                "expected_frames": "",
                "frame_loss_rate": "",
            }

            signals_check_result_detail_items.append(check_result_detail)
            signals_check_result_detail_columns["检查项"].append(f"{signal_name}不稳定率")
            signals_check_result_detail_columns["检查详情"].append(f"{check_failed_sourceticktime_intervals[signal_name] }")
            plot_sourceticktime_intervals[signal_name] = sourceticktime_intervals


        # 组装完整检查结果
        signals_check_result["checkResult"] = signals_check_result_items
        signals_check_result_detail["checkResultDetail"] = signals_check_result_detail_items

        # 保存结果
        with open(self._data_output_check_result_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result, f, ensure_ascii=False, indent=4)
        with open(self._data_output_check_result_detail_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result_detail, f, ensure_ascii=False, indent=4)
        for signal_name in check_failed_sourceticktime_intervals.keys():
            signal_data = check_failed_sourceticktime_intervals[signal_name]
            if len(signal_data) <= 0:
                continue
            csv_file = os.path.join(self._data_output_path, f"{signal_name}_checkfailed.csv")
            with open(csv_file, mode='w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(["sourceTicktime", "intervals"])
                writer.writerows(signal_data)
            pass

        # 生成表格
        self.generate_table(signals_check_result_columns, signals_check_result_detail_columns, cur_date)

        # 生成曲线图
        self.generate_scatter(plot_sourceticktime_intervals)

        # 生成直方图
        self.generate_hist(plot_sourceticktime_intervals)

        # 处理完成
        return "success"


    def deinit(self):
        """销毁检查实例"""
        self._logger.info("deinit")
        pass

    def get_checker_name(self)-> str:
        """获取检查项名称"""
        return self._checker_name

    def prepare_dirs(self):
        if not os.path.exists(self._data_input_path):
            print(f"File not found: {self._data_input_path}")
            return False, "File not found"
        if not os.path.exists(self._data_output_path):
            print(f"output folder not found, try create: {self._data_output_path}")
            os.makedirs(self._data_output_path)
            if not os.path.exists(self._data_output_path):
                return False, "output folder not found, try create failed"
            else:
                print(f"output folder created: {self._data_output_path}")
                return True, ""
        else:
            print(f"output folder already exists: {self._data_output_path}")
        return True, ""

    def setup_logger(self, level=logging.INFO):
        """设置日志器，同时输出到控制台和文件"""
        date_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        log_file = os.path.join(self._data_output_path, f"{self._checker_name}_{date_time_str}.log")

        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 文件 handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)

        # 控制台 handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # 创建 logger
        logger = logging.getLogger(self._checker_name)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def parse_file(self, file_path):
        acce3d_data = {"sourceTickTime": [], "valueX": [], "valueY": [], "valueZ": []}
        gyro_data = {"sourceTickTime": [], "valueX": [], "valueY": [], "valueZ": [], "temperature": []}
        pulse_data = {"sourceTickTime": [], "speed": []}
        image_data = {"sourceTickTime": []}
        gnss_data = {"sourceTickTime": [], "lon": [], "lat": [], "alt": [], "isNS": [], "isEW": [], "speed": [],
                     "accuracy": []}

        with open(file_path, 'r') as file:
            for line in file:
                try:
                    data = json.loads(line.strip())
                    signal_type = data["value"]["type"]

                    if signal_type == "acce3d":
                        acce3d_data["sourceTickTime"].append(int(data["value"]["sourceTickTime"]))
                        acce3d_data["valueX"].append(data["value"]["acceX"])
                        acce3d_data["valueY"].append(data["value"]["acceY"])
                        acce3d_data["valueZ"].append(data["value"]["acceZ"])

                    elif signal_type == "gyro":
                        gyro_data["sourceTickTime"].append(int(data["value"]["sourceTickTime"]))
                        gyro_data["valueX"].append(data["value"]["valueX"])
                        gyro_data["valueY"].append(data["value"]["valueY"])
                        gyro_data["valueZ"].append(data["value"]["valueZ"])
                        gyro_data["temperature"].append(data["value"]["temperature"])

                    elif signal_type == "pulse":
                        pulse_data["sourceTickTime"].append(int(data["value"]["sourceTickTime"]))
                        pulse_data["speed"].append(data["value"]["speed"])

                    elif signal_type == "image":
                        image_data["sourceTickTime"].append(int(data["value"]["timestampExposure"]))

                    elif signal_type == "gnss":
                        gnss_data["sourceTickTime"].append(int(data["value"]["sourceTickTime"]))
                        gnss_data["lon"].append(data["value"]["lon"])
                        gnss_data["lat"].append(data["value"]["lat"])
                        gnss_data["alt"].append(data["value"]["alt"])
                        gnss_data["isNS"].append(data["value"]["isNS"])
                        gnss_data["isEW"].append(data["value"]["isEW"])
                        gnss_data["speed"].append(data["value"]["speed"])
                        gnss_data["accuracy"].append(data["value"]["accuracy"])

                except json.JSONDecodeError as e:
                    print(f"Error parsing line: {line}. Error: {e}")

        return acce3d_data, gyro_data, pulse_data, image_data, gnss_data

    # 丢帧检查
    def check_frame_loss(self, signal_name, source_tick_times, frequency_hz, loss_rate_max):
        total_frames = len(source_tick_times)
        expected_frames = int((max(source_tick_times) - min(source_tick_times)) / (1000 / frequency_hz)) + 1
        frame_loss_rate = (expected_frames - total_frames) / expected_frames if expected_frames > 0 else 0
        check_result = "Pass" if frame_loss_rate < loss_rate_max else "Fail"

        # 瞬时丢帧率
        instantaneous_loss = []
        for i in range(1, len(source_tick_times)):
            interval = source_tick_times[i] - source_tick_times[i - 1]
            expected_interval = 1000 / frequency_hz
            loss = max(0, interval - expected_interval) / expected_interval
            instantaneous_loss.append(loss)

        return total_frames, expected_frames, frame_loss_rate, instantaneous_loss, check_result

    # 频率不稳定检查
    def check_frequency_instability(self, signal_name, source_tick_times, min_interval, max_interval, freq_inst_max):
        intervals = [source_tick_times[i] - source_tick_times[i - 1] for i in range(1, len(source_tick_times))]
        unstable_count = sum(1 for interval in intervals if interval < min_interval or interval > max_interval)
        instability_rate = unstable_count / len(intervals) if intervals else 0
        check_result = "Pass" if instability_rate < freq_inst_max else "Fail"
        sourceticktime_intervals = {}
        sourceticktime_intervals["sourceTicktime"] = [source_tick_times[i] for i in range(2, len(source_tick_times))]
        sourceticktime_intervals["intervals"] = intervals
        return len(source_tick_times), unstable_count, instability_rate, sourceticktime_intervals, check_result

    # 构建表格样式函数
    def generate_table(self, signals_check_result_columns, signals_check_result_detail_columns, cur_date):
        # 生成表格
        cell_colors = []
        for _ in range(len(signals_check_result_columns)):
            cell_colors.append(['lavender'] * len(signals_check_result_columns["检查结果"]))  # 默认颜色

        # 对于丢帧率大于 0 的行，整行标红
        for i in range(len(signals_check_result_columns["检查结果"])):
            if signals_check_result_columns["检查结果"][i] != "Pass":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'lightcoral'
        # 绘制表格
        table_result = go.Table(
                header=dict(values=list(signals_check_result_columns.keys()), fill_color='paleturquoise',
                            align='center'),
                cells=dict(values=[signals_check_result_columns[k] for k in signals_check_result_columns],
                           fill_color=cell_colors, align='center')
        )
        table_result_title = f"{self._checker_title}检查结果"

        table_result_detail = go.Table(
                header=dict(values=list(signals_check_result_detail_columns.keys()), fill_color='paleturquoise',
                            align='center'),
                cells=dict(values=[signals_check_result_detail_columns[k] for k in signals_check_result_detail_columns],
                           fill_color=cell_colors, align='center')
        )
        table_result_detail_title = f"{self._checker_title}检查详情"
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=2,  # 三行
            cols=1,  # 一列
            specs=[[{'type': 'table'}],  # 第一行是一个表格
                   [{'type': 'table'}]],  # 第二行是一个表格
            subplot_titles=(table_result_title, table_result_detail_title),  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        # 将表格添加到对应的子图位置
        fig.add_trace(table_result, row=1, col=1)
        fig.add_trace(table_result_detail, row=2, col=1)

        # --- 更新整体布局 ---
        fig.update_layout(
            height=700,  # 可能需要调整高度以容纳所有表格
            title_text=f"<b>{self._checker_title}检查结果汇总</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50)  # 调整边距
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_table_html_path)

    def generate_scatter(self, plot_sourceticktime_intervals):
        # Prepare the figure
        fig = make_subplots(rows=len(plot_sourceticktime_intervals),
                            cols=1,
                            shared_xaxes=True,
                            subplot_titles=list(plot_sourceticktime_intervals.keys()),
                            vertical_spacing=0.1)

        for i, (signal_name, sourceticktime_intervals) in enumerate(plot_sourceticktime_intervals.items()):
            x = sourceticktime_intervals["sourceTicktime"]
            y = sourceticktime_intervals["intervals"]
            fig.add_trace(go.Scatter(x=x,
                                     y=y,
                                     name=signal_name,
                                     # mode='lines+markers',
                                     mode='lines',
                                     hoverinfo='text',
                                     text=[f"{signal_name} Point {x[j],y[j]}" for j in range(len(x))]
                                     ),
                          row=i+1, col=1)

        # 设置共享 x 轴并添加滑动条（range slider）
        # 只在最后一个子图的 x 轴添加滑动条
        for i, (name, data) in enumerate(plot_sourceticktime_intervals.items()):
            if i == len(plot_sourceticktime_intervals) - 1:
                fig.update_xaxes(
                    title_text="sourceTicktime",
                    rangeslider=dict(visible=True, thickness=0.05),  # 最后一个子图显示滑动条
                    row=i + 1
                )
            else:
                fig.update_xaxes(
                    showticklabels=True,  # 显示刻度标签
                    title_text="",  # 不显示标题
                    row=i + 1
                )
        # 设置 y 轴统一标签
        fig.update_yaxes(title_text="intervals(ms)")

        # 更新布局
        fig.update_layout(
            height=300 * len(plot_sourceticktime_intervals),  # 动态调整高度
            title_text="各模块 sourceTicktime - intervals 曲线",
            showlegend=False,
            xaxis_showspikes=True,
            yaxis_showspikes=True
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_scatter_html_path)
        pass

    def generate_hist(self, plot_sourceticktime_intervals):
        # 计算总子图数量, 每个信号占一行：直方图
        total_rows = len(plot_sourceticktime_intervals)
        fig = make_subplots(
            rows=total_rows,
            cols=1,
            subplot_titles=[
                f"{signal_name} Frequency Instability Histogram"
                for signal_name in plot_sourceticktime_intervals.keys()
            ],
            vertical_spacing=0.05
        )

        row_index = 1  # 当前绘图的行号

        for signal_name, sourceticktime_intervals in plot_sourceticktime_intervals.items():
            intervals = sourceticktime_intervals["intervals"]

            if not intervals:
                self._logger.warning(f"No interval data to plot histogram for {signal_name}")
                continue

            # 计算统计信息
            min_val = min(intervals)
            max_val = max(intervals)
            mean_val = np.mean(intervals)
            median_val = np.median(intervals)
            std_val = np.std(intervals)
            # 计算 16% 和 84% 分位数
            q16 = np.percentile(intervals, 16)
            q84 = np.percentile(intervals, 84)
            diff_q16_q84 = abs(q84 - q16)
            # 计算 2.5% 和 97.5% 分位数
            q2_5 = np.percentile(intervals, 2.5)
            q97_5 = np.percentile(intervals, 97.5)
            diff_q2_5_q97_5 = abs(q97_5 - q2_5)

            # 添加直方图
            fig.add_trace(go.Histogram(
                x=intervals,
                name=f'{signal_name} Histogram',
                xbins=dict(size=1),
                marker_color='skyblue',
                opacity=0.8
            ), row=row_index, col=1)

            # 添加统计信息文本（可选）
            stats_text = (
                f'<b>Stats</b><br>'
                f'Mean: {mean_val:.2f}<br>'
                f'Std Dev: {std_val:.2f}<br>'
                f'Median: {median_val:.2f}<br>'
                f'Q16: {q16:.2f}<br>'
                f'Q84: {q84:.2f}<br>'
                f'Diff: {diff_q16_q84:.2f}<br>'
                f'Q2.5: {q2_5:.2f}<br>'
                f'Q97.5: {q97_5:.2f}<br>'
                f'Diff: {diff_q2_5_q97_5:.2f}<br>'
                f'Min: {min_val:.2f}<br>'
                f'Max: {max_val:.2f}<br>'
            )

            # 获取当前数据对应的频数分布
            hist, bin_edges = np.histogram(intervals, bins=np.arange(min_val, max_val + 1, 1))
            y_max_count = max(hist)  # 获取最大频数值
            fig.add_annotation(
                xref="x domain", yref="y",
                x=1.05, y=y_max_count * 0.5,  # 设置在顶部附近
                text=stats_text,
                showarrow=False,
                font=dict(size=10),
                align="left",
                bordercolor="#000000",
                borderwidth=1,
                bgcolor="#FFFFFF",
                row=row_index, col=1
            )

            # 设置 Y 轴范围为 [0, y_max_count * 1.1]，留一点空间
            fig.update_yaxes(title_text="Count", range=[0, y_max_count * 1.1], row=row_index, col=1)

            row_index += 1  # 移动到下一行

        # 更新整体布局
        fig.update_layout(
            title_text="All Signals Frequency Instability Analysis",
            height=300 * total_rows,  # 动态高度
            width=1500,
            showlegend=False,
            margin=dict(t=50, b=50, l=50, r=100)
        )

        # 设置坐标轴标签
        fig.update_xaxes(title_text="Interval (ms)")

        # 保存为单个 HTML 文件
        fig.write_html(self._data_output_check_result_hist_html_path)


# Example usage (for testing this file directly)
if __name__ == "__main__":
    # 添加可选参数（带默认值）
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", "-i", help="数据根路径")
    args = parser.parse_args()

    checker = C02FreqInstImpl()
    print(f"Checker Name: {checker.get_checker_name()}")
    checker.init(args.input)
    checker.check()
    checker.deinit()

