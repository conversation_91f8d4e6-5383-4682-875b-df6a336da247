import logging
import sys
import os
import argparse
import json
from datetime import datetime
import plotly.graph_objects as go
from plotly.subplots import make_subplots

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir)) # Up two levels to signalAnalysisTool
sys.path.insert(0, parent_dir)

try:
    from SignalCheckerInterface import SignalCheckerInterface
except ImportError:
    print("Could not import SignalCheckerInterface directly. Ensure signalAnalysisTool is in PYTHONPATH.")


class C01LossRateImpl(SignalCheckerInterface):
    _checker_title= "丢帧率检查"
    _checker_name= "C01LossRate"
    _data_input_path = ""
    _data_data_txt_path = ""
    _data_output_path = ""
    _data_output_check_result_json_path = ""
    _data_output_check_result_detail_json_path = ""
    _data_output_check_result_table_html_path = ""
    def init(self, input_path: str):
        """初始化传入数据路径"""
        self._data_input_path = input_path
        folder_name = os.path.basename(os.path.normpath(self._data_input_path))
        self._data_data_txt_path = os.path.join(self._data_input_path, f"AutoSdkDemo/online/vlane/{folder_name}/data.txt")
        self._data_output_path = os.path.join(self._data_input_path, "CheckResult/" + self.get_checker_name())
        self._data_output_check_result_json_path = os.path.join(self._data_output_path, "check_result.json")
        self._data_output_check_result_detail_json_path = os.path.join(self._data_output_path, "check_result_detail.json")
        self._data_output_check_result_table_html_path = os.path.join(self._data_output_path, "check_result_table.html")
        self.prepare_dirs()
        self._logger = self.setup_logger()
        self._logger.info("init")
        pass

    def check(self) -> str:
        """进行信号检查"""
        prepare_result, prepare_result_msg = self.prepare_dirs()
        if not prepare_result:
            return prepare_result_msg

        # 定义信号丢帧率阈值
        image_frequency_hz = 1000.0 / 667
        signals_config = {
            "acce3d": {"frequency_hz": 10, "loss_rate_max":0.0005, "min_interval": 50, "max_interval": 150, "description": "不通过需手动排查"},
            "gyro": {"frequency_hz": 10, "loss_rate_max":0.0005, "min_interval": 50, "max_interval": 150, "description": "陀螺丢帧率"},
            "pulse": {"frequency_hz": 10, "loss_rate_max":0.0005, "min_interval": 50, "max_interval": 150, "description": "车速(轮速)丢帧率"},
            "image": {"frequency_hz": image_frequency_hz, "loss_rate_max":0.0005, "min_interval": 0.5*float(1000.0/image_frequency_hz), "max_interval": 1.5*float(1000.0/image_frequency_hz), "description": "image丢帧率"},
            "gnss": {"frequency_hz": 1, "loss_rate_max":0.0005, "min_interval": 500, "max_interval": 1500, "description": "gnss丢帧率"}
        }

        # 定义检查结果头
        cur_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        signals_check_result= {
            "checker": "lostRateChecker",
            "title": f"{self._checker_title}",
            "datatxt": self._data_data_txt_path,
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResult"
        }
        signals_check_result_detail = {
            "checker": "lostRateChecker",
            "title": f"{self._checker_title}详情",
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResultDetail"
        }
        signals_check_result_items = []
        signals_check_result_detail_items = []
        signals_check_result_columns = {"检查项":[], "统计数值":[],  "检查规则":[], "检查结果":[],"描述":[]}
        signals_check_result_detail_columns = {"检查项":[], "检查详情":[]}

        # 解析文件
        acce3d_data, gyro_data, pulse_data, image_data, gnss_data = self.parse_file(self._data_data_txt_path)

        # 丢帧检查
        for signal_name, config in signals_config.items():
            source_tick_times = eval(f"{signal_name}_data['sourceTickTime']")
            total_frames, expected_frames, frame_loss_rate, inst_loss, check_result = self.check_frame_loss(signal_name,
                                                                                              source_tick_times,
                                                                                             config["frequency_hz"],
                                                                                             config["loss_rate_max"])
            current_check_result = {
                "checkItem": f"{signal_name}丢帧率",
                "checkResult": check_result,
                "checkResultStatistic": frame_loss_rate,
                "checkRules": f"丢帧率<{signals_config[signal_name]["loss_rate_max"]}",
                "description": "不通过需手动排查"
            }
            signals_check_result_items.append(current_check_result)

            signals_check_result_columns["检查项"].append(f"{signal_name}丢帧率")
            signals_check_result_columns["统计数值"].append(f"总帧数:{total_frames}\n 期望帧数:{expected_frames}\n 丢帧率%:{frame_loss_rate*100:.3f}")
            signals_check_result_columns["检查规则"].append(f"丢帧率<{signals_config[signal_name]['loss_rate_max']}")
            signals_check_result_columns["检查结果"].append(check_result)
            signals_check_result_columns["描述"].append("不通过需手动排查")

            check_result_detail = {
                "checkItem": f"{signal_name}丢帧率",
                "total_frames": total_frames,
                "expected_frames": expected_frames,
                "frame_loss_rate": frame_loss_rate,
            }
            signals_check_result_detail_items.append(check_result_detail)
            signals_check_result_detail_columns["检查项"].append(f"{signal_name}丢帧率")
            signals_check_result_detail_columns["检查详情"].append(f"总帧数:{total_frames}\n 期望帧数:{expected_frames}\n 丢帧率%:{frame_loss_rate}")

        # 组装完整检查结果
        signals_check_result["checkResult"] = signals_check_result_items
        signals_check_result_detail["checkResultDetail"] = signals_check_result_detail_items

        # 保存结果
        with open(self._data_output_check_result_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result, f, ensure_ascii=False, indent=4)
        with open(self._data_output_check_result_detail_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result_detail, f, ensure_ascii=False, indent=4)

        # 生成表格
        self.generate_table(signals_check_result_columns, signals_check_result_detail_columns, cur_date)

        # 处理完成
        return "success"


    def deinit(self):
        """销毁检查实例"""
        pass

    def get_checker_name(self)-> str:
        """获取检查项名称"""
        return "C01LossRate"

    def prepare_dirs(self):
        if not os.path.exists(self._data_input_path):
            print(f"File not found: {self._data_input_path}")
            return False, "File not found"
        if not os.path.exists(self._data_output_path):
            print(f"output folder not found, try create: {self._data_output_path}")
            os.makedirs(self._data_output_path)
            if not os.path.exists(self._data_output_path):
                return False, "output folder not found, try create failed"
            else:
                print(f"output folder created: {self._data_output_path}")
                return True, ""
        else:
            print(f"output folder already exists: {self._data_output_path}")
        return True, ""

    def setup_logger(self, level=logging.INFO):
        """设置日志器，同时输出到控制台和文件"""
        date_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        log_file = os.path.join(self._data_output_path, f"{self._checker_name}_{date_time_str}.log")

        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 文件 handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)

        # 控制台 handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # 创建 logger
        logger = logging.getLogger(self._checker_name)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def parse_file(self, file_path):
        acce3d_data = {"sourceTickTime": [], "valueX": [], "valueY": [], "valueZ": []}
        gyro_data = {"sourceTickTime": [], "valueX": [], "valueY": [], "valueZ": [], "temperature": []}
        pulse_data = {"sourceTickTime": [], "speed": []}
        image_data = {"sourceTickTime": []}
        gnss_data = {"sourceTickTime": [], "lon": [], "lat": [], "alt": [], "isNS": [], "isEW": [], "speed": [],
                     "accuracy": []}

        with open(file_path, 'r') as file:
            for line in file:
                try:
                    data = json.loads(line.strip())
                    signal_type = data["value"]["type"]

                    if signal_type == "acce3d":
                        acce3d_data["sourceTickTime"].append(int(data["value"]["sourceTickTime"]))
                        acce3d_data["valueX"].append(data["value"]["acceX"])
                        acce3d_data["valueY"].append(data["value"]["acceY"])
                        acce3d_data["valueZ"].append(data["value"]["acceZ"])

                    elif signal_type == "gyro":
                        gyro_data["sourceTickTime"].append(int(data["value"]["sourceTickTime"]))
                        gyro_data["valueX"].append(data["value"]["valueX"])
                        gyro_data["valueY"].append(data["value"]["valueY"])
                        gyro_data["valueZ"].append(data["value"]["valueZ"])
                        gyro_data["temperature"].append(data["value"]["temperature"])

                    elif signal_type == "pulse":
                        pulse_data["sourceTickTime"].append(int(data["value"]["sourceTickTime"]))
                        pulse_data["speed"].append(data["value"]["speed"])

                    elif signal_type == "image":
                        image_data["sourceTickTime"].append(int(data["value"]["timestampExposure"]))

                    elif signal_type == "gnss":
                        gnss_data["sourceTickTime"].append(int(data["value"]["sourceTickTime"]))
                        gnss_data["lon"].append(data["value"]["lon"])
                        gnss_data["lat"].append(data["value"]["lat"])
                        gnss_data["alt"].append(data["value"]["alt"])
                        gnss_data["isNS"].append(data["value"]["isNS"])
                        gnss_data["isEW"].append(data["value"]["isEW"])
                        gnss_data["speed"].append(data["value"]["speed"])
                        gnss_data["accuracy"].append(data["value"]["accuracy"])

                except json.JSONDecodeError as e:
                    print(f"Error parsing line: {line}. Error: {e}")

        return acce3d_data, gyro_data, pulse_data, image_data, gnss_data

    # 丢帧检查
    def check_frame_loss(self, signal_name, source_tick_times, frequency_hz, loss_rate_max):
        total_frames = len(source_tick_times)
        if total_frames > 0:
            expected_frames = int((max(source_tick_times) - min(source_tick_times)) / (1000 / frequency_hz)) + 1
            print(f"{signal_name , max(source_tick_times) - min(source_tick_times)}")
        else:
            expected_frames = 0
            print(f"{signal_name , 0}")
        frame_loss_rate = (expected_frames - total_frames) / expected_frames if expected_frames > 0 else 0
        check_result = "Pass" if frame_loss_rate < loss_rate_max else "Fail"

        # 瞬时丢帧率
        instantaneous_loss = []
        for i in range(1, len(source_tick_times)):
            interval = source_tick_times[i] - source_tick_times[i - 1]
            expected_interval = 1000 / frequency_hz
            loss = max(0, interval - expected_interval) / expected_interval
            instantaneous_loss.append(loss)

        return total_frames, expected_frames, frame_loss_rate, instantaneous_loss, check_result


    # 构建表格样式函数
    def generate_table(self, signals_check_result_columns, signals_check_result_detail_columns, cur_date):
        # 生成表格
        cell_colors = []
        for _ in range(len(signals_check_result_columns)):
            cell_colors.append(['lavender'] * len(signals_check_result_columns["检查结果"]))  # 默认颜色

        # 对于丢帧率大于 0 的行，整行标红
        for i in range(len(signals_check_result_columns["检查结果"])):
            if signals_check_result_columns["检查结果"][i] != "Pass":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'lightcoral'
        # 绘制表格
        table_result = go.Table(
                header=dict(values=list(signals_check_result_columns.keys()), fill_color='paleturquoise',
                            align='center'),
                cells=dict(values=[signals_check_result_columns[k] for k in signals_check_result_columns],
                           fill_color=cell_colors, align='center')
        )
        table_result_title = f"{self._checker_title}结果"

        table_result_detail = go.Table(
                header=dict(values=list(signals_check_result_detail_columns.keys()), fill_color='paleturquoise',
                            align='center'),
                cells=dict(values=[signals_check_result_detail_columns[k] for k in signals_check_result_detail_columns],
                           fill_color=cell_colors, align='center')
        )
        table_result_detail_title = f"{self._checker_title}详情"
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=2,  # 三行
            cols=1,  # 一列
            specs=[[{'type': 'table'}],  # 第一行是一个表格
                   [{'type': 'table'}]],  # 第二行是一个表格
            subplot_titles=(table_result_title, table_result_detail_title),  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        # 将表格添加到对应的子图位置
        fig.add_trace(table_result, row=1, col=1)
        fig.add_trace(table_result_detail, row=2, col=1)

        # --- 更新整体布局 ---
        fig.update_layout(
            height=700,  # 可能需要调整高度以容纳所有表格
            title_text=f"<b>{self._checker_title}结果汇总</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50)  # 调整边距
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_table_html_path)


# Example usage (for testing this file directly)
if __name__ == "__main__":
    # 添加可选参数（带默认值）
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", "-i", help="数据根路径")
    args = parser.parse_args()

    checker = C01LossRateImpl()
    print(f"Checker Name: {checker.get_checker_name()}")
    checker.init(args.input)
    checker.check()
    checker.deinit()
