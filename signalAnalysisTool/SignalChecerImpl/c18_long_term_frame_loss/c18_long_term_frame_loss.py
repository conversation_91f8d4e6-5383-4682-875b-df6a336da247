import csv
import logging
import sys
import os
import argparse
import json
from datetime import datetime
import plotly.graph_objects as go
from plotly.subplots import make_subplots

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir)) # Up two levels to signalAnalysisTool
sys.path.insert(0, parent_dir)

try:
    from SignalCheckerInterface import SignalCheckerInterface
except ImportError:
    print("Could not import SignalCheckerInterface directly. Ensure signalAnalysisTool is in PYTHONPATH.")

class C18LongTermFrameLossImpl(SignalCheckerInterface):
    _checker_title= "丢帧时长检查"
    _checker_name= "C18LongTermFrameLoss"
    _data_input_path = ""
    _data_data_txt_path = ""
    _data_output_path = ""
    _data_output_check_result_json_path = ""
    _data_output_check_result_detail_json_path = ""
    _data_output_check_result_table_html_path = ""
    def init(self, input_path: str):
        """初始化传入数据路径"""
        self._data_input_path = input_path
        folder_name = os.path.basename(os.path.normpath(self._data_input_path))
        self._data_data_txt_path = os.path.join(self._data_input_path, f"AutoSdkDemo/online/vlane/{folder_name}/data.txt")
        self._data_output_path = os.path.join(self._data_input_path, "CheckResult/" + self.get_checker_name())
        self._data_output_check_result_json_path = os.path.join(self._data_output_path, "check_result.json")
        self._data_output_check_result_detail_json_path = os.path.join(self._data_output_path, "check_result_detail.json")
        self._data_output_check_result_table_html_path = os.path.join(self._data_output_path, "check_result_table.html")
        self.prepare_dirs()
        self._logger = self.setup_logger()
        self._logger.info("init")
        pass

    def check(self) -> str:
        """进行信号检查"""
        prepare_result, prepare_result_msg = self.prepare_dirs()
        if not prepare_result:
            return prepare_result_msg

        # 定义信号丢时长阈值
        signals_config = {
            "acce3d": {"loss_rate_time_max":10, "sourceTimeField":"sourceTickTime", "XField":"acceX", "YField":"acceY", "description": "加速度计丢帧时长"},
            "gyro": {"loss_rate_time_max":10, "sourceTimeField":"sourceTickTime", "XField":"valueX", "YField":"valueY", "description": "陀螺丢帧时长"},
            "pulse": {"loss_rate_time_max":10, "sourceTimeField":"sourceTickTime", "XField":"", "YField":"", "description": "轮速丢帧时长"},
            "image": {"loss_rate_time_max":2, "sourceTimeField":"timestampExposure", "XField":"", "YField":"", "description": "image丢帧时长"}
        }

        # 定义检查结果头
        cur_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        signals_check_result= {
            "checker": "longTermFrameLossChecker",
            "title": f"{self._checker_title}",
            "datatxt": self._data_data_txt_path,
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResult"
        }
        signals_check_result_detail = {
            "checker": "longTermFrameLossChecker",
            "title": f"{self._checker_title}详情",
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResultDetail"
        }
        signals_check_result_items = []
        signals_check_result_detail_items = []
        signals_check_result_columns = {"检查项":[], "统计数值":[],  "检查规则":[], "检查结果":[],"描述":[]}
        signals_check_result_detail_columns = {"检查项":[], "检查详情":[]}
        check_failed_sourceticktime_intervals = {}

        # 解析文件
        acce3d_data, gyro_data, pulse_data, image_data, frist_signal_time = self.parse_file(self._data_data_txt_path)

        # 丢帧检查
        for signal_name, config in signals_config.items():
            signal_datas = eval(f"{signal_name}_data")
            check_result, max_delay_info, exception_info = self.check_frame_loss(signal_name, signal_datas, config, frist_signal_time)
            current_check_result = {
                "checkItem": f"{signal_name}长时丢帧",
                "checkResult": check_result,
                "checkResultStatistic": max_delay_info["delay_time"],
                "checkRules": f"最大丢帧时长小于<{config["loss_rate_time_max"]}秒",
                "description": config["description"]
            }
            signals_check_result_items.append(current_check_result)

            signals_check_result_columns["检查项"].append(f"{signal_name}长时丢帧")
            if signal_name == "image" or signal_name == "pulse":
                signals_check_result_columns["统计数值"].append(f"最大丢帧时长:{max_delay_info["delay_time"]}秒\n TickTime时刻:{max_delay_info["tick_time"]}")
            else:
                signals_check_result_columns["统计数值"].append(f"最大丢帧时长:{max_delay_info["delay_time"]}秒\n TickTime时刻:{max_delay_info["tick_time"]}\n 经度:{max_delay_info["x"]}\n 纬度:{max_delay_info["y"]}")
            signals_check_result_columns["检查规则"].append(f"最大丢帧时长小于<{config['loss_rate_time_max']}秒")
            signals_check_result_columns["检查结果"].append(check_result)
            description = config["description"] if check_result == "Pass" else "不通过需手动排查"
            signals_check_result_columns["描述"].append(description)

            print(f"{signal_name} exception_info len : {len(exception_info)}")
            if len(exception_info) > 0:
                abnormal_data = []
                print(f"exception_info: {exception_info}")
                abnormal_data.append(exception_info)
                # for exception_data in exception_info:
                #     item = (exception_data["tick_time"], exception_data["x"], exception_data["y"], exception_data["delay_time"])
                #     abnormal_data.append(item)
                check_failed_sourceticktime_intervals[signal_name] = abnormal_data
            else:
                check_failed_sourceticktime_intervals[signal_name] = [""]

            check_result_detail = {
                "checkItem": f"{signal_name}长时丢帧",
                "total_frames": max_delay_info["delay_time"],
                "expected_frames": exception_info,
                "frame_loss_rate": "",
            }
            signals_check_result_detail_items.append(check_result_detail)
            signals_check_result_detail_columns["检查项"].append(f"{signal_name}长时丢帧")
            signals_check_result_detail_columns["检查详情"].append(check_failed_sourceticktime_intervals[signal_name])

        # 组装完整检查结果
        signals_check_result["checkResult"] = signals_check_result_items
        signals_check_result_detail["checkResultDetail"] = signals_check_result_detail_items

        # 保存结果
        with open(self._data_output_check_result_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result, f, ensure_ascii=False, indent=4)
        with open(self._data_output_check_result_detail_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result_detail, f, ensure_ascii=False, indent=4)
        for signal_name in check_failed_sourceticktime_intervals.keys():
            signal_data = check_failed_sourceticktime_intervals[signal_name]
            if len(signal_data) <= 0:
                continue
            csv_file = os.path.join(self._data_output_path, f"{signal_name}_checkfailed.csv")
            with open(csv_file, mode='w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(["tick_time", "x", "y", "delay_time"])
                writer.writerows(signal_data)
            pass

        # 生成表格
        self.generate_table(signals_check_result_columns, signals_check_result_detail_columns, cur_date)

        # 处理完成
        return "success"


    def deinit(self):
        """销毁检查实例"""
        pass

    def get_checker_name(self)-> str:
        """获取检查项名称"""
        return "C18LongTermFrameLoss"

    def prepare_dirs(self):
        if not os.path.exists(self._data_input_path):
            print(f"File not found: {self._data_input_path}")
            return False, "File not found"
        if not os.path.exists(self._data_output_path):
            print(f"output folder not found, try create: {self._data_output_path}")
            os.makedirs(self._data_output_path)
            if not os.path.exists(self._data_output_path):
                return False, "output folder not found, try create failed"
            else:
                print(f"output folder created: {self._data_output_path}")
                return True, ""
        else:
            print(f"output folder already exists: {self._data_output_path}")
        return True, ""

    def setup_logger(self, level=logging.INFO):
        """设置日志器，同时输出到控制台和文件"""
        date_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        log_file = os.path.join(self._data_output_path, f"{self._checker_name}_{date_time_str}.log")

        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 文件 handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)

        # 控制台 handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # 创建 logger
        logger = logging.getLogger(self._checker_name)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    def parse_file(self, file_path):
        acce3d_data = []
        gyro_data = []
        pulse_data = []
        image_data = []
        first_signal_time = None
        last_line = None

        with open(file_path, 'r') as file:
            for line in file:
                try:
                    data = json.loads(line.strip())
                    signal_value = data["value"]
                    signal_type = signal_value["type"]

                    # 获取到第一个信号的时间 和最后一个信号的数据
                    if signal_type  in ["acce3d", "gyro", "pulse"]:
                        if first_signal_time is None:
                            first_signal_time = int(signal_value.get("sourceTickTime"))
                        else:
                            last_line = signal_value
                    elif signal_type == "image":
                        if first_signal_time is None:
                            first_signal_time = int(signal_value.get("timestampExposure"))
                        else:
                            last_line = signal_value

                    if signal_type == "acce3d":
                        acce3d_data.append(signal_value)

                    elif signal_type == "gyro":
                        gyro_data.append(signal_value)

                    elif signal_type == "pulse":
                        pulse_data.append(signal_value)

                    elif signal_type == "image":
                        image_data.append(signal_value)

                except json.JSONDecodeError as e:
                    self._logger.error(f"Error parsing line: {line}. Error: {e}")

        self.set_last_signal_time(last_line, acce3d_data, gyro_data, pulse_data, image_data)
        

        return acce3d_data, gyro_data, pulse_data, image_data, first_signal_time

    def set_last_signal_time(self, last_line, acce3d_data, gyro_data, pulse_data, image_data):
        if last_line is not None:
            signal_type = last_line.get("type")
            if signal_type == "acce3d":
                source_tick_time = last_line.get("sourceTickTime")
                if len(gyro_data) > 0:
                    signal_gyro_data = gyro_data[-1]
                    copy_signal_gyro_data  = signal_gyro_data.copy()
                    copy_signal_gyro_data["sourceTickTime"] = source_tick_time
                    gyro_data.append(copy_signal_gyro_data)

                if len(pulse_data) > 0:
                    signal_pulse_data = pulse_data[-1]
                    copy_signal_pulse_data  = signal_pulse_data.copy()
                    copy_signal_pulse_data["sourceTickTime"] = source_tick_time
                    pulse_data.append(copy_signal_pulse_data)

                if len(image_data) > 0:
                    signal_image_data = image_data[-1]
                    copy_signal_image_data  = signal_image_data.copy()
                    copy_signal_image_data["timestampExposure"] = source_tick_time
                    image_data.append(copy_signal_image_data)

            elif signal_type == "gyro":
                source_tick_time = last_line.get("sourceTickTime")
                if len(acce3d_data) > 0:
                    signal_acce3d_data = acce3d_data[-1]
                    copy_signal_acce3d_data  = signal_acce3d_data.copy()
                    copy_signal_acce3d_data["sourceTickTime"] = source_tick_time
                    gyro_data.append(copy_signal_acce3d_data)

                if len(pulse_data) > 0:
                    signal_pulse_data = pulse_data[-1]
                    copy_signal_pulse_data  = signal_pulse_data.copy()
                    copy_signal_pulse_data["sourceTickTime"] = source_tick_time
                    pulse_data.append(copy_signal_pulse_data)

                if len(image_data) > 0:
                    signal_image_data = image_data[-1]
                    copy_signal_image_data  = signal_image_data.copy()
                    copy_signal_image_data["timestampExposure"] = source_tick_time
                    image_data.append(copy_signal_image_data)
            elif signal_type == "pulse":
                source_tick_time = last_line.get("sourceTickTime")
                if len(acce3d_data) > 0:
                    signal_acce3d_data = acce3d_data[-1]
                    copy_signal_acce3d_data  = signal_acce3d_data.copy()
                    copy_signal_acce3d_data["sourceTickTime"] = source_tick_time
                    gyro_data.append(copy_signal_acce3d_data)

                if len(gyro_data) > 0:
                    signal_gyro_data = gyro_data[-1]
                    copy_signal_gyro_data  = signal_gyro_data.copy()
                    copy_signal_gyro_data["sourceTickTime"] = source_tick_time
                    gyro_data.append(copy_signal_gyro_data)

                if len(image_data) > 0:
                    signal_image_data = image_data[-1]
                    copy_signal_image_data  = signal_image_data.copy()
                    copy_signal_image_data["timestampExposure"] = source_tick_time
                    image_data.append(copy_signal_image_data)
            elif signal_type == "image":
                timestamp_exposure = last_line.get("timestampExposure")
                if len(acce3d_data) > 0:
                    signal_acce3d_data = acce3d_data[-1]
                    copy_signal_acce3d_data  = signal_acce3d_data.copy()
                    copy_signal_acce3d_data["sourceTickTime"] = timestamp_exposure
                    gyro_data.append(copy_signal_acce3d_data)

                if len(gyro_data) > 0:
                    signal_gyro_data = gyro_data[-1]
                    copy_signal_gyro_data  = signal_gyro_data.copy()
                    copy_signal_gyro_data["sourceTickTime"] = timestamp_exposure
                    gyro_data.append(copy_signal_gyro_data)

                if len(pulse_data) > 0:
                    signal_pulse_data = pulse_data[-1]
                    copy_signal_pulse_data  = signal_pulse_data.copy()
                    copy_signal_pulse_data["sourceTickTime"] = timestamp_exposure
                    pulse_data.append(copy_signal_pulse_data)
                


    # 丢帧检查
    def check_frame_loss(self, signal_name, signal_datas, config, frist_signal_time):
        check_result = "Pass"
        max_delay_time = 0
        max_delay_info = {}
        source_time_field = config["sourceTimeField"]
        x_field = config["XField"]
        y_field = config["YField"]
        delay_max = config["loss_rate_time_max"]
        global_tick_time = frist_signal_time
        exception_info = {}
        for signal_data in signal_datas:
            tick_time = int(signal_data[source_time_field])
            if global_tick_time is not None:
                delay_time = (tick_time - global_tick_time) / 1000
                # 记录最大丢帧时长
                if delay_time > max_delay_time:
                    max_delay_time = delay_time
                    max_delay_info["delay_time"] = delay_time
                    max_delay_info["tick_time"] = tick_time

                    signal_x_field = signal_data.get(x_field)
                    signal_y_field = signal_data.get(y_field)
                    if x_field != "" and signal_x_field is not None and y_field != "" and signal_y_field is not None:
                        max_delay_info["x"] = signal_x_field
                        max_delay_info["y"] = signal_y_field
                # 写入检查不通过的case
                if delay_time > delay_max:
                    exception_info["delay_time"] = delay_time
                    exception_info["tick_time"] = tick_time

                    signal_exception_info_x = signal_data.get(x_field)
                    signal_exception_info_y = signal_data.get(y_field)
                    if x_field != "" and  signal_exception_info_x is not None and y_field != "" and signal_exception_info_y is not None:
                        exception_info["x"] = signal_exception_info_x
                        exception_info["y"] = signal_exception_info_y
            global_tick_time = tick_time
        if len(exception_info) > 0:
            check_result = "Fail"
        return check_result, max_delay_info, exception_info


    # 构建表格样式函数
    def generate_table(self, signals_check_result_columns, signals_check_result_detail_columns, cur_date):
        # 生成表格
        cell_colors = []
        for _ in range(len(signals_check_result_columns)):
            cell_colors.append(['lavender'] * len(signals_check_result_columns["检查结果"]))  # 默认颜色

        # 对于丢帧率大于 0 的行，整行标红
        for i in range(len(signals_check_result_columns["检查结果"])):
            if signals_check_result_columns["检查结果"][i] != "Pass":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'lightcoral'
        # 绘制表格
        table_result = go.Table(
                header=dict(values=list(signals_check_result_columns.keys()), fill_color='paleturquoise',
                            align='center'),
                cells=dict(values=[signals_check_result_columns[k] for k in signals_check_result_columns],
                           fill_color=cell_colors, align='center')
        )
        table_result_title = f"{self._checker_title}结果"

        table_result_detail = go.Table(
                header=dict(values=list(signals_check_result_detail_columns.keys()), fill_color='paleturquoise',
                            align='center'),
                cells=dict(values=[signals_check_result_detail_columns[k] for k in signals_check_result_detail_columns],
                           fill_color=cell_colors, align='center')
        )
        table_result_detail_title = f"{self._checker_title}详情"
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=2,  # 三行
            cols=1,  # 一列
            specs=[[{'type': 'table'}],  # 第一行是一个表格
                   [{'type': 'table'}]],  # 第二行是一个表格
            subplot_titles=(table_result_title, table_result_detail_title),  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        # 将表格添加到对应的子图位置
        fig.add_trace(table_result, row=1, col=1)
        fig.add_trace(table_result_detail, row=2, col=1)

        # --- 更新整体布局 ---
        fig.update_layout(
            height=700,  # 可能需要调整高度以容纳所有表格
            title_text=f"<b>{self._checker_title}结果汇总</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50)  # 调整边距
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_table_html_path)


# Example usage (for testing this file directly)
if __name__ == "__main__":
    # 添加可选参数（带默认值）
    default_input_path = "/Users/<USER>/Downloads/sourceCode/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_154816"  # 替换为你想设置的默认路径

    parser = argparse.ArgumentParser()
    parser.add_argument("--input", "-i", help="数据根路径", default=default_input_path)
    args = parser.parse_args()

    checker = C18LongTermFrameLossImpl()
    print(f"Checker Name: {checker.get_checker_name()}")
    checker.init(args.input)
    checker.check()
    checker.deinit()
