import logging
import sys
import os
import argparse
import json
from datetime import datetime
import numpy as np
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# 首先设置 sys.path，然后再进行模块导入
current_dir = os.path.dirname(os.path.abspath(__file__))
# 需要添加项目根目录到 sys.path，即包含 signalAnalysisTool 的目录
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))  # Up three levels to project root
sys.path.insert(0, project_root)

from signalAnalysisTool.SignalChecerImpl.c11_gnss_accuracy.gnss_alignment_domain import Gnss
from signalAnalysisTool.SignalChecerImpl.c11_gnss_accuracy.sorted_dict_operator import SortedDictOperator
from signalAnalysisTool.SignalChecerImpl.c13_gyro_accuracy.gyro_alignment_domain import Gyro, AlignmentGyroRtkGnss, \
    CheckReport, LocGtRtk, TrueAngularVelocity

try:
    from signalAnalysisTool.SignalCheckerInterface import SignalCheckerInterface
except ImportError:
    print("Could not import signalAnalysisTool.SignalCheckerInterface directly. Ensure signalAnalysisTool is in PYTHONPATH.")


class C13GyroAccuracy(SignalCheckerInterface):
    _checker_title = "陀螺仪精度检查"
    _checker_name = "C13GyroAccuracy"
    _data_input_path = ""
    _data_data_txt_path = ""
    _data_output_path = ""
    _data_output_check_result_json_path = ""
    _data_output_check_result_detail_json_path = ""
    _data_output_check_result_table_html_path = ""

    def init(self, input_path: str):
        """初始化传入数据路径"""
        self._data_input_path = input_path
        folder_name = os.path.basename(os.path.normpath(self._data_input_path))
        self._data_data_txt_path = os.path.join(self._data_input_path, f"AutoSdkDemo/online/vlane/{folder_name}/data.txt")
        self._summary_txt_path = os.path.join(self._data_input_path, f"AutoSdkDemo/online/vlane/summary.txt")

        gt_folder = self.find_gt_folder()
        print(f"==========gt_folder: {gt_folder}")
        self._loc_gt_txt_path = os.path.join(self._data_input_path, f"{gt_folder}/loc_gt.txt") if gt_folder else None

        self._data_output_path = os.path.join(self._data_input_path, "CheckResult/" + self.get_checker_name())
        self._data_output_check_result_json_path = os.path.join(self._data_output_path, "check_result.json")
        self._data_output_check_result_detail_json_path = os.path.join(self._data_output_path, "check_result_detail.json")
        self._data_output_check_result_table_html_path = os.path.join(self._data_output_path, "check_result_table.html")
        self._data_output_check_result_histogram_html_path = os.path.join(self._data_output_path, "check_result_histogram.html")
        self._data_output_check_result_scatter_html_path = os.path.join(self._data_output_path, "check_result_scatter.html")
        self._data_output_error_out_of_limit_xlsx_path = os.path.join(self._data_output_path, "error_out_of_limit.xlsx")
        self.prepare_dirs()
        self._logger = self.setup_logger()
        self._logger.info("init")
        pass


    def check(self) -> str:
        """进行信号检查"""
        prepare_result, prepare_result_msg = self.prepare_dirs()
        if not prepare_result:
            return prepare_result_msg

        # 定义检查结果头
        cur_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        signals_check_result = {
            "checker": "C13GyroAccuracy",
            "title": f"{self._checker_title}",
            "datatxt": self._data_data_txt_path,
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResult"
        }
        signals_check_result_detail = {
            "checker": "C13GyroAccuracy",
            "title": f"{self._checker_title}详情",
            "version": "1.0",
            "date": cur_date,
            "type": "CheckResultDetail"
        }
        signals_check_result_items = []
        signals_check_result_detail_items = []
        signals_check_result_columns = {"检查项": [], "统计数值": [], "检查规则": [], "检查结果": [], "描述": []}
        signals_check_result_detail_columns = {"检查项": [], "检查详情": []}

        imuexteuler_matrix = self.get_imuexteuler_matrix_from_summary(self._summary_txt_path)
        if imuexteuler_matrix is None:
            return "failed, imuexteuler is None"

        measurement_value_list = self.calc_measurement_value_list(self._data_data_txt_path, imuexteuler_matrix)
        is_rtk_true_value, true_value_sorted_dict = self.calc_true_value_list(self._loc_gt_txt_path, self._data_data_txt_path)

        # 数据对齐
        alignment_data_list, unmatched_gnss_data_list = self.alignment_data(measurement_value_list, true_value_sorted_dict)
        if len(alignment_data_list) == 0:
            return "failed, no alignment data"

        # 误差超限点列表
        error_out_of_limit_list = [item for item in alignment_data_list if item.course_diff > 0.5]

        # 误差超限点分组
        continuous_error_out_of_limit_groups = self.build_continuous_error_out_of_limit_groups(alignment_data_list)
        # 连续误差超限的时间区间，输出最长区间的时间长度（误差超限最长时间）
        max_source_tick_time_diff = self.build_max_source_tick_time_diff(continuous_error_out_of_limit_groups)

        course_diff_list = [item.course_diff for item in alignment_data_list]
        course_diff_mean = np.mean(course_diff_list)
        course_diff_median = np.median(course_diff_list)

        # 误差超限比例
        error_out_of_limit_percent = len(error_out_of_limit_list) / len(alignment_data_list)

        # 误差超限区间内角速度误差积分小于4.5deg, 使用梯形法则计算积分
        integral_list = []
        for group_id, group_alignment_list in continuous_error_out_of_limit_groups.items():
            error_array = [item.course_diff for item in group_alignment_list]
            second_time_array = [item.source_tick_time/1000 for item in group_alignment_list]
            integral = np.trapz(error_array, x=second_time_array)
            integral_list.append(integral)

        gyro_accuracy_check_result = self.calc_gyro_accuracy_check_result(course_diff_mean, course_diff_median,
                                                                          integral_list, is_rtk_true_value,
                                                                          error_out_of_limit_percent,
                                                                          max_source_tick_time_diff,
                                                                          alignment_data_list)
        check_report_list = [gyro_accuracy_check_result]

        for item in check_report_list:
            current_check_result = {
                "checkItem": item.check_item,
                "checkResult": item.check_result,
                "checkValue": item.check_value,
                "checkRules": item.check_rules,
                "description": item.description
            }
            signals_check_result_items.append(current_check_result)

            signals_check_result_columns["检查项"].append(item.check_item)
            signals_check_result_columns["检查结果"].append(item.check_result)
            signals_check_result_columns["统计数值"].append(item.check_value)
            signals_check_result_columns["检查规则"].append(item.check_rules)
            signals_check_result_columns["描述"].append(item.description)

        # 组装完整检查结果
        signals_check_result["checkResult"] = signals_check_result_items
        signals_check_result_detail["checkResultDetail"] = signals_check_result_detail_items

        # 保存结果
        with open(self._data_output_check_result_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result, f, ensure_ascii=False, indent=4)
        with open(self._data_output_check_result_detail_json_path, "w", encoding="utf-8") as f:
            json.dump(signals_check_result_detail, f, ensure_ascii=False, indent=4)

        # 创建一个 Excel writer 对象
        with pd.ExcelWriter(self._data_output_error_out_of_limit_xlsx_path, engine='openpyxl') as writer:
            column_names = ["group_index", "source_tick_time", "gyro_matrixZ", "angular_velocity", "course_diff"]

            rows = []
            for index, group in continuous_error_out_of_limit_groups.items():
                for frame in group:  # 直接遍历每一帧
                    row_data = [str(index)]
                    for column_name in column_names:
                        if "group_index" != column_name:
                            row_data.append(getattr(frame, column_name))
                    rows.append(row_data)  # 添加一行数据

            # 转换为 DataFrame
            if rows:  # 确保有数据
                df = pd.DataFrame(rows, columns=column_names)
                # 写入到对应的 sheet
                df.to_excel(writer, sheet_name="sheet1", index=False)

        # 生成表格
        self.generate_table(signals_check_result_columns, signals_check_result_detail_columns, cur_date, is_rtk_true_value)
        self.generate_histogram(check_report_list, cur_date, is_rtk_true_value)

        # 处理完成
        return "success"

    '''
    1. 天向角速度误差均值和中位数都小于1dps
    2. 误差超限点比例小于千分之五（使用RTK真值计算, 仅当存在RTK真值时才检查）
    3. 误差超限区间内角速度误差积分小于4.5deg
    '''
    def calc_gyro_accuracy_check_result(self, course_diff_mean, course_diff_median, integral_list, is_rtk_true_value,
                                        error_out_of_limit_percent, max_source_tick_time_diff, alignment_data_list):
        success = self.hit_range(course_diff_mean, 1) and self.hit_range(course_diff_median, 1)

        if is_rtk_true_value:
            success = success and self.hit_range(error_out_of_limit_percent, 0.005)

        max_integral = max(integral_list)
        if len(integral_list) > 0:
            for integral in integral_list:
                success = success and self.hit_range(integral, 4.5)

        check_report = CheckReport()
        check_report.set_check_item("陀螺精度")
        check_report.set_check_result("Pass" if success else "Failed")
        check_report.set_check_value(f"mean:{course_diff_mean}<br>median:{course_diff_median}<br>误差超限区间内角速度误差积分:{max_integral}<br>误差超限点比例:{error_out_of_limit_percent}<br>连续误差超限的最长时间(ms):{max_source_tick_time_diff}")
        check_report.set_check_rules("天向角速度误差均值和中位数都小于1dps<br>误差超限区间内角速度误差积分小于4.5deg<br>误差超限点比例小于千分之五（使用RTK真值计算, 仅当存在RTK真值时才检查）")
        check_report.set_description("不通过需手动检查")
        check_report.set_source_tick_time_list([item.source_tick_time for item in alignment_data_list])
        check_report.set_data_list([item.course_diff for item in alignment_data_list])
        return check_report

    def build_max_source_tick_time_diff(self, continuous_error_out_of_limit_groups):
        max_source_tick_time_diff = 0

        for group_id, value in continuous_error_out_of_limit_groups.items():
            source_tick_time_list = [item.source_tick_time for item in value]
            min_source_tick_time = min(source_tick_time_list)
            max_source_tick_time = max(source_tick_time_list)
            diff_source_tick_time = max_source_tick_time - min_source_tick_time
            abs_diff_source_tick_time = abs(diff_source_tick_time)
            max_source_tick_time_diff = max(max_source_tick_time_diff, abs_diff_source_tick_time)

        return max_source_tick_time_diff

    # 对误差超限数据分组
    def build_continuous_error_out_of_limit_groups(self, alignment_data_list):
        groups = {}
        group_index = 0
        current_group = []

        for item in alignment_data_list:
            is_out_of_limit = item.course_diff > 0.5
            # 如果当前元素是目标值，加入当前组
            if is_out_of_limit:
                current_group.append(item)
            else:
                # 如果当前元素不是目标值，且当前组不为空，保存当前组
                if len(current_group) > 0:
                    groups[group_index] = current_group
                    group_index += 1
                    current_group = []

        # 处理数组末尾的连续元素
        if len(current_group) > 0:
            groups[group_index] = current_group
            group_index += 1

        return groups

    def hit_range(self, target, boundary):
        return -boundary <= target <= boundary

    def find_gt_folder(self):
        # 获取所有子文件夹
        subfolders = [f for f in os.listdir(self._data_input_path)]
        # 过滤出符合条件的文件夹
        gt_folders = [f for f in subfolders if f.startswith('GT_') and f.endswith('_plaintext')]
        # 强制校验
        assert len(gt_folders) <= 1, f"Found {len(gt_folders)} GT folders, expected 0 or 1"
        return gt_folders[0] if len(gt_folders) == 1 else None


    def alignment_data(self, gyro_measurement_list, true_sorted_dict):
        gyro_sourceticktimes = []
        for gyro in gyro_measurement_list:
            gyro_source_tick_nano_time = gyro.sourceTickTime * 1000 * 1000
            gyro_sourceticktimes.append(gyro_source_tick_nano_time)

        gyro_sourceticktimes = np.ndarray(gyro_sourceticktimes)

        true_times = []
        true_datas = []
        for true_angular_velocity in true_sorted_dict:
            true_times.append(true_angular_velocity[0])
            true_datas.append(true_angular_velocity[1])

        true_sourceticktimes = true_times
        true_values = true_datas
        alignment_data_list = np.interp(gyro_sourceticktimes, true_sourceticktimes, true_values).aslist()
        unmatched_gyro_data_list = []
        return alignment_data_list, unmatched_gyro_data_list
        pass

    # 根据source_tick_time对齐gnss和rkt。如果最贴近的rtk有多个，随便取一个即可
    def alignment_data2(self, gyro_measurement_list, true_sorted_dict):
        alignment_data_list = []
        unmatched_gyro_data_list = []

        # 最大误差100ms
        max_diff_nano_time = 100 * 1000 * 1000

        for gyro in gyro_measurement_list:
            gyro_source_tick_nano_time = gyro.sourceTickTime * 1000 * 1000
            closest_true_value = true_sorted_dict.find_closest(gyro_source_tick_nano_time)

            if closest_true_value is None:
                unmatched_gyro_data_list.append(gyro)
            else:
                abs_diff_nano_time = abs(closest_true_value.source_tick_nano_time - gyro_source_tick_nano_time)
                if abs_diff_nano_time >= max_diff_nano_time:
                    unmatched_gyro_data_list.append(gyro)
                else:
                    alignment_data_list.append(AlignmentGyroRtkGnss(gyro, closest_true_value))

        return alignment_data_list, unmatched_gyro_data_list

    def parse_loc_gt_file(self, file_path):
        dict = SortedDictOperator()

        with (open(file_path, 'r') as lines):
            for line in lines:
                rtk = LocGtRtk(line)
                dict.add_item(rtk.source_tick_nano_time, rtk)

        return dict

    # 依赖data_data.txt和summary.txt计算时序测量值, 两个文件必须都存在
    def calc_measurement_value_list(self, data_data_txt_path, imuexteuler_matrix):
        gyro_value_list = []

        with (open(data_data_txt_path, 'r') as file):
            for line in file:
                data = json.loads(line.strip())
                value = data["value"]
                type = value["type"]

                if type == "gyro":
                    gyro = Gyro.model_validate(value)

                    # 计算天向陀螺仪数值
                    vector_data = np.array([gyro.valueX, gyro.valueY, gyro.valueZ])
                    result = np.dot(imuexteuler_matrix.astype(np.float64), vector_data.astype(np.float64))
                    result_array = np.array(result)
                    gyro.matrixZ = float(result_array[2])
                    # print(f"gyro.matrixZ: {gyro.matrixZ}")

                    gyro_value_list.append(gyro)

        return gyro_value_list

    # 如果loc_gt.txt文件存在，则计算rtk真值；否则使用data.txt计算gnss模拟的真值
    def calc_true_value_list(self, loc_gt_txt_path, data_txt_path):
        is_rtk_true_value = loc_gt_txt_path is not None and os.path.exists(loc_gt_txt_path)

        if is_rtk_true_value:
            value_sorted_dict = self.get_rtk_course_sorted_dict(loc_gt_txt_path)
        else:
            value_sorted_dict = self.get_gnss_course_sorted_dict(data_txt_path)

        return is_rtk_true_value, value_sorted_dict

    def get_gnss_course_sorted_dict(self, data_txt_path):
        # 计算gnss天向角速度
        gnss_course_sorted_dict = SortedDictOperator()

        previous_gnss = None
        with (open(data_txt_path, 'r') as file):
            for line in file:
                data = json.loads(line.strip())
                value = data["value"]
                type = value["type"]

                if type == "gnss":
                    current_gnss = Gnss.model_validate(value)

                    if previous_gnss is not None:
                        course_diff = current_gnss.course - previous_gnss.course
                        ticktime_diff = current_gnss.sourceTickTime - previous_gnss.sourceTickTime

                        # 角速度 = 角度差 / 时间差,单位：°/s
                        # 频率是1000ms，超过1.5倍，也即超过150ms的gnss真值丢弃
                        if 0 < ticktime_diff < 1500:
                            previous_nano_ticktime = previous_gnss.sourceTickTime * 1000 * 1000
                            current_nano_ticktime = current_gnss.sourceTickTime * 1000 * 1000
                            mid_nano_ticktime = (previous_nano_ticktime + current_nano_ticktime) / 2

                            angular_velocity = course_diff / (ticktime_diff / 1000)
                            true_angular_velocity = TrueAngularVelocity(mid_nano_ticktime, angular_velocity)

                            gnss_course_sorted_dict.add_item(mid_nano_ticktime, true_angular_velocity)

                    previous_gnss = current_gnss

        return gnss_course_sorted_dict

    def get_rtk_course_sorted_dict(self, loc_gt_txt_path):
        # 计算RTK天向角速度
        rtk_course_sorted_dict = SortedDictOperator()
        previous_rtk_line_data = None

        with open(loc_gt_txt_path, 'r') as file:
            for line in file:
                line = line.strip()  # 去除行尾换行符等

                # 1. 计算rtk天向角速度
                if previous_rtk_line_data is not None:
                    pri_rtk_data = LocGtRtk(previous_rtk_line_data)
                    current_rtk_data = LocGtRtk(line)

                    pri_course = 360 - pri_rtk_data.course
                    current_course = 360 - current_rtk_data.course
                    course_diff = current_course - pri_course
                    if course_diff > 180:
                        course_diff -= 360
                    elif course_diff < -180:
                        course_diff += 360

                    previous_ticktime = pri_rtk_data.source_tick_nano_time // 1000000
                    current_ticktime = current_rtk_data.source_tick_nano_time // 1000000
                    diff_ticktime = (current_ticktime - previous_ticktime)

                    # 角速度 = 角度差 / 时间差,单位：°/s
                    # 超过150ms的rtk真值丢弃
                    if 0 < diff_ticktime < 150:
                        angular_velocity = course_diff / (diff_ticktime / 1000)
                        mid_nano_ticktime = (pri_rtk_data.source_tick_nano_time + current_rtk_data.source_tick_nano_time) / 2
                        true_angular_velocity = TrueAngularVelocity(mid_nano_ticktime, angular_velocity)
                        rtk_course_sorted_dict.add_item(mid_nano_ticktime, true_angular_velocity)

                previous_rtk_line_data = line

        return rtk_course_sorted_dict

    def get_imuexteuler_matrix_from_summary(self, summary_txt_url):
        # 获取天向陀螺数值
        imuexteuler_matrix = None

        # [2025-06-13 12:03:37.781048][d][VAEngine][[VAPose]][128746][VAEngine::vaDrSignalManager::setIMUExtEuler@290]-1.000000,0.000000,-0.000000,0.000000,0.998441,0.055822,0.000000,0.055822,-0.998441
        with open(summary_txt_url, 'r', encoding='utf-8', errors='ignore') as file:
            for line in file:
                line = line.strip()  # 去除行尾换行符等
                if 'setIMUExtEuler' in line:
                    imuexteuler = line.split(']')[-1]
                    imuexteuler_splits = imuexteuler.split(',')
                    if len(imuexteuler_splits) == 9:
                        imuexteuler_matrix = np.array([
                            [imuexteuler_splits[0], imuexteuler_splits[1], imuexteuler_splits[2]],
                            [imuexteuler_splits[3], imuexteuler_splits[4], imuexteuler_splits[5]],
                            [imuexteuler_splits[6], imuexteuler_splits[7], imuexteuler_splits[8]]
                        ])
                        print(f"imuexteuler parse success, imuexteuler: {imuexteuler}")
                    else:
                        print(f"imuexteuler is not 9, imuexteuler : {imuexteuler}")
                    break
        return imuexteuler_matrix

    def parse_file(self, file_path):
        gnss_data = []

        with (open(file_path, 'r') as file):
            for line in file:
                data = json.loads(line.strip())
                value = data["value"]
                type = value["type"]

                if type == "gnss":
                    gnss = Gnss.model_validate(value)
                    gnss_data.append(gnss)

        return gnss_data

    # 构建表格样式函数
    def generate_table(self, signals_check_result_columns, signals_check_result_detail_columns, cur_date, is_rtk_true_value):
        # 生成表格
        cell_colors = []
        for _ in range(len(signals_check_result_columns)):
            cell_colors.append(['lavender'] * len(signals_check_result_columns["检查结果"]))  # 默认颜色

        # 不通过的整行标红
        for i in range(len(signals_check_result_columns["检查结果"])):
            if signals_check_result_columns["检查结果"][i] == "Failed":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'lightcoral'
            elif signals_check_result_columns["检查结果"][i] == "Warning":
                for j in range(len(signals_check_result_columns)):
                    cell_colors[j][i] = 'orange'
        # 绘制表格
        table_result = go.Table(
            header=dict(values=list(signals_check_result_columns.keys()), fill_color='paleturquoise',
                        align='center'),
            cells=dict(values=[signals_check_result_columns[k] for k in signals_check_result_columns],
                       fill_color=cell_colors,
                       align='left',
                       height=50)  # 增加单元格行高
        )
        rtk_title = "RTK" if is_rtk_true_value else "GNSS"
        table_result_title = f"{self._checker_title}结果-真值来自于{rtk_title}"

        table_result_detail = go.Table(
            header=dict(values=list(signals_check_result_detail_columns.keys()), fill_color='paleturquoise',
                        align='center'),
            cells=dict(values=[signals_check_result_detail_columns[k] for k in signals_check_result_detail_columns],
                       fill_color=cell_colors,
                       align='left',
                       height=200)  # 增加单元格行高
        )
        table_result_detail_title = f"{self._checker_title}详情"
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=1,  # 三行
            cols=1,  # 一列
            specs=[[{'type': 'table'}]  # 概览-表格
                   ],
            subplot_titles=(table_result_title),  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        # 将表格添加到对应的子图位置
        fig.add_trace(table_result, row=1, col=1)

        # --- 更新整体布局 ---
        fig.update_layout(
            height=1000,  # 可能需要调整高度以容纳所有表格
            title_text=f"<b>{self._checker_title}结果汇总-{cur_date}-真值来自于{rtk_title}</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50),  # 调整边距
            # 添加悬停模式配置
            hovermode='x unified',  # 统一显示同一x坐标的所有y值
            hoverdistance=100,  # 鼠标距离数据点的最大距离
            spikedistance=1000,  # 显示垂直参考线的距离
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_table_html_path)

    def generate_histogram(self, check_report_list, cur_date, is_rtk_true_value):
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=len(check_report_list),
            cols=1,
            specs=[[{'type': 'scatter'}] for _ in check_report_list],
            subplot_titles=[item.check_item for item in check_report_list],  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        for i, item in enumerate(check_report_list):
            abs_max = max(item.data_list)
            abs_min = min(item.data_list)
            abs_range = abs_max - abs_min
            real_nbinsx = int(abs_range + 1) * 100

            fig.add_trace(
                go.Histogram(
                    x=item.data_list,
                    name=item.check_item,
                    nbinsx=real_nbinsx,  # 设置直方图的bin数量
                    marker_color='blue',
                    marker_line_color='black',  # 边缘线颜色
                    marker_line_width=2,  # 边缘线宽度
                    opacity=0.7
                ),
                row=i + 1, col=1
            )

        rtk_title = "RTK" if is_rtk_true_value else "GNSS"
        # --- 更新整体布局 ---
        fig.update_layout(
            height=300 * len(check_report_list),  # 根据子图数量动态调整高度
            xaxis_title="rtk和gyro天向角度差值",  # 横轴标题
            yaxis_title="数量",  # 纵轴标题
            title_text=f"<b>{self._checker_title}结果汇总-{cur_date}-真值来自于{rtk_title}</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50),  # 调整边距
            # 添加悬停模式配置
            hovermode='x unified',  # 统一显示同一x坐标的所有y值
            hoverdistance=100,  # 鼠标距离数据点的最大距离
            spikedistance=1000,  # 显示垂直参考线的距离
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_histogram_html_path)

    def generate_scatter(self, valid_check_report_list, cur_date):
        # --- 创建带子图的 Figure ---
        # 假设我们想让表格1和表格2并排，表格3在它们下方单独一行
        # 或者更简单地，将它们垂直堆叠
        # specs 参数允许为每个子图指定类型，对于表格，类型为 'table'
        fig = make_subplots(
            rows=len(valid_check_report_list),
            cols=1,
            specs=[[{'type': 'scatter'}] for _ in valid_check_report_list],
            subplot_titles=[item.check_item for item in valid_check_report_list],  # 为每个子图添加标题
            vertical_spacing=0.05  # 调整子图间的垂直间距
        )

        for i, item in enumerate(valid_check_report_list):
            fig.add_trace(
                go.Scatter(
                    x=item.source_tick_time_list,
                    y=item.data_list,
                    name=item.check_item,
                    line=dict(color='blue', width=2),
                    mode='lines',
                    showlegend=True,  # 是否在图例中显示
                    legendgroup="default",  # 图例分组
                    hovertemplate="source_tick_time: %{x}<br>值: %{y:.2f}<extra></extra>"  # 鼠标悬停时显示的提示信息
                ),
                row=i + 1, col=1
            )

        # --- 更新整体布局 ---
        fig.update_layout(
            height=1000,  # 可能需要调整高度以容纳所有表格
            title_text=f"<b>{self._checker_title}结果汇总-{cur_date}</b>",  # 整个页面的主标题
            title_x=0.5,
            margin=dict(t=100, b=50, l=50, r=50),  # 调整边距
            # 添加悬停模式配置
            hovermode='x unified',  # 统一显示同一x坐标的所有y值
            hoverdistance=100,  # 鼠标距离数据点的最大距离
            spikedistance=1000,  # 显示垂直参考线的距离
        )

        # fig.show()
        fig.write_html(self._data_output_check_result_scatter_html_path)

    def deinit(self):
        """销毁检查实例"""
        pass

    def get_checker_name(self) -> str:
        """获取检查项名称"""
        return "C13GyroAccuracy"

    def prepare_dirs(self):
        # 测量值依赖的data.txt和summary.txt文件必须存在
        if self._data_data_txt_path is None or not os.path.exists(self._data_data_txt_path):
            print(f"File not found: {self._data_data_txt_path}")
            return False, "data.txt not found"

        if self._summary_txt_path is None or not os.path.exists(self._summary_txt_path):
            print(f"File not found: {self._summary_txt_path}")
            return False, "summary.txt not found"

        if not os.path.exists(self._data_output_path):
            print(f"output folder not found, try create: {self._data_output_path}")
            os.makedirs(self._data_output_path)
            if not os.path.exists(self._data_output_path):
                return False, "output folder not found, try create failed"
            else:
                print(f"output folder created: {self._data_output_path}")
                return True, ""
        else:
            print(f"output folder already exists: {self._data_output_path}")
        return True, ""

    def setup_logger(self, level=logging.INFO):
        """设置日志器，同时输出到控制台和文件"""
        date_time_str = datetime.now().strftime("%Y%m%d%H%M%S")
        log_file = os.path.join(self._data_output_path, f"{self._checker_name}_{date_time_str}.log")

        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        # 文件 handler
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)

        # 控制台 handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)

        # 创建 logger
        logger = logging.getLogger(self._checker_name)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger


# Example usage (for testing this file directly)
if __name__ == "__main__":
    # 添加可选参数（带默认值）
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", "-i", help="数据根路径")
    args = parser.parse_args()

    checker = C13GyroAccuracy()
    print(f"Checker Name: {checker.get_checker_name()}")
    checker.init(args.input)
    checker.check()
    checker.deinit()
