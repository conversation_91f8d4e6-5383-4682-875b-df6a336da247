# 这是一个上传下载的工具类
import multiprocessing
import platform
import time
import tarfile
import socket
import oss2
import glob
# from commons.utils import shellcommand
import os

access_key_id = 'LTAI5t5aRHLMF3zxbLPuZGTV'
access_key_secret = '******************************'
end_point = 'oss-cn-zhangjiakou.aliyuncs.com'
bucket_name = 'vlane'

# 上传文件
def upload_text2oss(bucket, text, oss_path):
    if oss_path.startswith("oss://"):
        oss_path = oss_path.replace(f"oss://{bucket_name}/", "")
        # print(f"oss_path:{oss_path}")
    for i in range(3):
        try:
            result = bucket.put_object(oss_path, text)
            # HTTP返回码。
            print('http status: {0}'.format(result.status))
            return
        except Exception as e:
            time.sleep(0.05 * (i + 1))
            print(f"Retry({i + 1}):{e}")
    print(f"upload failed![text]=>[{oss_path}]")
    return

# 上传文件夹
def upload_localdir2oss(access_key_id, access_key_secret, end_point, local_dir, oss_dir):
    
    pass


def download_oss2local(oss_path, local_path):
    # Convert the full OSS path to an object key
    if oss_path.startswith(f"oss://{bucket_name}/"):
        oss_path = oss_path.replace(f"oss://{bucket_name}/", "")

    # Check if the oss_path is still pointing to a directory
    if oss_path.endswith('/'):
        print("Error: Specified oss_path is a directory, not a file.")
        return

    # Initialize bucket
    bucket = oss2.Bucket(
        oss2.Auth(access_key_id, access_key_secret),
        end_point,
        bucket_name
    )

    try:
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        bucket.get_object_to_file(oss_path, local_path)
        print(f"Successfully downloaded {oss_path} to {local_path}")
    except oss2.exceptions.NoSuchKey as nk:
        print(f"OSS NoSuchKey error: {nk}")
    except oss2.exceptions as e:
        print(f"OSS error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")


def download_directory_from_oss(oss_dir, local_dir):
    print(f"download_directory_from_oss oss_dir:{oss_dir} \n local_dir:{local_dir}")

    bucket = oss2.Bucket(
        oss2.Auth(access_key_id, access_key_secret),
        end_point,
        bucket_name
    )
    if oss_dir.startswith(f"oss://{bucket_name}/"):
        oss_dir = oss_dir.replace(f"oss://{bucket_name}/", "")

    print(f" oss_dir:{oss_dir}")


    for obj in oss2.ObjectIterator(bucket, prefix=oss_dir):
        # Ignore non-file objects, typically directory listings
        if obj.key.endswith('/'):
            continue
        
        # print(f" obj.key:{obj.key}")
        # Construct local file path safely
        local_file_path = os.path.join(local_dir, os.path.relpath(obj.key, oss_dir))
        
        # Ensure the local directory exists for file storage
        os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
        
        # Download the file
        bucket.get_object_to_file(obj.key, local_file_path)


def upload_directory_to_oss(local_dir, oss_dir):
    """
    将本地目录及其内容上传到 OSS 指定路径。
    
    :param local_dir: 本地目录路径
    :param oss_dir: OSS 上的目标路径（目录）
    """
    oss_dir = oss_dir.replace(f"oss://{bucket_name}/", "")
    print(f"upload_directory_to_oss local_dir:{local_dir} \n oss_dir:{oss_dir}")
    auth = oss2.Auth(access_key_id, access_key_secret)
    bucket = oss2.Bucket(auth, end_point, bucket_name)

    # 遍历本地目录，并上传文件
    for root, dirs, files in os.walk(local_dir):
        for file_name in files:
            local_file_path = os.path.join(root, file_name)
            # 在 OSS 中构造相应的对象键，确保保留文件夹结构
            oss_file_path = os.path.join(oss_dir, os.path.relpath(local_file_path, start=local_dir))
            
            # 上传文件
            print(f'Uploading {local_file_path} to {oss_file_path}')
            bucket.put_object_from_file(oss_file_path, local_file_path)


def get_oss_folsers(oss_dir: str = "") -> list:

    print(f"get_oss_folsers, oss_dir:{oss_dir}")

    bucket = oss2.Bucket(
        oss2.Auth(access_key_id, access_key_secret),
        end_point,
        bucket_name
    )
    if oss_dir.startswith(f"oss://{bucket_name}/"):
        oss_dir = oss_dir.replace(f"oss://{bucket_name}/", "")

    # 分页遍历所有文件夹
    marker = ''
    folder_names = []
    while True:
        result = bucket.list_objects(prefix=oss_dir, delimiter='/', marker=marker)

        # 根据版本判断使用哪个属性
        if hasattr(result, 'prefix_list'):
            for prefix_name in result.prefix_list:
                folder_name = prefix_name[len(oss_dir):].rstrip('/')
                folder_names.append(folder_name)
                print(folder_name)
        elif hasattr(result, 'common_prefix_list'):
            for common_prefix in result.common_prefix_list:
                folder_name = common_prefix[len(oss_dir):].rstrip('/')
                folder_names.append(folder_name)
                print(folder_name)
        else:
            print("无法获取文件夹列表，请检查 oss2 版本或配置。")

        if not result.is_truncated:
            break
        marker = result.next_marker
    
    return folder_names

