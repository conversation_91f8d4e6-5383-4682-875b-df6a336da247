{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_100536/AutoSdkDemo/online/vlane/20250703_100536/data.txt", "version": "1.0", "date": "2025-07-03 17:15:19", "type": "CheckResult", "checkResult": [{"checkItem": "加速度器静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 25<br>时长>2秒区间数: 25<br>通过区间数: 25<br>失败区间数: 0<br><br>区间1:<br>  时长: 55.95秒<br>  加速度器数据个数: 540<br>  标准差 - X:0.0013, Y:0.0009, Z:0.0008<br>  极值差 - X:0.0096, Y:0.0091, Z:0.0082<br>区间2:<br>  时长: 4.50秒<br>  加速度器数据个数: 25<br>  标准差 - X:0.0019, Y:0.0017, Z:0.0009<br>  极值差 - X:0.0073, Y:0.0059, Z:0.0033<br>区间3:<br>  时长: 39.10秒<br>  加速度器数据个数: 372<br>  标准差 - X:0.0007, Y:0.0007, Z:0.0006<br>  极值差 - X:0.0053, Y:0.0076, Z:0.0060<br>区间4:<br>  时长: 9.40秒<br>  加速度器数据个数: 74<br>  标准差 - X:0.0011, Y:0.0012, Z:0.0013<br>  极值差 - X:0.0070, Y:0.0067, Z:0.0065<br>区间5:<br>  时长: 6.10秒<br>  加速度器数据个数: 41<br>  标准差 - X:0.0008, Y:0.0004, Z:0.0006<br>  极值差 - X:0.0042, Y:0.0016, Z:0.0032<br>区间6:<br>  时长: 4.40秒<br>  加速度器数据个数: 24<br>  标准差 - X:0.0016, Y:0.0011, Z:0.0009<br>  极值差 - X:0.0066, Y:0.0039, Z:0.0036<br>区间7:<br>  时长: 93.00秒<br>  加速度器数据个数: 911<br>  标准差 - X:0.0008, Y:0.0008, Z:0.0006<br>  极值差 - X:0.0111, Y:0.0127, Z:0.0084<br>区间8:<br>  时长: 9.50秒<br>  加速度器数据个数: 75<br>  标准差 - X:0.0012, Y:0.0007, Z:0.0009<br>  极值差 - X:0.0091, Y:0.0036, Z:0.0073<br>区间9:<br>  时长: 69.70秒<br>  加速度器数据个数: 676<br>  标准差 - X:0.0006, Y:0.0008, Z:0.0005<br>  极值差 - X:0.0078, Y:0.0114, Z:0.0059<br>区间10:<br>  时长: 28.90秒<br>  加速度器数据个数: 270<br>  标准差 - X:0.0005, Y:0.0006, Z:0.0005<br>  极值差 - X:0.0044, Y:0.0050, Z:0.0040<br>区间11:<br>  时长: 6.10秒<br>  加速度器数据个数: 42<br>  标准差 - X:0.0005, Y:0.0003, Z:0.0005<br>  极值差 - X:0.0024, Y:0.0018, Z:0.0034<br>区间12:<br>  时长: 69.00秒<br>  加速度器数据个数: 670<br>  标准差 - X:0.0006, Y:0.0005, Z:0.0005<br>  极值差 - X:0.0053, Y:0.0047, Z:0.0053<br>区间13:<br>  时长: 79.90秒<br>  加速度器数据个数: 779<br>  标准差 - X:0.0006, Y:0.0005, Z:0.0006<br>  极值差 - X:0.0045, Y:0.0053, Z:0.0058<br>区间14:<br>  时长: 8.20秒<br>  加速度器数据个数: 63<br>  标准差 - X:0.0010, Y:0.0011, Z:0.0007<br>  极值差 - X:0.0061, Y:0.0084, Z:0.0047<br>区间15:<br>  时长: 42.50秒<br>  加速度器数据个数: 406<br>  标准差 - X:0.0008, Y:0.0009, Z:0.0007<br>  极值差 - X:0.0076, Y:0.0061, Z:0.0077<br>区间16:<br>  时长: 87.80秒<br>  加速度器数据个数: 858<br>  标准差 - X:0.0005, Y:0.0006, Z:0.0005<br>  极值差 - X:0.0047, Y:0.0051, Z:0.0048<br>区间17:<br>  时长: 51.90秒<br>  加速度器数据个数: 499<br>  标准差 - X:0.0005, Y:0.0006, Z:0.0005<br>  极值差 - X:0.0058, Y:0.0065, Z:0.0039<br>区间18:<br>  时长: 20.90秒<br>  加速度器数据个数: 190<br>  标准差 - X:0.0006, Y:0.0004, Z:0.0004<br>  极值差 - X:0.0053, Y:0.0027, Z:0.0026<br>区间19:<br>  时长: 57.00秒<br>  加速度器数据个数: 550<br>  标准差 - X:0.0005, Y:0.0006, Z:0.0004<br>  极值差 - X:0.0032, Y:0.0065, Z:0.0037<br>区间20:<br>  时长: 7.30秒<br>  加速度器数据个数: 53<br>  标准差 - X:0.0009, Y:0.0004, Z:0.0006<br>  极值差 - X:0.0040, Y:0.0020, Z:0.0047<br>区间21:<br>  时长: 27.80秒<br>  加速度器数据个数: 258<br>  标准差 - X:0.0011, Y:0.0013, Z:0.0008<br>  极值差 - X:0.0113, Y:0.0097, Z:0.0060<br>区间22:<br>  时长: 12.10秒<br>  加速度器数据个数: 101<br>  标准差 - X:0.0006, Y:0.0007, Z:0.0007<br>  极值差 - X:0.0029, Y:0.0042, Z:0.0056<br>区间23:<br>  时长: 74.90秒<br>  加速度器数据个数: 730<br>  标准差 - X:0.0005, Y:0.0007, Z:0.0006<br>  极值差 - X:0.0058, Y:0.0092, Z:0.0071<br>区间24:<br>  时长: 21.80秒<br>  加速度器数据个数: 199<br>  标准差 - X:0.0006, Y:0.0008, Z:0.0005<br>  极值差 - X:0.0038, Y:0.0056, Z:0.0039<br>区间25:<br>  时长: 6.50秒<br>  加速度器数据个数: 46<br>  标准差 - X:0.0006, Y:0.0005, Z:0.0007<br>  极值差 - X:0.0028, Y:0.0023, Z:0.0037", "checkRules": "所有静态区间(时长>2秒)的标准差<0.1<br>且极值差<0.2dps", "description": "不通过需手动排查"}]}