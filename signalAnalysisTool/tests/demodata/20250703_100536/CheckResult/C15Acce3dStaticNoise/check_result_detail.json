{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查详情", "version": "1.0", "date": "2025-07-03 17:15:19", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "加速度器静态噪声过大", "static_intervals": [{"start_time": 116555165, "end_time": 116611117, "duration": 55.952, "start_time_adj": 116556165, "end_time_adj": 116610117}, {"start_time": 116622518, "end_time": 116627018, "duration": 4.5, "start_time_adj": 116623518, "end_time_adj": 116626018}, {"start_time": 116832517, "end_time": 116871618, "duration": 39.101, "start_time_adj": 116833517, "end_time_adj": 116870618}, {"start_time": 118645218, "end_time": 118654617, "duration": 9.399, "start_time_adj": 118646218, "end_time_adj": 118653617}, {"start_time": 118687118, "end_time": 118693218, "duration": 6.1, "start_time_adj": 118688118, "end_time_adj": 118692218}, {"start_time": 118704620, "end_time": 118709018, "duration": 4.398, "start_time_adj": 118705620, "end_time_adj": 118708018}, {"start_time": 119035417, "end_time": 119128417, "duration": 93.0, "start_time_adj": 119036417, "end_time_adj": 119127417}, {"start_time": 119551518, "end_time": 119561018, "duration": 9.5, "start_time_adj": 119552518, "end_time_adj": 119560018}, {"start_time": 119852618, "end_time": 119922317, "duration": 69.699, "start_time_adj": 119853618, "end_time_adj": 119921317}, {"start_time": 119961617, "end_time": 119990518, "duration": 28.901, "start_time_adj": 119962617, "end_time_adj": 119989518}, {"start_time": 120047517, "end_time": 120053617, "duration": 6.1, "start_time_adj": 120048517, "end_time_adj": 120052617}, {"start_time": 120062718, "end_time": 120131717, "duration": 68.999, "start_time_adj": 120063718, "end_time_adj": 120130717}, {"start_time": 120188318, "end_time": 120268217, "duration": 79.899, "start_time_adj": 120189318, "end_time_adj": 120267217}, {"start_time": 120380417, "end_time": 120388618, "duration": 8.201, "start_time_adj": 120381417, "end_time_adj": 120387618}, {"start_time": 120394318, "end_time": 120436822, "duration": 42.504, "start_time_adj": 120395318, "end_time_adj": 120435822}, {"start_time": 120443118, "end_time": 120530917, "duration": 87.799, "start_time_adj": 120444118, "end_time_adj": 120529917}, {"start_time": 120694418, "end_time": 120746319, "duration": 51.901, "start_time_adj": 120695418, "end_time_adj": 120745319}, {"start_time": 120764117, "end_time": 120785017, "duration": 20.9, "start_time_adj": 120765117, "end_time_adj": 120784017}, {"start_time": 120951019, "end_time": 121008018, "duration": 56.999, "start_time_adj": 120952019, "end_time_adj": 121007018}, {"start_time": 121165319, "end_time": 121172617, "duration": 7.298, "start_time_adj": 121166319, "end_time_adj": 121171617}, {"start_time": 121268317, "end_time": 121296118, "duration": 27.801, "start_time_adj": 121269317, "end_time_adj": 121295118}, {"start_time": 121354218, "end_time": 121366318, "duration": 12.1, "start_time_adj": 121355218, "end_time_adj": 121365318}, {"start_time": 121369617, "end_time": 121444518, "duration": 74.901, "start_time_adj": 121370617, "end_time_adj": 121443518}, {"start_time": 121572518, "end_time": 121594317, "duration": 21.799, "start_time_adj": 121573518, "end_time_adj": 121593317}, {"start_time": 121603618, "end_time": 121610122, "duration": 6.504, "start_time_adj": 121604618, "end_time_adj": 121609122}], "long_static_intervals": [{"start_time": 116555165, "end_time": 116611117, "duration": 55.952, "gyro_count": 540, "mean_x": -0.016179369196847634, "mean_y": -0.04941519063518003, "mean_z": 1.02304945897173, "std_x": 0.00130839122491152, "std_y": 0.0009193989076750159, "std_z": 0.0008020741314722857, "range_x": 0.009550967253744602, "range_y": 0.009061280637979507, "range_z": 0.008203983306884766}, {"start_time": 116622518, "end_time": 116627018, "duration": 4.5, "gyro_count": 25, "mean_x": -0.028089812844991683, "mean_y": -0.049812244772911074, "mean_z": 1.022865333557129, "std_x": 0.0018757787915905948, "std_y": 0.001666608894341752, "std_z": 0.0009190627130519382, "range_x": 0.00734696164727211, "range_y": 0.005877543240785599, "range_z": 0.003306150436401367}, {"start_time": 116832517, "end_time": 116871618, "duration": 39.101, "gyro_count": 372, "mean_x": -0.010027311716018426, "mean_y": -0.021208370074389443, "mean_z": 1.0240075927908703, "std_x": 0.0007186308729732527, "std_y": 0.0006820821292461433, "std_z": 0.00061312613814764, "range_x": 0.005265349056571722, "range_y": 0.007591744884848595, "range_z": 0.006000041961669922}, {"start_time": 118645218, "end_time": 118654617, "duration": 9.399, "gyro_count": 74, "mean_x": -0.036134041996823775, "mean_y": -0.0009944851697435148, "mean_z": 1.0241533630603068, "std_x": 0.0010611550949866948, "std_y": 0.0011840617684661485, "std_z": 0.0012867823567613728, "range_x": 0.006979670375585556, "range_y": 0.006734765833243728, "range_z": 0.006489753723144531}, {"start_time": 118687118, "end_time": 118693218, "duration": 6.1, "gyro_count": 41, "mean_x": -0.043137881995701205, "mean_y": 0.007346942509728961, "mean_z": 1.0236137552959164, "std_x": 0.0008458063255492563, "std_y": 0.0003901147580164954, "std_z": 0.0005866691296464243, "range_x": 0.004163343459367752, "range_y": 0.0015918081626296043, "range_z": 0.003183603286743164}, {"start_time": 118704620, "end_time": 118709018, "duration": 4.398, "gyro_count": 24, "mean_x": -0.04202551844840249, "mean_y": 0.008239785306310901, "mean_z": 1.0235306471586227, "std_x": 0.0015805252944903643, "std_y": 0.001065944336914019, "std_z": 0.0009110820111375696, "range_x": 0.006612256169319153, "range_y": 0.003918325062841177, "range_z": 0.003551006317138672}, {"start_time": 119035417, "end_time": 119128417, "duration": 93.0, "gyro_count": 911, "mean_x": -0.009966611644175439, "mean_y": -0.01920029195485602, "mean_z": 1.024847456443192, "std_x": 0.0008089197508590392, "std_y": 0.0008341966944008747, "std_z": 0.0006343045399605987, "range_x": 0.011142894858494401, "range_y": 0.012734704650938511, "range_z": 0.008448958396911621}, {"start_time": 119551518, "end_time": 119561018, "duration": 9.5, "gyro_count": 75, "mean_x": -0.01907590474933386, "mean_y": -0.017098773072163263, "mean_z": 1.02477880636851, "std_x": 0.0012159073888233353, "std_y": 0.0006512668988538504, "std_z": 0.0008852759102650898, "range_x": 0.009061282500624657, "range_y": 0.0035510296002030373, "range_z": 0.007346987724304199}, {"start_time": 119852618, "end_time": 119922317, "duration": 69.699, "gyro_count": 676, "mean_x": -0.015590861585740683, "mean_y": -0.021434914893828906, "mean_z": 1.02480507496546, "std_x": 0.0006150502634222333, "std_y": 0.0007536591318290481, "std_z": 0.0005237191918123642, "range_x": 0.00783676840364933, "range_y": 0.011387679725885391, "range_z": 0.005877494812011719}, {"start_time": 119961617, "end_time": 119990518, "duration": 28.901, "gyro_count": 270, "mean_x": -0.022073005436471217, "mean_y": -0.020302958338073007, "mean_z": 1.0244471960597568, "std_x": 0.0004958115593401156, "std_y": 0.000595689328646019, "std_z": 0.0005009856430467163, "range_x": 0.004408249631524086, "range_y": 0.005020447075366974, "range_z": 0.004040837287902832}, {"start_time": 120047517, "end_time": 120053617, "duration": 6.1, "gyro_count": 42, "mean_x": -0.002988344022361118, "mean_y": -0.02150436947565703, "mean_z": 1.0246443549791973, "std_x": 0.00048230559914598937, "std_y": 0.00034520802528359635, "std_z": 0.0005329785909727135, "range_x": 0.0024489082861691713, "range_y": 0.0018367115408182144, "range_z": 0.0034284591674804688}, {"start_time": 120062718, "end_time": 120131717, "duration": 68.999, "gyro_count": 670, "mean_x": -0.01614497743769368, "mean_y": -0.016586714449213513, "mean_z": 1.0245132796799958, "std_x": 0.0005566876765100179, "std_y": 0.0005307458544977257, "std_z": 0.0005476481614773902, "range_x": 0.005265349522233009, "range_y": 0.004653031937777996, "range_z": 0.0052651166915893555}, {"start_time": 120188318, "end_time": 120268217, "duration": 79.899, "gyro_count": 779, "mean_x": -0.017905854398865297, "mean_y": -0.018640059420650057, "mean_z": 1.0244878891649847, "std_x": 0.0005660929939519867, "std_y": 0.0005270282200375381, "std_z": 0.0005559863576943152, "range_x": 0.004530640318989754, "range_y": 0.00526534765958786, "range_z": 0.005755186080932617}, {"start_time": 120380417, "end_time": 120388618, "duration": 8.201, "gyro_count": 63, "mean_x": -0.02476966700383595, "mean_y": -0.015008753520392236, "mean_z": 1.0243809847604661, "std_x": 0.0009811788002192733, "std_y": 0.0011318518920637975, "std_z": 0.000745944756153861, "range_x": 0.0061224475502967834, "range_y": 0.008448965847492218, "range_z": 0.004652857780456543}, {"start_time": 120394318, "end_time": 120436822, "duration": 42.504, "gyro_count": 406, "mean_x": -0.02872724850831801, "mean_y": -0.017288531410723486, "mean_z": 1.0242585912713864, "std_x": 0.0007938209258139025, "std_y": 0.0009049517154086888, "std_z": 0.0007027134611199216, "range_x": 0.007591865956783295, "range_y": 0.006122448481619358, "range_z": 0.007714390754699707}, {"start_time": 120443118, "end_time": 120530917, "duration": 87.799, "gyro_count": 858, "mean_x": -0.02969931017841909, "mean_y": -0.019166255569506636, "mean_z": 1.0241273196982892, "std_x": 0.0004580695804016465, "std_y": 0.0006281166312961958, "std_z": 0.000482171996976942, "range_x": 0.004653032869100571, "range_y": 0.005142837762832642, "range_z": 0.0047757625579833984}, {"start_time": 120694418, "end_time": 120746319, "duration": 51.901, "gyro_count": 499, "mean_x": -0.018804618549385744, "mean_y": -0.02337966533844361, "mean_z": 1.0245038958971868, "std_x": 0.00047791422560000327, "std_y": 0.0006083248718655152, "std_z": 0.00047372431376708324, "range_x": 0.005755153484642506, "range_y": 0.006489742547273636, "range_z": 0.00391840934753418}, {"start_time": 120764117, "end_time": 120785017, "duration": 20.9, "gyro_count": 190, "mean_x": -0.021101189385119238, "mean_y": -0.010642747801581495, "mean_z": 1.0244584591765153, "std_x": 0.0005683448065568356, "std_y": 0.00040038423294672397, "std_z": 0.00042846927155569217, "range_x": 0.00526534765958786, "range_y": 0.0026939306408166885, "range_z": 0.0025714635848999023}, {"start_time": 120951019, "end_time": 121008018, "duration": 56.999, "gyro_count": 550, "mean_x": -0.01900585261596875, "mean_y": -0.031721851493824615, "mean_z": 1.0243698954582214, "std_x": 0.0004974518496410208, "std_y": 0.0006195850871479718, "std_z": 0.00041304009735932475, "range_x": 0.0031836163252592087, "range_y": 0.006489863619208336, "range_z": 0.0036734342575073242}, {"start_time": 121165319, "end_time": 121172617, "duration": 7.298, "gyro_count": 53, "mean_x": -0.027698889901896694, "mean_y": -0.02151406285757164, "mean_z": 1.024093972062165, "std_x": 0.0009063023952047562, "std_y": 0.00043397020459918085, "std_z": 0.0006010735369706072, "range_x": 0.004040837287902832, "range_y": 0.001959221437573433, "range_z": 0.004652976989746094}, {"start_time": 121268317, "end_time": 121296118, "duration": 27.801, "gyro_count": 258, "mean_x": -0.0159814827678393, "mean_y": -0.06647034389392813, "mean_z": 1.0232098014779794, "std_x": 0.0011262198799571375, "std_y": 0.0012640741707368255, "std_z": 0.0007948490168882503, "range_x": 0.011265406385064125, "range_y": 0.009673479944467545, "range_z": 0.005999922752380371}, {"start_time": 121354218, "end_time": 121366318, "duration": 12.1, "gyro_count": 101, "mean_x": -0.01679369178370084, "mean_y": -0.016982830424636306, "mean_z": 1.0245864119860206, "std_x": 0.0005681473199573609, "std_y": 0.0007403422126712741, "std_z": 0.0006908444976435452, "range_x": 0.002938714809715748, "range_y": 0.004163227044045925, "range_z": 0.005632638931274414}, {"start_time": 121369617, "end_time": 121444518, "duration": 74.901, "gyro_count": 730, "mean_x": -0.014789671232694224, "mean_y": -0.01831400956422703, "mean_z": 1.0246225660794401, "std_x": 0.0005485122361641482, "std_y": 0.0007154731692264251, "std_z": 0.0005738523795768529, "range_x": 0.0057550352066755295, "range_y": 0.009183674119412899, "range_z": 0.007102012634277344}, {"start_time": 121572518, "end_time": 121594317, "duration": 21.799, "gyro_count": 199, "mean_x": -0.021040313516804323, "mean_y": -0.013772131701996877, "mean_z": 1.0243497302184752, "std_x": 0.0006229179916215007, "std_y": 0.0008236135538136788, "std_z": 0.00051156350611433, "range_x": 0.0037959329783916473, "range_y": 0.0056326426565647125, "range_z": 0.00391840934753418}, {"start_time": 121603618, "end_time": 121610122, "duration": 6.504, "gyro_count": 46, "mean_x": -0.017779075824048206, "mean_y": -0.016349595748697935, "mean_z": 1.024341642856598, "std_x": 0.0006410199137759324, "std_y": 0.0005288805549907459, "std_z": 0.0006906225513633161, "range_x": 0.0028163231909275055, "range_y": 0.0023265155032277107, "range_z": 0.003673553466796875}], "failed_intervals": [], "all_passed": true}]}