{"checker": "C03ValueRepeatFrame", "title": "数值重帧检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_100536/AutoSdkDemo/online/vlane/20250703_100536/data.txt", "version": "1.0", "date": "2025-07-03 17:09:06", "type": "CheckResult", "checkResult": [{"checkItem": "acce3d数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.0018217355826707403, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro_xyz数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.0013852780993225421, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro_temperature数值重帧率", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "数值重帧率<0", "description": "不通过需手动排查"}, {"checkItem": "pulse数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.13342315501831223, "checkRules": "数值重帧率<0.01", "description": "不通过需手动排查"}, {"checkItem": "gnss数值重帧率", "checkResult": "Pass", "checkResultStatistic": 0.00037979491074819596, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}]}