{"checker": "lostRate<PERSON><PERSON><PERSON>", "title": "轮速正负号检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_100536/AutoSdkDemo/online/vlane/20250703_100536/data.txt", "version": "1.0", "date": "2025-07-03 17:28:33", "type": "CheckResult", "checkResult": [{"checkItem": "轮速正负号检查", "checkResult": "Fail", "checkResultStatistic": 542, "checkRules": "倒车时轮速为负，前进时轮速为正", "description": "不通过需手动排查"}, {"checkItem": "D档轮速检查", "checkResult": "Pass", "checkResultStatistic": 0, "checkRules": "D档时，轮速<-1e-6", "description": "D档轮速检查"}, {"checkItem": "N档轮速检查", "checkResult": "Pass", "checkResultStatistic": 0, "checkRules": "N档时，轮速>1e-6", "description": "N档轮速检查"}, {"checkItem": "R档轮速检查", "checkResult": "Pass", "checkResultStatistic": 0, "checkRules": "R档时，轮速>1e-6", "description": "R档轮速检查"}, {"checkItem": "P档轮速检查", "checkResult": "Pass", "checkResultStatistic": 0, "checkRules": "P档时，轮速>1e-6", "description": "P档轮速检查"}]}