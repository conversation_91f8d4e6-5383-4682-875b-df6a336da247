{"checker": "C14GryoStaticNoise", "title": "陀螺仪静态噪声检查详情", "version": "1.0", "date": "2025-07-03 17:15:15", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "陀螺静态噪声过大", "static_intervals": [{"start_time": 116555165, "end_time": 116611117, "duration": 55.952, "start_time_adj": 116556165, "end_time_adj": 116610117}, {"start_time": 116622518, "end_time": 116627018, "duration": 4.5, "start_time_adj": 116623518, "end_time_adj": 116626018}, {"start_time": 116832517, "end_time": 116871618, "duration": 39.101, "start_time_adj": 116833517, "end_time_adj": 116870618}, {"start_time": 118645218, "end_time": 118654617, "duration": 9.399, "start_time_adj": 118646218, "end_time_adj": 118653617}, {"start_time": 118687118, "end_time": 118693218, "duration": 6.1, "start_time_adj": 118688118, "end_time_adj": 118692218}, {"start_time": 118704620, "end_time": 118709018, "duration": 4.398, "start_time_adj": 118705620, "end_time_adj": 118708018}, {"start_time": 119035417, "end_time": 119128417, "duration": 93.0, "start_time_adj": 119036417, "end_time_adj": 119127417}, {"start_time": 119551518, "end_time": 119561018, "duration": 9.5, "start_time_adj": 119552518, "end_time_adj": 119560018}, {"start_time": 119852618, "end_time": 119922317, "duration": 69.699, "start_time_adj": 119853618, "end_time_adj": 119921317}, {"start_time": 119961617, "end_time": 119990518, "duration": 28.901, "start_time_adj": 119962617, "end_time_adj": 119989518}, {"start_time": 120047517, "end_time": 120053617, "duration": 6.1, "start_time_adj": 120048517, "end_time_adj": 120052617}, {"start_time": 120062718, "end_time": 120131717, "duration": 68.999, "start_time_adj": 120063718, "end_time_adj": 120130717}, {"start_time": 120188318, "end_time": 120268217, "duration": 79.899, "start_time_adj": 120189318, "end_time_adj": 120267217}, {"start_time": 120380417, "end_time": 120388618, "duration": 8.201, "start_time_adj": 120381417, "end_time_adj": 120387618}, {"start_time": 120394318, "end_time": 120436822, "duration": 42.504, "start_time_adj": 120395318, "end_time_adj": 120435822}, {"start_time": 120443118, "end_time": 120530917, "duration": 87.799, "start_time_adj": 120444118, "end_time_adj": 120529917}, {"start_time": 120694418, "end_time": 120746319, "duration": 51.901, "start_time_adj": 120695418, "end_time_adj": 120745319}, {"start_time": 120764117, "end_time": 120785017, "duration": 20.9, "start_time_adj": 120765117, "end_time_adj": 120784017}, {"start_time": 120951019, "end_time": 121008018, "duration": 56.999, "start_time_adj": 120952019, "end_time_adj": 121007018}, {"start_time": 121165319, "end_time": 121172617, "duration": 7.298, "start_time_adj": 121166319, "end_time_adj": 121171617}, {"start_time": 121268317, "end_time": 121296118, "duration": 27.801, "start_time_adj": 121269317, "end_time_adj": 121295118}, {"start_time": 121354218, "end_time": 121366318, "duration": 12.1, "start_time_adj": 121355218, "end_time_adj": 121365318}, {"start_time": 121369617, "end_time": 121444518, "duration": 74.901, "start_time_adj": 121370617, "end_time_adj": 121443518}, {"start_time": 121572518, "end_time": 121594317, "duration": 21.799, "start_time_adj": 121573518, "end_time_adj": 121593317}, {"start_time": 121603618, "end_time": 121610122, "duration": 6.504, "start_time_adj": 121604618, "end_time_adj": 121609122}], "long_static_intervals": [{"start_time": 116555165, "end_time": 116611117, "duration": 55.952, "gyro_count": 539, "mean_x": 0.6519436867206128, "mean_y": -1.3911075008158782, "mean_z": -0.5170745868961533, "std_x": 0.02642955278531633, "std_y": 0.020864457435718384, "std_z": 0.015823094629830087, "range_x": 0.2874135971069336, "range_y": 0.12192130088806152, "range_z": 0.12192127108573914}, {"start_time": 116622518, "end_time": 116627018, "duration": 4.5, "gyro_count": 25, "mean_x": 0.6528222894668579, "mean_y": -1.3801980876922608, "mean_z": -0.5194044423103332, "std_x": 0.04570422616084485, "std_y": 0.023865181622880063, "std_z": 0.026473874118353165, "range_x": 0.1654624342918396, "range_y": 0.12192130088806152, "range_z": 0.10451680421829224}, {"start_time": 116832517, "end_time": 116871618, "duration": 39.101, "gyro_count": 372, "mean_x": 0.656917800505956, "mean_y": -1.3952122814552759, "mean_z": -0.5179933156537754, "std_x": 0.023357747533774263, "std_y": 0.020415555097661093, "std_z": 0.015797225174278798, "range_x": 0.1654624342918396, "range_y": 0.11321902275085449, "range_z": 0.08711224794387817}, {"start_time": 118645218, "end_time": 118654617, "duration": 9.399, "gyro_count": 74, "mean_x": 0.6470507351127831, "mean_y": -1.4060268933708604, "mean_z": -0.509943948404209, "std_x": 0.034556694319052235, "std_y": 0.022571711440041428, "std_z": 0.017245761545820405, "range_x": 0.18286705017089844, "range_y": 0.12192118167877197, "range_z": 0.07838007807731628}, {"start_time": 118687118, "end_time": 118693218, "duration": 6.1, "gyro_count": 41, "mean_x": 0.6510484756492987, "mean_y": -1.410429835319519, "mean_z": -0.5125520047618122, "std_x": 0.0213118049534463, "std_y": 0.022704068527164525, "std_z": 0.0162404208542622, "range_x": 0.08708244562149048, "range_y": 0.10448694229125977, "range_z": 0.07838007807731628}, {"start_time": 118704620, "end_time": 118709018, "duration": 4.398, "gyro_count": 24, "mean_x": 0.6488176931937536, "mean_y": -1.4148429532845814, "mean_z": -0.5134630079070727, "std_x": 0.02928167724602586, "std_y": 0.03492791856941993, "std_z": 0.013457950456338881, "range_x": 0.11321902275085449, "range_y": 0.15676021575927734, "range_z": 0.05224347114562988}, {"start_time": 119035417, "end_time": 119128417, "duration": 93.0, "gyro_count": 911, "mean_x": 0.6482963891113105, "mean_y": -1.409848527903091, "mean_z": -0.5098698745051278, "std_x": 0.02612069982616341, "std_y": 0.021346579625762193, "std_z": 0.016159086331887226, "range_x": 0.33095479011535645, "range_y": 0.15676021575927734, "range_z": 0.10448691248893738}, {"start_time": 119551518, "end_time": 119561018, "duration": 9.5, "gyro_count": 76, "mean_x": 0.647329604155139, "mean_y": -1.4091338295685618, "mean_z": -0.5092460368024675, "std_x": 0.021821044769268338, "std_y": 0.023023090086915582, "std_z": 0.017673906758241688, "range_x": 0.09581446647644043, "range_y": 0.14805805683135986, "range_z": 0.09581449627876282}, {"start_time": 119852618, "end_time": 119922317, "duration": 69.699, "gyro_count": 677, "mean_x": 0.64636683719373, "mean_y": -1.4073016747414098, "mean_z": -0.5087979135696433, "std_x": 0.02567990443238432, "std_y": 0.019490379275860305, "std_z": 0.016293783471230082, "range_x": 0.2874135375022888, "range_y": 0.11321902275085449, "range_z": 0.11321902275085449}, {"start_time": 119961617, "end_time": 119990518, "duration": 28.901, "gyro_count": 269, "mean_x": 0.6467611658971992, "mean_y": -1.4061270198857474, "mean_z": -0.5084863711688599, "std_x": 0.021650568509623717, "std_y": 0.02097747988805543, "std_z": 0.015865864720378944, "range_x": 0.13065344095230103, "range_y": 0.130623459815979, "range_z": 0.08708235621452332}, {"start_time": 120047517, "end_time": 120053617, "duration": 6.1, "gyro_count": 41, "mean_x": 0.6508362264167972, "mean_y": -1.402782274455559, "mean_z": -0.5110655223451009, "std_x": 0.01847637197100779, "std_y": 0.0229101045044635, "std_z": 0.017024716073587375, "range_x": 0.06967782974243164, "range_y": 0.09578478336334229, "range_z": 0.06967782974243164}, {"start_time": 120062718, "end_time": 120131717, "duration": 68.999, "gyro_count": 671, "mean_x": 0.6487849487100024, "mean_y": -1.40549240506708, "mean_z": -0.5089994476644542, "std_x": 0.022071781276566085, "std_y": 0.020722256425325446, "std_z": 0.015392420580049936, "range_x": 0.18286705017089844, "range_y": 0.11321902275085449, "range_z": 0.0870823860168457}, {"start_time": 120188318, "end_time": 120268217, "duration": 79.899, "gyro_count": 780, "mean_x": 0.6491743094646014, "mean_y": -1.4042646151322584, "mean_z": -0.506280316450657, "std_x": 0.021969677653940006, "std_y": 0.020585058717036996, "std_z": 0.015200238307726776, "range_x": 0.1741647720336914, "range_y": 0.130623459815979, "range_z": 0.0870823860168457}, {"start_time": 120380417, "end_time": 120388618, "duration": 8.201, "gyro_count": 62, "mean_x": 0.6471319852336761, "mean_y": -1.4063577036703787, "mean_z": -0.5049777939434974, "std_x": 0.031204680508495456, "std_y": 0.025218054449305177, "std_z": 0.014916739201343633, "range_x": 0.17416471242904663, "range_y": 0.1480579376220703, "range_z": 0.06967779994010925}, {"start_time": 120394318, "end_time": 120436822, "duration": 42.504, "gyro_count": 406, "mean_x": 0.6485163984921178, "mean_y": -1.4048459236257769, "mean_z": -0.5072427514591827, "std_x": 0.025655522047862617, "std_y": 0.02212151620189967, "std_z": 0.016183258195256286, "range_x": 0.1741647720336914, "range_y": 0.16546249389648438, "range_z": 0.09578463435173035}, {"start_time": 120443118, "end_time": 120530917, "duration": 87.799, "gyro_count": 858, "mean_x": 0.6481972745943181, "mean_y": -1.406031099371699, "mean_z": -0.5060634731829583, "std_x": 0.023245586351310587, "std_y": 0.020208945622057415, "std_z": 0.014980975311265503, "range_x": 0.15676021575927734, "range_y": 0.1480579376220703, "range_z": 0.09578463435173035}, {"start_time": 120694418, "end_time": 120746319, "duration": 51.901, "gyro_count": 500, "mean_x": 0.6403870269060135, "mean_y": -1.4099121499061584, "mean_z": -0.5062339022159577, "std_x": 0.022560189116792136, "std_y": 0.019447809691729306, "std_z": 0.015115086388723312, "range_x": 0.15676021575927734, "range_y": 0.11321902275085449, "range_z": 0.09578463435173035}, {"start_time": 120764117, "end_time": 120785017, "duration": 20.9, "gyro_count": 189, "mean_x": 0.640638789802632, "mean_y": -1.4106221804543146, "mean_z": -0.5063176752713622, "std_x": 0.019166679860620376, "std_y": 0.020997510669523597, "std_z": 0.016879959589058045, "range_x": 0.09581446647644043, "range_y": 0.09581446647644043, "range_z": 0.07838010787963867}, {"start_time": 120951019, "end_time": 121008018, "duration": 56.999, "gyro_count": 550, "mean_x": 0.6453498115322807, "mean_y": -1.409887765971097, "mean_z": -0.5068295971913771, "std_x": 0.022712871576461374, "std_y": 0.020190300803437033, "std_z": 0.014905018223483302, "range_x": 0.1654624342918396, "range_y": 0.130623459815979, "range_z": 0.0870823860168457}, {"start_time": 121165319, "end_time": 121172617, "duration": 7.298, "gyro_count": 52, "mean_x": 0.647309334232257, "mean_y": -1.4018101692199707, "mean_z": -0.5042828321456909, "std_x": 0.021929444669587666, "std_y": 0.021119863274708054, "std_z": 0.016107407544025322, "range_x": 0.08711212873458862, "range_y": 0.09578466415405273, "range_z": 0.052273333072662354}, {"start_time": 121268317, "end_time": 121296118, "duration": 27.801, "gyro_count": 258, "mean_x": 0.6407156501167505, "mean_y": -1.405485893404761, "mean_z": -0.5044438388458518, "std_x": 0.03342304642737796, "std_y": 0.023548798220776563, "std_z": 0.01880381837509977, "range_x": 0.23514026403427124, "range_y": 0.18289697170257568, "range_z": 0.17419463396072388}, {"start_time": 121354218, "end_time": 121366318, "duration": 12.1, "gyro_count": 102, "mean_x": 0.641731386091195, "mean_y": -1.403508863028358, "mean_z": -0.5049474996094611, "std_x": 0.02382717621410823, "std_y": 0.019172979690341034, "std_z": 0.014546953060571155, "range_x": 0.11321902275085449, "range_y": 0.08708250522613525, "range_z": 0.07838007807731628}, {"start_time": 121369617, "end_time": 121444518, "duration": 74.901, "gyro_count": 730, "mean_x": 0.6390586562352638, "mean_y": -1.4057822024985536, "mean_z": -0.505716087107789, "std_x": 0.0236451913882085, "std_y": 0.020788935773810815, "std_z": 0.015393390821242818, "range_x": 0.1741945743560791, "range_y": 0.1306532621383667, "range_z": 0.09578463435173035}, {"start_time": 121572518, "end_time": 121594317, "duration": 21.799, "gyro_count": 198, "mean_x": 0.6493896224884071, "mean_y": -1.4082564067358923, "mean_z": -0.5046790285844995, "std_x": 0.02429067162764271, "std_y": 0.01996717292465233, "std_z": 0.015062482759094676, "range_x": 0.15676021575927734, "range_y": 0.09578466415405273, "range_z": 0.06967779994010925}, {"start_time": 121603618, "end_time": 121610122, "duration": 6.504, "gyro_count": 46, "mean_x": 0.6467343983442887, "mean_y": -1.4104739997697913, "mean_z": -0.50455075243245, "std_x": 0.019894259795430417, "std_y": 0.01942657650338786, "std_z": 0.013156249713702531, "range_x": 0.07838010787963867, "range_y": 0.07838022708892822, "range_z": 0.05224350094795227}], "failed_intervals": [], "all_passed": true}]}