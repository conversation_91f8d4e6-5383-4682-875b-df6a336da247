{"checker": "C14GryoStaticNoise", "title": "陀螺仪静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_100536/AutoSdkDemo/online/vlane/20250703_100536/data.txt", "version": "1.0", "date": "2025-07-03 17:15:15", "type": "CheckResult", "checkResult": [{"checkItem": "陀螺静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 25<br>时长>2秒区间数: 25<br>通过区间数: 25<br>失败区间数: 0<br><br>区间1:<br>  时长: 55.95秒<br>  陀螺仪数据个数: 539<br>  标准差 - X:0.0264, Y:0.0209, Z:0.0158<br>  极值差 - X:0.2874, Y:0.1219, Z:0.1219<br>区间2:<br>  时长: 4.50秒<br>  陀螺仪数据个数: 25<br>  标准差 - X:0.0457, Y:0.0239, Z:0.0265<br>  极值差 - X:0.1655, Y:0.1219, Z:0.1045<br>区间3:<br>  时长: 39.10秒<br>  陀螺仪数据个数: 372<br>  标准差 - X:0.0234, Y:0.0204, Z:0.0158<br>  极值差 - X:0.1655, Y:0.1132, Z:0.0871<br>区间4:<br>  时长: 9.40秒<br>  陀螺仪数据个数: 74<br>  标准差 - X:0.0346, Y:0.0226, Z:0.0172<br>  极值差 - X:0.1829, Y:0.1219, Z:0.0784<br>区间5:<br>  时长: 6.10秒<br>  陀螺仪数据个数: 41<br>  标准差 - X:0.0213, Y:0.0227, Z:0.0162<br>  极值差 - X:0.0871, Y:0.1045, Z:0.0784<br>区间6:<br>  时长: 4.40秒<br>  陀螺仪数据个数: 24<br>  标准差 - X:0.0293, Y:0.0349, Z:0.0135<br>  极值差 - X:0.1132, Y:0.1568, Z:0.0522<br>区间7:<br>  时长: 93.00秒<br>  陀螺仪数据个数: 911<br>  标准差 - X:0.0261, Y:0.0213, Z:0.0162<br>  极值差 - X:0.3310, Y:0.1568, Z:0.1045<br>区间8:<br>  时长: 9.50秒<br>  陀螺仪数据个数: 76<br>  标准差 - X:0.0218, Y:0.0230, Z:0.0177<br>  极值差 - X:0.0958, Y:0.1481, Z:0.0958<br>区间9:<br>  时长: 69.70秒<br>  陀螺仪数据个数: 677<br>  标准差 - X:0.0257, Y:0.0195, Z:0.0163<br>  极值差 - X:0.2874, Y:0.1132, Z:0.1132<br>区间10:<br>  时长: 28.90秒<br>  陀螺仪数据个数: 269<br>  标准差 - X:0.0217, Y:0.0210, Z:0.0159<br>  极值差 - X:0.1307, Y:0.1306, Z:0.0871<br>区间11:<br>  时长: 6.10秒<br>  陀螺仪数据个数: 41<br>  标准差 - X:0.0185, Y:0.0229, Z:0.0170<br>  极值差 - X:0.0697, Y:0.0958, Z:0.0697<br>区间12:<br>  时长: 69.00秒<br>  陀螺仪数据个数: 671<br>  标准差 - X:0.0221, Y:0.0207, Z:0.0154<br>  极值差 - X:0.1829, Y:0.1132, Z:0.0871<br>区间13:<br>  时长: 79.90秒<br>  陀螺仪数据个数: 780<br>  标准差 - X:0.0220, Y:0.0206, Z:0.0152<br>  极值差 - X:0.1742, Y:0.1306, Z:0.0871<br>区间14:<br>  时长: 8.20秒<br>  陀螺仪数据个数: 62<br>  标准差 - X:0.0312, Y:0.0252, Z:0.0149<br>  极值差 - X:0.1742, Y:0.1481, Z:0.0697<br>区间15:<br>  时长: 42.50秒<br>  陀螺仪数据个数: 406<br>  标准差 - X:0.0257, Y:0.0221, Z:0.0162<br>  极值差 - X:0.1742, Y:0.1655, Z:0.0958<br>区间16:<br>  时长: 87.80秒<br>  陀螺仪数据个数: 858<br>  标准差 - X:0.0232, Y:0.0202, Z:0.0150<br>  极值差 - X:0.1568, Y:0.1481, Z:0.0958<br>区间17:<br>  时长: 51.90秒<br>  陀螺仪数据个数: 500<br>  标准差 - X:0.0226, Y:0.0194, Z:0.0151<br>  极值差 - X:0.1568, Y:0.1132, Z:0.0958<br>区间18:<br>  时长: 20.90秒<br>  陀螺仪数据个数: 189<br>  标准差 - X:0.0192, Y:0.0210, Z:0.0169<br>  极值差 - X:0.0958, Y:0.0958, Z:0.0784<br>区间19:<br>  时长: 57.00秒<br>  陀螺仪数据个数: 550<br>  标准差 - X:0.0227, Y:0.0202, Z:0.0149<br>  极值差 - X:0.1655, Y:0.1306, Z:0.0871<br>区间20:<br>  时长: 7.30秒<br>  陀螺仪数据个数: 52<br>  标准差 - X:0.0219, Y:0.0211, Z:0.0161<br>  极值差 - X:0.0871, Y:0.0958, Z:0.0523<br>区间21:<br>  时长: 27.80秒<br>  陀螺仪数据个数: 258<br>  标准差 - X:0.0334, Y:0.0235, Z:0.0188<br>  极值差 - X:0.2351, Y:0.1829, Z:0.1742<br>区间22:<br>  时长: 12.10秒<br>  陀螺仪数据个数: 102<br>  标准差 - X:0.0238, Y:0.0192, Z:0.0145<br>  极值差 - X:0.1132, Y:0.0871, Z:0.0784<br>区间23:<br>  时长: 74.90秒<br>  陀螺仪数据个数: 730<br>  标准差 - X:0.0236, Y:0.0208, Z:0.0154<br>  极值差 - X:0.1742, Y:0.1307, Z:0.0958<br>区间24:<br>  时长: 21.80秒<br>  陀螺仪数据个数: 198<br>  标准差 - X:0.0243, Y:0.0200, Z:0.0151<br>  极值差 - X:0.1568, Y:0.0958, Z:0.0697<br>区间25:<br>  时长: 6.50秒<br>  陀螺仪数据个数: 46<br>  标准差 - X:0.0199, Y:0.0194, Z:0.0132<br>  极值差 - X:0.0784, Y:0.0784, Z:0.0522", "checkRules": "所有静态区间(时长>2秒)的标准差<0.5dps<br>且极值差<1.5dps", "description": "不通过需手动排查"}]}