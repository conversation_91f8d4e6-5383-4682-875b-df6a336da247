<html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>                <div id="bdbaf29a-4cf9-4773-ab6f-80f8b92b99d9" class="plotly-graph-div" style="height:1000px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("bdbaf29a-4cf9-4773-ab6f-80f8b92b99d9")) {                    Plotly.newPlot(                        "bdbaf29a-4cf9-4773-ab6f-80f8b92b99d9",                        [{"cells":{"align":["left","center","left","center","center"],"fill":{"color":[["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"]]},"format":[null,null,"html",null,null],"values":[["原始轮速误差检查","轮速尺度因子检查","标定轮速误差检查（全场景）","标定轮速误差检查(转弯或掉头)","误差超限积分检查"],["\u003cbr\u003e轮速误差均值:0.48\u003cbr\u003e轮速误差中位数:0.29","\u003cbr\u003e轮速尺度因子均值:1.02                    \u003cbr\u003e轮速尺度因子中位数:1.02                    \u003cbr\u003e16%分位轮速尺度因子:0.99                    \u003cbr\u003e84%分位轮速尺度因子:1.04                    \u003cbr\u003e轮速尺度因子均值检查结果:True                    \u003cbr\u003e轮速尺度因子中位数检查结果:True                    \u003cbr\u003e16%分位轮速尺度因子检查结果:True                    \u003cbr\u003e84%分位轮速尺度因子检查结果:True","\u003cbr\u003e轮速误差均值:0.13\u003cbr\u003e轮速误差中位数:0.03","\u003cbr\u003e轮速误差均值:-0.32\u003cbr\u003e轮速误差中位数:-0.11","\u003cbr\u003e超限区间误差积分最大累计值:9.28"],["误差均值或中位数在正负1km\u002fh之内","误均值或中位数在0.95~1.05范围\u003cbr\u003e尺度因子的16%或84%分位数在0.95~1.05范围之内","误差均值或中位数在正负1km\u002fh之内","误差均值或中位数在正负1km\u002fh之内","误差超限区间内轮速误差积分小于3.5米（全场景）"],["Pass","Pass","Pass","Pass","Fail"],["","","","",""]]},"header":{"align":"center","fill":{"color":"paleturquoise"},"values":["检查项","统计数值","检查规则","检查结果","描述"]},"type":"table","domain":{"x":[0.0,1.0],"y":[0.525,1.0]}},{"cells":{"align":"center","fill":{"color":[["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"]]},"values":[["原始轮速误差检查","轮速尺度因子检查","标定轮速误差检查（全场景）","标定轮速误差检查(转弯或掉头)","误差超限积分检查"],["[\n  {\n    \"pulse_sourceTickTime\": 116751119,\n    \"pulse_speed\": 51.1875,\n    \"gt_speed\": 54.809502043076584,\n    \"speed_diff\": 3.6220020430765842,\n    \"angular_velocity\": -0.8805100000000721\n  },\n  {\n    \"pulse_sourceTickTime\": 116751218,\n    \"pulse_speed\": 50.849998474121094,\n    \"gt_speed\": 54.662739306770874,\n    \"speed_diff\": 3.81274083264978,\n    \"angular_velocity\": -0.5277199999999027\n  },\n  {\n    \"pulse_sourceTickTime\": 116751317,\n    \"pulse_speed\": 50.23124694824219,\n    \"gt_speed\": 54.44595642993696,\n    \"speed_diff\": 4.214709481694776,\n    \"angular_velocity\": -0.34569000000004735\n  },\n  {\n    \"pulse_sourceTickTime\": 116751420,\n    \"pulse_speed\": 49.61249923706055,\n    \"gt_speed\": 54.18331587941861,\n    \"speed_diff\": 4.570816642358061,\n    \"angular_velocity\": -0.3393599999999708\n  },\n  {\n    \"pulse_sourceTickTime\": 116751517,\n    \"pulse_speed\": 49.33124923706055,\n    \"gt_speed\": 53.924508286764684,\n    \"speed_diff\": 4.5932590497041375,\n    \"angular_velocity\": -0.3665841584158402\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 116611117,\n    \"pulse_speed\": 0.44999998807907104,\n    \"gt_speed\": 0.010118826450710895,\n    \"scale\": 0.022486281597262802\n  },\n  {\n    \"pulse_sourceTickTime\": 116611218,\n    \"pulse_speed\": 0.5625,\n    \"gt_speed\": 0.012992895735004135,\n    \"scale\": 0.023098481306674017\n  },\n  {\n    \"pulse_sourceTickTime\": 116611318,\n    \"pulse_speed\": 0.84375,\n    \"gt_speed\": 0.013150114821147285,\n    \"scale\": 0.015585321269507893\n  },\n  {\n    \"pulse_sourceTickTime\": 116611418,\n    \"pulse_speed\": 1.181249976158142,\n    \"gt_speed\": 0.021107384433771054,\n    \"scale\": 0.017868685595592564\n  },\n  {\n    \"pulse_sourceTickTime\": 116611518,\n    \"pulse_speed\": 1.40625,\n    \"gt_speed\": 0.06163306961656405,\n    \"scale\": 0.04382796061622332\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 117392119,\n    \"pulse_speed\": 72.89571290625001,\n    \"gt_speed\": 76.50587664737319,\n    \"speed_diff\": 3.6101637411231735,\n    \"angular_velocity\": 0.49988000000041666\n  },\n  {\n    \"pulse_sourceTickTime\": 117392317,\n    \"pulse_speed\": 71.47750837500001,\n    \"gt_speed\": 75.89195955212965,\n    \"speed_diff\": 4.414451177129635,\n    \"angular_velocity\": 0.6536900000003243\n  },\n  {\n    \"pulse_sourceTickTime\": 117392617,\n    \"pulse_speed\": 71.2505971888504,\n    \"gt_speed\": 75.00575414057671,\n    \"speed_diff\": 3.755156951726306,\n    \"angular_velocity\": 0.5752599999999575\n  },\n  {\n    \"pulse_sourceTickTime\": 117392717,\n    \"pulse_speed\": 71.03100250797272,\n    \"gt_speed\": 74.68078218004923,\n    \"speed_diff\": 3.6497796720765194,\n    \"angular_velocity\": 0.5565643564357805\n  },\n  {\n    \"pulse_sourceTickTime\": 117392817,\n    \"pulse_speed\": 70.6906044765091,\n    \"gt_speed\": 74.34365437944932,\n    \"speed_diff\": 3.653049902940225,\n    \"angular_velocity\": 0.5990600000001223\n  }\n]","[]","[\n  {\n    \"start_time\": 121776218,\n    \"end_time\": 121776518\n  }\n]"]]},"header":{"align":"center","fill":{"color":"paleturquoise"},"values":["检查项","检查详情"]},"type":"table","domain":{"x":[0.0,1.0],"y":[0.0,0.475]}}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"annotations":[{"font":{"size":16},"showarrow":false,"text":"轮速精度检查结果","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"轮速精度检查详情","x":0.5,"xanchor":"center","xref":"paper","y":0.475,"yanchor":"bottom","yref":"paper"}],"title":{"text":"\u003cb\u003e轮速精度检查结果汇总\u003c\u002fb\u003e","x":0.5},"margin":{"t":100,"b":50,"l":50,"r":50},"height":1000,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html>