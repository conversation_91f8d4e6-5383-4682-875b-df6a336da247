{"checker": "C12PulseAccuracy", "title": "轮速精度检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_100536/AutoSdkDemo/online/vlane/20250703_100536/data.txt", "version": "1.0", "date": "2025-07-03 17:09:40", "type": "CheckResult", "checkResult": [{"checkItem": "原始轮速误差检查", "checkResult": "Pass", "checkResultStatistic": "<br>轮速误差均值:0.48<br>轮速误差中位数:0.29", "checkRules": "误差均值或中位数在正负1km/h之内", "description": ""}, {"checkItem": "轮速尺度因子检查", "checkResult": "Pass", "checkResultStatistic": "<br>轮速尺度因子均值:1.02                    <br>轮速尺度因子中位数:1.02                    <br>16%分位轮速尺度因子:0.99                    <br>84%分位轮速尺度因子:1.04                    <br>轮速尺度因子均值检查结果:True                    <br>轮速尺度因子中位数检查结果:True                    <br>16%分位轮速尺度因子检查结果:True                    <br>84%分位轮速尺度因子检查结果:True", "checkRules": "误均值或中位数在0.95~1.05范围<br>尺度因子的16%或84%分位数在0.95~1.05范围之内", "description": ""}, {"checkItem": "标定轮速误差检查（全场景）", "checkResult": "Pass", "checkResultStatistic": "<br>轮速误差均值:0.13<br>轮速误差中位数:0.03", "checkRules": "误差均值或中位数在正负1km/h之内", "description": ""}, {"checkItem": "标定轮速误差检查(转弯或掉头)", "checkResult": "Pass", "checkResultStatistic": "<br>轮速误差均值:-0.32<br>轮速误差中位数:-0.11", "checkRules": "误差均值或中位数在正负1km/h之内", "description": ""}, {"checkItem": "误差超限积分检查", "checkResult": "Fail", "checkResultStatistic": "<br>超限区间误差积分最大累计值:9.28", "checkRules": "误差超限区间内轮速误差积分小于3.5米（全场景）", "description": ""}]}