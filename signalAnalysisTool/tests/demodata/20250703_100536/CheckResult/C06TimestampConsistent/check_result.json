{"checker": "C06TimestampConsistent", "title": "时间戳匹配检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_100536/AutoSdkDemo/online/vlane/20250703_100536/data.txt", "version": "1.0", "date": "2025-07-03 17:09:20", "type": "CheckResult", "checkResult": [{"checkItem": "acce3d时间戳匹配检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "sourceTicktime开始时间大于100ms\n", "description": ""}, {"checkItem": "gyro时间戳匹配检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "sourceTicktime开始时间大于100ms\n", "description": ""}, {"checkItem": "pulse时间戳匹配检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "sourceTicktime开始时间大于100ms\n", "description": ""}, {"checkItem": "gnss时间戳匹配检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "sourceTicktime开始时间大于100ms\n\n                     GNSS前后500ms有图像数据\n\n                     GNSS前后100ms要有IMU数据/车速数", "description": ""}, {"checkItem": "image时间戳匹配检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "sourceTicktime开始时间大于100ms\n\n                图像前后100ms要有IMU数据/车速数据", "description": ""}]}