{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250624_145130/AutoSdkDemo/online/vlane/20250624_145130/data.txt", "version": "1.0", "date": "2025-07-04 12:55:49", "type": "CheckResult", "checkResult": [{"checkItem": "加速度器静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 19<br>时长>2秒区间数: 19<br>通过区间数: 19<br>失败区间数: 0<br><br>区间1:<br>  时长: 10.10秒<br>  加速度器数据个数: 81<br>  标准差 - X:0.0010, Y:0.0010, Z:0.0010<br>  极值差 - X:0.0057, Y:0.0054, Z:0.0053<br>区间2:<br>  时长: 33.70秒<br>  加速度器数据个数: 317<br>  标准差 - X:0.0008, Y:0.0008, Z:0.0009<br>  极值差 - X:0.0053, Y:0.0053, Z:0.0057<br>区间3:<br>  时长: 43.20秒<br>  加速度器数据个数: 412<br>  标准差 - X:0.0009, Y:0.0009, Z:0.0010<br>  极值差 - X:0.0065, Y:0.0067, Z:0.0078<br>区间4:<br>  时长: 32.10秒<br>  加速度器数据个数: 301<br>  标准差 - X:0.0008, Y:0.0008, Z:0.0009<br>  极值差 - X:0.0057, Y:0.0062, Z:0.0047<br>区间5:<br>  时长: 5.20秒<br>  加速度器数据个数: 32<br>  标准差 - X:0.0010, Y:0.0012, Z:0.0014<br>  极值差 - X:0.0051, Y:0.0044, Z:0.0053<br>区间6:<br>  时长: 55.20秒<br>  加速度器数据个数: 532<br>  标准差 - X:0.0009, Y:0.0010, Z:0.0008<br>  极值差 - X:0.0063, Y:0.0112, Z:0.0066<br>区间7:<br>  时长: 25.70秒<br>  加速度器数据个数: 237<br>  标准差 - X:0.0010, Y:0.0015, Z:0.0011<br>  极值差 - X:0.0063, Y:0.0193, Z:0.0088<br>区间8:<br>  时长: 25.20秒<br>  加速度器数据个数: 232<br>  标准差 - X:0.0009, Y:0.0011, Z:0.0009<br>  极值差 - X:0.0049, Y:0.0075, Z:0.0069<br>区间9:<br>  时长: 7.80秒<br>  加速度器数据个数: 58<br>  标准差 - X:0.0008, Y:0.0009, Z:0.0010<br>  极值差 - X:0.0031, Y:0.0043, Z:0.0045<br>区间10:<br>  时长: 63.90秒<br>  加速度器数据个数: 619<br>  标准差 - X:0.0008, Y:0.0008, Z:0.0009<br>  极值差 - X:0.0073, Y:0.0056, Z:0.0067<br>区间11:<br>  时长: 13.50秒<br>  加速度器数据个数: 115<br>  标准差 - X:0.0008, Y:0.0012, Z:0.0009<br>  极值差 - X:0.0045, Y:0.0088, Z:0.0059<br>区间12:<br>  时长: 38.90秒<br>  加速度器数据个数: 369<br>  标准差 - X:0.0008, Y:0.0010, Z:0.0009<br>  极值差 - X:0.0050, Y:0.0098, Z:0.0067<br>区间13:<br>  时长: 28.10秒<br>  加速度器数据个数: 262<br>  标准差 - X:0.0009, Y:0.0012, Z:0.0009<br>  极值差 - X:0.0065, Y:0.0095, Z:0.0059<br>区间14:<br>  时长: 57.90秒<br>  加速度器数据个数: 559<br>  标准差 - X:0.0008, Y:0.0018, Z:0.0008<br>  极值差 - X:0.0049, Y:0.0257, Z:0.0069<br>区间15:<br>  时长: 14.90秒<br>  加速度器数据个数: 129<br>  标准差 - X:0.0008, Y:0.0009, Z:0.0010<br>  极值差 - X:0.0049, Y:0.0047, Z:0.0069<br>区间16:<br>  时长: 170.50秒<br>  加速度器数据个数: 1685<br>  标准差 - X:0.0008, Y:0.0008, Z:0.0009<br>  极值差 - X:0.0055, Y:0.0058, Z:0.0139<br>区间17:<br>  时长: 26.40秒<br>  加速度器数据个数: 244<br>  标准差 - X:0.0009, Y:0.0009, Z:0.0010<br>  极值差 - X:0.0053, Y:0.0061, Z:0.0065<br>区间18:<br>  时长: 83.10秒<br>  加速度器数据个数: 811<br>  标准差 - X:0.0008, Y:0.0011, Z:0.0012<br>  极值差 - X:0.0065, Y:0.0130, Z:0.0190<br>区间19:<br>  时长: 9.81秒<br>  加速度器数据个数: 78<br>  标准差 - X:0.0008, Y:0.0008, Z:0.0014<br>  极值差 - X:0.0041, Y:0.0037, Z:0.0070", "checkRules": "所有静态区间(时长>2秒)的标准差<0.1<br>且极值差<0.2dps", "description": "不通过需手动排查"}]}