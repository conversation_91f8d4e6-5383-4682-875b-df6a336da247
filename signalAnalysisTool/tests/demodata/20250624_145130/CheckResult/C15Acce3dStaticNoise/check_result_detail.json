{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查详情", "version": "1.0", "date": "2025-07-04 12:55:49", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "加速度器静态噪声过大", "static_intervals": [{"start_time": 525647151, "end_time": 525657251, "duration": 10.1, "start_time_adj": 525648151, "end_time_adj": 525656251}, {"start_time": 525933551, "end_time": 525967251, "duration": 33.7, "start_time_adj": 525934551, "end_time_adj": 525966251}, {"start_time": 526270851, "end_time": 526314051, "duration": 43.2, "start_time_adj": 526271851, "end_time_adj": 526313051}, {"start_time": 526799351, "end_time": 526831452, "duration": 32.101, "start_time_adj": 526800351, "end_time_adj": 526830452}, {"start_time": 526895651, "end_time": 526900851, "duration": 5.2, "start_time_adj": 526896651, "end_time_adj": 526899851}, {"start_time": 526901751, "end_time": 526956951, "duration": 55.2, "start_time_adj": 526902751, "end_time_adj": 526955951}, {"start_time": 527245751, "end_time": 527271451, "duration": 25.7, "start_time_adj": 527246751, "end_time_adj": 527270451}, {"start_time": 527306351, "end_time": 527331551, "duration": 25.2, "start_time_adj": 527307351, "end_time_adj": 527330551}, {"start_time": 527584951, "end_time": 527592751, "duration": 7.8, "start_time_adj": 527585951, "end_time_adj": 527591751}, {"start_time": 527641451, "end_time": 527705351, "duration": 63.9, "start_time_adj": 527642451, "end_time_adj": 527704351}, {"start_time": 527998051, "end_time": 528011551, "duration": 13.5, "start_time_adj": 527999051, "end_time_adj": 528010551}, {"start_time": 528069451, "end_time": 528108351, "duration": 38.9, "start_time_adj": 528070451, "end_time_adj": 528107351}, {"start_time": 528226851, "end_time": 528254951, "duration": 28.1, "start_time_adj": 528227851, "end_time_adj": 528253951}, {"start_time": 528289751, "end_time": 528347651, "duration": 57.9, "start_time_adj": 528290751, "end_time_adj": 528346651}, {"start_time": 528437551, "end_time": 528452451, "duration": 14.9, "start_time_adj": 528438551, "end_time_adj": 528451451}, {"start_time": 528538451, "end_time": 528708952, "duration": 170.501, "start_time_adj": 528539451, "end_time_adj": 528707952}, {"start_time": 528777951, "end_time": 528804351, "duration": 26.4, "start_time_adj": 528778951, "end_time_adj": 528803351}, {"start_time": 528918851, "end_time": 529001951, "duration": 83.1, "start_time_adj": 528919851, "end_time_adj": 529000951}, {"start_time": 529112151, "end_time": 529121957, "duration": 9.806, "start_time_adj": 529113151, "end_time_adj": 529120957}], "long_static_intervals": [{"start_time": 525647151, "end_time": 525657251, "duration": 10.1, "gyro_count": 81, "mean_x": -0.013192630028007206, "mean_y": -0.007101598655644022, "mean_z": 1.0012735878979717, "std_x": 0.0010039448470270278, "std_y": 0.001012843852313849, "std_z": 0.0010400731727108184, "range_x": 0.005708531476557255, "range_y": 0.005402626935392618, "range_z": 0.0053008198738098145}, {"start_time": 525933551, "end_time": 525967251, "duration": 33.7, "gyro_count": 317, "mean_x": -0.01990983443046782, "mean_y": -0.009593315957237494, "mean_z": 1.001131260056601, "std_x": 0.0008396578101822289, "std_y": 0.0007750780588095623, "std_z": 0.0008946863049391116, "range_x": 0.005300737917423248, "range_y": 0.005300738383084536, "range_z": 0.005708515644073486}, {"start_time": 526270851, "end_time": 526314051, "duration": 43.2, "gyro_count": 412, "mean_x": -0.018089084160396632, "mean_y": -0.014035113639587048, "mean_z": 1.0010678703055798, "std_x": 0.0008540808457320005, "std_y": 0.0008974564471730459, "std_z": 0.0010265450442257982, "range_x": 0.00652400404214859, "range_y": 0.00672778207808733, "range_z": 0.007849156856536865}, {"start_time": 526799351, "end_time": 526831452, "duration": 32.101, "gyro_count": 301, "mean_x": -0.02342142200598685, "mean_y": -0.008243334256855158, "mean_z": 1.0009621255025516, "std_x": 0.000816264488489068, "std_y": 0.0007918464945094576, "std_z": 0.0008933021465789377, "range_x": 0.005708532407879829, "range_y": 0.006218097172677517, "range_z": 0.004689157009124756}, {"start_time": 526895651, "end_time": 526900851, "duration": 5.2, "gyro_count": 32, "mean_x": -0.029122075531631708, "mean_y": -0.004959859747032169, "mean_z": 1.0008792281150818, "std_x": 0.0010129148811853562, "std_y": 0.0012165267258235236, "std_z": 0.0014345595484304787, "range_x": 0.005096839740872383, "range_y": 0.004383259220048785, "range_z": 0.0053008198738098145}, {"start_time": 526901751, "end_time": 526956951, "duration": 55.2, "gyro_count": 532, "mean_x": -0.02889678372014174, "mean_y": -0.003954458045588301, "mean_z": 1.000858410520661, "std_x": 0.0009007010563129816, "std_y": 0.0010057369871279453, "std_z": 0.0008475862757741268, "range_x": 0.006320105865597725, "range_y": 0.011213048128411174, "range_z": 0.006625950336456299}, {"start_time": 527245751, "end_time": 527271451, "duration": 25.7, "gyro_count": 237, "mean_x": -0.022225232983503162, "mean_y": -0.005111031912616945, "mean_z": 1.0011694921722896, "std_x": 0.0010426619853753667, "std_y": 0.001451925880512566, "std_z": 0.0011256356333554712, "range_x": 0.006320105865597725, "range_y": 0.019265984185039997, "range_z": 0.008766591548919678}, {"start_time": 527306351, "end_time": 527331551, "duration": 25.2, "gyro_count": 232, "mean_x": -0.01922738803390028, "mean_y": -0.004981284777198292, "mean_z": 1.001138461304122, "std_x": 0.0009067698067805477, "std_y": 0.001064206793803124, "std_z": 0.0009499349424080635, "range_x": 0.004892943426966667, "range_y": 0.007543252489995211, "range_z": 0.006931781768798828}, {"start_time": 527584951, "end_time": 527592751, "duration": 7.8, "gyro_count": 58, "mean_x": -0.01294598182470634, "mean_y": -0.009659398427425787, "mean_z": 1.0012496124053825, "std_x": 0.0008427636670547775, "std_y": 0.000883587698268204, "std_z": 0.0010126383901749147, "range_x": 0.00305810384452343, "range_y": 0.004281369037926197, "range_z": 0.0044852495193481445}, {"start_time": 527641451, "end_time": 527705351, "duration": 63.9, "gyro_count": 619, "mean_x": -0.012072841867034192, "mean_y": -0.007816032666761145, "mean_z": 1.0010934935247764, "std_x": 0.000811258366829426, "std_y": 0.0008015579713974876, "std_z": 0.0009063340677074702, "range_x": 0.007339354604482651, "range_y": 0.005606524180620909, "range_z": 0.006727933883666992}, {"start_time": 527998051, "end_time": 528011551, "duration": 13.5, "gyro_count": 115, "mean_x": -0.011843285652930321, "mean_y": -0.008840138864015107, "mean_z": 1.0012463051339855, "std_x": 0.0008274266395172402, "std_y": 0.0012250738173386145, "std_z": 0.0009164348322093675, "range_x": 0.004485267214477062, "range_y": 0.008766637183725834, "range_z": 0.005912303924560547}, {"start_time": 528069451, "end_time": 528108351, "duration": 38.9, "gyro_count": 369, "mean_x": -0.02054758698535644, "mean_y": -0.01809308999457818, "mean_z": 1.000929047098651, "std_x": 0.0007819510148979319, "std_y": 0.0009818990526545886, "std_z": 0.0008843174955800704, "range_x": 0.00499483197927475, "range_y": 0.009786004200577736, "range_z": 0.006727874279022217}, {"start_time": 528226851, "end_time": 528254951, "duration": 28.1, "gyro_count": 262, "mean_x": -0.018334617393930223, "mean_y": -0.008286451020647722, "mean_z": 1.0010933059317466, "std_x": 0.0008846929568056352, "std_y": 0.001167893212958818, "std_z": 0.0008949614271479294, "range_x": 0.006523885764181614, "range_y": 0.009480098495259881, "range_z": 0.005912482738494873}, {"start_time": 528289751, "end_time": 528347651, "duration": 57.9, "gyro_count": 559, "mean_x": -0.016089054112770795, "mean_y": -0.008845342149914698, "mean_z": 1.0010983483309397, "std_x": 0.0008164926907000931, "std_y": 0.001772838045874739, "std_z": 0.0008440323837199505, "range_x": 0.004892943426966667, "range_y": 0.02568809874355793, "range_z": 0.006931722164154053}, {"start_time": 528437551, "end_time": 528452451, "duration": 14.9, "gyro_count": 129, "mean_x": -0.02037234288142171, "mean_y": -0.005476931723686614, "mean_z": 1.0010509966879853, "std_x": 0.0007936077402352932, "std_y": 0.00091029720839674, "std_z": 0.0010182346343401124, "range_x": 0.004892941564321518, "range_y": 0.004689045017585158, "range_z": 0.006931662559509277}, {"start_time": 528538451, "end_time": 528708952, "duration": 170.501, "gyro_count": 1685, "mean_x": -0.01688848012942235, "mean_y": -0.014321969403644875, "mean_z": 1.0009852363023277, "std_x": 0.000755831333527191, "std_y": 0.0007553598498550562, "std_z": 0.0008585725730920026, "range_x": 0.005504634231328964, "range_y": 0.005810302682220936, "range_z": 0.01386338472366333}, {"start_time": 528777951, "end_time": 528804351, "duration": 26.4, "gyro_count": 244, "mean_x": -0.014591167261060632, "mean_y": -0.02192184137424729, "mean_z": 1.000859374882745, "std_x": 0.0009412718504243456, "std_y": 0.0009227131011270308, "std_z": 0.0010303838944929076, "range_x": 0.005300736986100674, "range_y": 0.00611620768904686, "range_z": 0.006524026393890381}, {"start_time": 528918851, "end_time": 529001951, "duration": 83.1, "gyro_count": 811, "mean_x": -0.018015668944498173, "mean_y": -0.007989785448807659, "mean_z": 1.001096303233677, "std_x": 0.0008023021812748074, "std_y": 0.0011085393815555371, "std_z": 0.0011619467096257072, "range_x": 0.006523885764181614, "range_y": 0.013047886779531837, "range_z": 0.018960237503051758}, {"start_time": 529112151, "end_time": 529121957, "duration": 9.806, "gyro_count": 78, "mean_x": -0.022047101639402218, "mean_y": -0.006782715220768482, "mean_z": 1.0008050440213618, "std_x": 0.0007802930136624829, "std_y": 0.0008074587076674569, "std_z": 0.0013702470726952346, "range_x": 0.004077473655343056, "range_y": 0.0036696777679026127, "range_z": 0.007033646106719971}], "failed_intervals": [], "all_passed": true}]}