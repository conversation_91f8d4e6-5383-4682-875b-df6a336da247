{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250624_102902/AutoSdkDemo/online/vlane/20250624_102902/data.txt", "version": "1.0", "date": "2025-07-04 13:42:37", "type": "CheckResult", "checkResult": [{"checkItem": "加速度器静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 4<br>时长>2秒区间数: 4<br>通过区间数: 4<br>失败区间数: 0<br><br>区间1:<br>  时长: 5.30秒<br>  加速度器数据个数: 33<br>  标准差 - X:0.0008, Y:0.0011, Z:0.0018<br>  极值差 - X:0.0035, Y:0.0042, Z:0.0082<br>区间2:<br>  时长: 18.90秒<br>  加速度器数据个数: 169<br>  标准差 - X:0.0009, Y:0.0010, Z:0.0010<br>  极值差 - X:0.0049, Y:0.0061, Z:0.0059<br>区间3:<br>  时长: 7.20秒<br>  加速度器数据个数: 52<br>  标准差 - X:0.0007, Y:0.0011, Z:0.0009<br>  极值差 - X:0.0031, Y:0.0061, Z:0.0049<br>区间4:<br>  时长: 25.00秒<br>  加速度器数据个数: 230<br>  标准差 - X:0.0007, Y:0.0010, Z:0.0016<br>  极值差 - X:0.0047, Y:0.0063, Z:0.0084", "checkRules": "所有静态区间(时长>2秒)的标准差<0.1<br>且极值差<0.2dps", "description": "不通过需手动排查"}]}