{"checker": "C06TimestampConsistent", "title": "时间戳匹配检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250624_102902/AutoSdkDemo/online/vlane/20250624_102902/data.txt", "version": "1.0", "date": "2025-07-04 13:41:35", "type": "CheckResult", "checkResult": [{"checkItem": "acce3d时间戳匹配检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "sourceTicktime开始时间大于{sourceTickTime_start_thresh}s\n", "description": ""}, {"checkItem": "gyro时间戳匹配检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "sourceTicktime开始时间大于{sourceTickTime_start_thresh}ms\n", "description": ""}, {"checkItem": "pulse时间戳匹配检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "sourceTicktime开始时间大于{sourceTickTime_start_thresh}ms\n", "description": ""}, {"checkItem": "gnss时间戳匹配检查", "checkResult": "Fail", "checkResultStatistic": 0.001040582726326743, "checkRules": "sourceTicktime开始时间大于{sourceTickTime_start_thresh}ms\n\n                     GNSS前后{gnss_img_time_thresh}ms有图像数据\n\n                     GNSS前后{gnss_imu_time_thresh}ms要有IMU数据/车速数", "description": ""}, {"checkItem": "image时间戳匹配检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "sourceTicktime开始时间大于{sourceTickTime_start_thresh}ms\n\n                图像前后100ms要有IMU数据/车速数据", "description": ""}]}