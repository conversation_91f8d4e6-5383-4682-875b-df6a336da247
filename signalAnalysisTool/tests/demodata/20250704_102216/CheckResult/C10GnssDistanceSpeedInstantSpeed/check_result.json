{"checker": "C10GnssDistanceSpeedInstantSpeed", "title": "GNSS距离速度和GNSS瞬时速度一致性检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250704_102216/AutoSdkDemo/online/vlane/20250704_102216/data.txt", "version": "1.0", "date": "2025-07-07 18:21:26", "type": "CheckResult", "checkResult": [{"checkItem": "median", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "阈值:[-1,1]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_16", "checkResult": "Pass", "checkResultStatistic": -0.8229781230264186, "checkRules": "阈值:[-3.6,3.6]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_84", "checkResult": "Pass", "checkResultStatistic": 0.832498060656739, "checkRules": "阈值:[-3.6,3.6]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_2_5", "checkResult": "Pass", "checkResultStatistic": -2.6362617066418266, "checkRules": "阈值:[-7.2,7.2]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_97_5", "checkResult": "Pass", "checkResultStatistic": 2.610472234643494, "checkRules": "阈值:[-7.2,7.2]", "description": "不通过需手动排查"}]}