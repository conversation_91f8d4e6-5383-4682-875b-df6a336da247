{"checker": "C14GryoStaticNoise", "title": "陀螺仪静态噪声检查详情", "version": "1.0", "date": "2025-07-07 18:25:51", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "陀螺静态噪声过大", "static_intervals": [{"start_time": 59477072, "end_time": 59565795, "duration": 88.723, "start_time_adj": 59478072, "end_time_adj": 59564795}, {"start_time": 60081496, "end_time": 60104798, "duration": 23.302, "start_time_adj": 60082496, "end_time_adj": 60103798}, {"start_time": 60172396, "end_time": 60219896, "duration": 47.5, "start_time_adj": 60173396, "end_time_adj": 60218896}, {"start_time": 60308896, "end_time": 60329096, "duration": 20.2, "start_time_adj": 60309896, "end_time_adj": 60328096}, {"start_time": 60821596, "end_time": 60827495, "duration": 5.899, "start_time_adj": 60822596, "end_time_adj": 60826495}, {"start_time": 61260995, "end_time": 61331996, "duration": 71.001, "start_time_adj": 61261995, "end_time_adj": 61330996}, {"start_time": 61364996, "end_time": 61381195, "duration": 16.199, "start_time_adj": 61365996, "end_time_adj": 61380195}, {"start_time": 61421098, "end_time": 61463695, "duration": 42.597, "start_time_adj": 61422098, "end_time_adj": 61462695}, {"start_time": 61497695, "end_time": 61503798, "duration": 6.103, "start_time_adj": 61498695, "end_time_adj": 61502798}, {"start_time": 61608296, "end_time": 61633996, "duration": 25.7, "start_time_adj": 61609296, "end_time_adj": 61632996}, {"start_time": 61761495, "end_time": 61833896, "duration": 72.401, "start_time_adj": 61762495, "end_time_adj": 61832896}, {"start_time": 62141396, "end_time": 62161096, "duration": 19.7, "start_time_adj": 62142396, "end_time_adj": 62160096}, {"start_time": 62164396, "end_time": 62174996, "duration": 10.6, "start_time_adj": 62165396, "end_time_adj": 62173996}, {"start_time": 62223196, "end_time": 62229895, "duration": 6.699, "start_time_adj": 62224196, "end_time_adj": 62228895}, {"start_time": 62305696, "end_time": 62329896, "duration": 24.2, "start_time_adj": 62306696, "end_time_adj": 62328896}, {"start_time": 62411296, "end_time": 62473595, "duration": 62.299, "start_time_adj": 62412296, "end_time_adj": 62472595}, {"start_time": 62501595, "end_time": 62567896, "duration": 66.301, "start_time_adj": 62502595, "end_time_adj": 62566896}, {"start_time": 62599196, "end_time": 62608595, "duration": 9.399, "start_time_adj": 62600196, "end_time_adj": 62607595}, {"start_time": 62614096, "end_time": 62716996, "duration": 102.9, "start_time_adj": 62615096, "end_time_adj": 62715996}, {"start_time": 62723195, "end_time": 62730995, "duration": 7.8, "start_time_adj": 62724195, "end_time_adj": 62729995}, {"start_time": 62735799, "end_time": 62748096, "duration": 12.297, "start_time_adj": 62736799, "end_time_adj": 62747096}, {"start_time": 62752796, "end_time": 62778496, "duration": 25.7, "start_time_adj": 62753796, "end_time_adj": 62777496}, {"start_time": 62780595, "end_time": 62790997, "duration": 10.402, "start_time_adj": 62781595, "end_time_adj": 62789997}, {"start_time": 62800496, "end_time": 62805896, "duration": 5.4, "start_time_adj": 62801496, "end_time_adj": 62804896}, {"start_time": 62808897, "end_time": 62812996, "duration": 4.099, "start_time_adj": 62809897, "end_time_adj": 62811996}, {"start_time": 62819596, "end_time": 62840295, "duration": 20.699, "start_time_adj": 62820596, "end_time_adj": 62839295}, {"start_time": 63243295, "end_time": 63247995, "duration": 4.7, "start_time_adj": 63244295, "end_time_adj": 63246995}, {"start_time": 63489596, "end_time": 63509295, "duration": 19.699, "start_time_adj": 63490596, "end_time_adj": 63508295}, {"start_time": 63539095, "end_time": 63548095, "duration": 9.0, "start_time_adj": 63540095, "end_time_adj": 63547095}, {"start_time": 63680097, "end_time": 63773396, "duration": 93.299, "start_time_adj": 63681097, "end_time_adj": 63772396}, {"start_time": 63830096, "end_time": 63882095, "duration": 51.999, "start_time_adj": 63831096, "end_time_adj": 63881095}, {"start_time": 64039496, "end_time": 64045798, "duration": 6.302, "start_time_adj": 64040496, "end_time_adj": 64044798}, {"start_time": 64050696, "end_time": 64069997, "duration": 19.301, "start_time_adj": 64051696, "end_time_adj": 64068997}, {"start_time": 64079895, "end_time": 64175396, "duration": 95.501, "start_time_adj": 64080895, "end_time_adj": 64174396}, {"start_time": 64246396, "end_time": 64261195, "duration": 14.799, "start_time_adj": 64247396, "end_time_adj": 64260195}, {"start_time": 64265996, "end_time": 64341996, "duration": 76.0, "start_time_adj": 64266996, "end_time_adj": 64340996}, {"start_time": 64460197, "end_time": 64520495, "duration": 60.298, "start_time_adj": 64461197, "end_time_adj": 64519495}, {"start_time": 64576995, "end_time": 64611195, "duration": 34.2, "start_time_adj": 64577995, "end_time_adj": 64610195}, {"start_time": 65068795, "end_time": 65103695, "duration": 34.9, "start_time_adj": 65069795, "end_time_adj": 65102695}, {"start_time": 65302196, "end_time": 65351396, "duration": 49.2, "start_time_adj": 65303196, "end_time_adj": 65350396}], "long_static_intervals": [{"start_time": 59477072, "end_time": 59565795, "duration": 88.723, "gyro_count": 868, "mean_x": 0.6648594842803094, "mean_y": -1.3727542569835065, "mean_z": -0.49855888785991803, "std_x": 0.030463686043450636, "std_y": 0.023597000322971438, "std_z": 0.017664105596428573, "range_x": 0.4093349575996399, "range_y": 0.20900368690490723, "range_z": 0.1741945743560791}, {"start_time": 60081496, "end_time": 60104798, "duration": 23.302, "gyro_count": 214, "mean_x": 0.6614734315983603, "mean_y": -1.3751618009861384, "mean_z": -0.5040201106082614, "std_x": 0.03965168985711279, "std_y": 0.022854544826809337, "std_z": 0.016638036196404183, "range_x": 0.4354715943336487, "range_y": 0.1741647720336914, "range_z": 0.07838010787963867}, {"start_time": 60172396, "end_time": 60219896, "duration": 47.5, "gyro_count": 456, "mean_x": 0.6628722332810101, "mean_y": -1.3729405029301058, "mean_z": -0.5028277535579706, "std_x": 0.023945729667122724, "std_y": 0.020233202447113397, "std_z": 0.01562079463117418, "range_x": 0.1480579972267151, "range_y": 0.13932597637176514, "range_z": 0.09578463435173035}, {"start_time": 60308896, "end_time": 60329096, "duration": 20.2, "gyro_count": 183, "mean_x": 0.665924450087417, "mean_y": -1.3704000867781092, "mean_z": -0.5018357981423862, "std_x": 0.021249074703271337, "std_y": 0.023892330041226328, "std_z": 0.015094211361582368, "range_x": 0.10448700189590454, "range_y": 0.14805805683135986, "range_z": 0.060975581407547}, {"start_time": 60821596, "end_time": 60827495, "duration": 5.899, "gyro_count": 40, "mean_x": 0.6686292737722397, "mean_y": -1.3764508813619614, "mean_z": -0.5070761151611805, "std_x": 0.021647046628041493, "std_y": 0.02077314205583817, "std_z": 0.01583647660229865, "range_x": 0.0870823860168457, "range_y": 0.11321902275085449, "range_z": 0.06097555160522461}, {"start_time": 61260995, "end_time": 61331996, "duration": 71.001, "gyro_count": 691, "mean_x": 0.6609475213263384, "mean_y": -1.3838046521767866, "mean_z": -0.5030652991079559, "std_x": 0.026268798937610232, "std_y": 0.02407641360171635, "std_z": 0.017498517357432034, "range_x": 0.2351403832435608, "range_y": 0.21770596504211426, "range_z": 0.12192127108573914}, {"start_time": 61364996, "end_time": 61381195, "duration": 16.199, "gyro_count": 142, "mean_x": 0.6669682327290656, "mean_y": -1.3794509197624636, "mean_z": -0.5038947574269603, "std_x": 0.0264080378456283, "std_y": 0.024280592403882622, "std_z": 0.017861083676184754, "range_x": 0.13065344095230103, "range_y": 0.14805805683135986, "range_z": 0.07838010787963867}, {"start_time": 61421098, "end_time": 61463695, "duration": 42.597, "gyro_count": 406, "mean_x": 0.6662978121799789, "mean_y": -1.3842523820294534, "mean_z": -0.5036187577130172, "std_x": 0.026883681841969185, "std_y": 0.02570659318076548, "std_z": 0.016824727217874876, "range_x": 0.17416471242904663, "range_y": 0.20030152797698975, "range_z": 0.121951162815094}, {"start_time": 61497695, "end_time": 61503798, "duration": 6.103, "gyro_count": 41, "mean_x": 0.6712253704303648, "mean_y": -1.3828140148302404, "mean_z": -0.5061808424751933, "std_x": 0.021804309692707944, "std_y": 0.021729831537529335, "std_z": 0.02025302657020961, "range_x": 0.0870823860168457, "range_y": 0.08711206912994385, "range_z": 0.0870823860168457}, {"start_time": 61608296, "end_time": 61633996, "duration": 25.7, "gyro_count": 238, "mean_x": 0.6661230030180025, "mean_y": -1.3795993994264042, "mean_z": -0.5023388356721702, "std_x": 0.024115344227945704, "std_y": 0.020188089833687754, "std_z": 0.015875447855710965, "range_x": 0.1219213604927063, "range_y": 0.10451662540435791, "range_z": 0.07838010787963867}, {"start_time": 61761495, "end_time": 61833896, "duration": 72.401, "gyro_count": 704, "mean_x": 0.6656775644726374, "mean_y": -1.3841808243570002, "mean_z": -0.5027077621357008, "std_x": 0.02160006396413035, "std_y": 0.020123992517775285, "std_z": 0.015137622246839861, "range_x": 0.13935565948486328, "range_y": 0.1306236982345581, "range_z": 0.0870823860168457}, {"start_time": 62141396, "end_time": 62161096, "duration": 19.7, "gyro_count": 178, "mean_x": 0.662467311607318, "mean_y": -1.379734060067809, "mean_z": -0.5014508897334002, "std_x": 0.03862206573101477, "std_y": 0.03862539441238132, "std_z": 0.021444871048874953, "range_x": 0.22643810510635376, "range_y": 0.38319826126098633, "range_z": 0.11321905255317688}, {"start_time": 62164396, "end_time": 62174996, "duration": 10.6, "gyro_count": 87, "mean_x": 0.6628817744638728, "mean_y": -1.3780176680663536, "mean_z": -0.5025175391942606, "std_x": 0.02871245660050023, "std_y": 0.020389344144727574, "std_z": 0.016802505272690352, "range_x": 0.1480579972267151, "range_y": 0.08711206912994385, "range_z": 0.0870823860168457}, {"start_time": 62223196, "end_time": 62229895, "duration": 6.699, "gyro_count": 46, "mean_x": 0.6688839948695638, "mean_y": -1.385861186877541, "mean_z": -0.5034150306297385, "std_x": 0.031138718552047463, "std_y": 0.02098468137755892, "std_z": 0.0144102566416725, "range_x": 0.13065332174301147, "range_y": 0.10451674461364746, "range_z": 0.06967779994010925}, {"start_time": 62305696, "end_time": 62329896, "duration": 24.2, "gyro_count": 223, "mean_x": 0.6627009037898794, "mean_y": -1.381443555045021, "mean_z": -0.5009419217772548, "std_x": 0.02723346577042749, "std_y": 0.023716162413950914, "std_z": 0.016852774638336235, "range_x": 0.16546255350112915, "range_y": 0.13932585716247559, "range_z": 0.09578463435173035}, {"start_time": 62411296, "end_time": 62473595, "duration": 62.299, "gyro_count": 603, "mean_x": 0.6572003445617397, "mean_y": -1.3811279487847097, "mean_z": -0.4966722334399943, "std_x": 0.025800447339785735, "std_y": 0.0223652354241641, "std_z": 0.01756552456926127, "range_x": 0.1741647720336914, "range_y": 0.1480579376220703, "range_z": 0.10451674461364746}, {"start_time": 62501595, "end_time": 62567896, "duration": 66.301, "gyro_count": 644, "mean_x": 0.656808181393961, "mean_y": -1.3783415058014556, "mean_z": -0.4967364799346983, "std_x": 0.025243466298518754, "std_y": 0.020376228344416183, "std_z": 0.01625704895384893, "range_x": 0.1741647720336914, "range_y": 0.1306236982345581, "range_z": 0.11321902275085449}, {"start_time": 62599196, "end_time": 62608595, "duration": 9.399, "gyro_count": 74, "mean_x": 0.6565842475440051, "mean_y": -1.3814303311141762, "mean_z": -0.49959004569698023, "std_x": 0.03292878075456466, "std_y": 0.020115118068687723, "std_z": 0.01615208122964087, "range_x": 0.1654624342918396, "range_y": 0.07837998867034912, "range_z": 0.07838013768196106}, {"start_time": 62614096, "end_time": 62716996, "duration": 102.9, "gyro_count": 1010, "mean_x": 0.6577848459234332, "mean_y": -1.383215897862274, "mean_z": -0.4981622785034746, "std_x": 0.02898557406555262, "std_y": 0.022639442789511615, "std_z": 0.01899786991508692, "range_x": 0.24384266138076782, "range_y": 0.17416489124298096, "range_z": 0.1480579674243927}, {"start_time": 62723195, "end_time": 62730995, "duration": 7.8, "gyro_count": 58, "mean_x": 0.6615802378490053, "mean_y": -1.3812688609649395, "mean_z": -0.49866583583683805, "std_x": 0.01859585996385299, "std_y": 0.015978783289048816, "std_z": 0.01947610980224435, "range_x": 0.07838010787963867, "range_y": 0.0696479082107544, "range_z": 0.08708235621452332}, {"start_time": 62735799, "end_time": 62748096, "duration": 12.297, "gyro_count": 103, "mean_x": 0.6578214776168749, "mean_y": -1.3791427461846362, "mean_z": -0.4981872210803541, "std_x": 0.021041631521474775, "std_y": 0.01799817128753363, "std_z": 0.01691214210078505, "range_x": 0.10448700189590454, "range_y": 0.0696479082107544, "range_z": 0.07838013768196106}, {"start_time": 62752796, "end_time": 62778496, "duration": 25.7, "gyro_count": 238, "mean_x": 0.6572338996314201, "mean_y": -1.382601552650708, "mean_z": -0.4966697543859482, "std_x": 0.0244834264542638, "std_y": 0.019300290421331495, "std_z": 0.014429314463550738, "range_x": 0.1480579972267151, "range_y": 0.11321902275085449, "range_z": 0.08708235621452332}, {"start_time": 62780595, "end_time": 62790997, "duration": 10.402, "gyro_count": 85, "mean_x": 0.6610607603017021, "mean_y": -1.3831889573265523, "mean_z": -0.49702667664079103, "std_x": 0.021441939001372815, "std_y": 0.020431973326645324, "std_z": 0.014582042399462826, "range_x": 0.10451668500900269, "range_y": 0.09581434726715088, "range_z": 0.06967788934707642}, {"start_time": 62800496, "end_time": 62805896, "duration": 5.4, "gyro_count": 35, "mean_x": 0.663122136252267, "mean_y": -1.3760167155947005, "mean_z": -0.5061132848262787, "std_x": 0.033738815041947326, "std_y": 0.020148373208449484, "std_z": 0.01619139452468756, "range_x": 0.13935565948486328, "range_y": 0.07837998867034912, "range_z": 0.06967779994010925}, {"start_time": 62808897, "end_time": 62812996, "duration": 4.099, "gyro_count": 21, "mean_x": 0.6548308503060114, "mean_y": -1.3776734386171614, "mean_z": -0.4885296296505701, "std_x": 0.02108329127191283, "std_y": 0.018187185248988963, "std_z": 0.01650290029796602, "range_x": 0.08708244562149048, "range_y": 0.06094563007354736, "range_z": 0.05224350094795227}, {"start_time": 62819596, "end_time": 62840295, "duration": 20.699, "gyro_count": 187, "mean_x": 0.6616007468917153, "mean_y": -1.3794158518633104, "mean_z": -0.4959465250930684, "std_x": 0.029950034437493715, "std_y": 0.027424378166516966, "std_z": 0.016741839588015294, "range_x": 0.19159913063049316, "range_y": 0.18289685249328613, "range_z": 0.10451671481132507}, {"start_time": 63243295, "end_time": 63247995, "duration": 4.7, "gyro_count": 27, "mean_x": 0.6541411302707814, "mean_y": -1.368920056908219, "mean_z": -0.4938300207809166, "std_x": 0.02419466918565281, "std_y": 0.028275105976104678, "std_z": 0.0170464413130116, "range_x": 0.07838010787963867, "range_y": 0.10451674461364746, "range_z": 0.06094580888748169}, {"start_time": 63489596, "end_time": 63509295, "duration": 19.699, "gyro_count": 176, "mean_x": 0.658909896896644, "mean_y": -1.366218440234661, "mean_z": -0.49690667556768114, "std_x": 0.030747215960419256, "std_y": 0.03646098014724397, "std_z": 0.019500497623681112, "range_x": 0.17416471242904663, "range_y": 0.3048182725906372, "range_z": 0.12192127108573914}, {"start_time": 63539095, "end_time": 63548095, "duration": 9.0, "gyro_count": 70, "mean_x": 0.6620041242667607, "mean_y": -1.3661900281906127, "mean_z": -0.49554155000618527, "std_x": 0.023698676708041086, "std_y": 0.0252011769311489, "std_z": 0.018348522571870907, "range_x": 0.13935565948486328, "range_y": 0.10451674461364746, "range_z": 0.08708235621452332}, {"start_time": 63680097, "end_time": 63773396, "duration": 93.299, "gyro_count": 913, "mean_x": 0.6634435579915167, "mean_y": -1.369205186688234, "mean_z": -0.4956777014189195, "std_x": 0.026627937817734218, "std_y": 0.021911738727337277, "std_z": 0.016334149642213202, "range_x": 0.26997923851013184, "range_y": 0.22643804550170898, "range_z": 0.09578463435173035}, {"start_time": 63830096, "end_time": 63882095, "duration": 51.999, "gyro_count": 501, "mean_x": 0.6624889949600615, "mean_y": -1.3696189062324113, "mean_z": -0.4945526082596617, "std_x": 0.02267023184843268, "std_y": 0.020227687821128305, "std_z": 0.015063702534961889, "range_x": 0.13065344095230103, "range_y": 0.12195110321044922, "range_z": 0.08708235621452332}, {"start_time": 64039496, "end_time": 64045798, "duration": 6.302, "gyro_count": 43, "mean_x": 0.6598497823227284, "mean_y": -1.3843172483665998, "mean_z": -0.49600631483765534, "std_x": 0.039674626910791114, "std_y": 0.025351216595975053, "std_z": 0.015430713537359976, "range_x": 0.16546255350112915, "range_y": 0.11321902275085449, "range_z": 0.06094580888748169}, {"start_time": 64050696, "end_time": 64069997, "duration": 19.301, "gyro_count": 174, "mean_x": 0.6642817985052349, "mean_y": -1.3719617895696354, "mean_z": -0.49330810329009744, "std_x": 0.03925489735052988, "std_y": 0.02428446259335076, "std_z": 0.018514677454031674, "range_x": 0.2525448203086853, "range_y": 0.16546249389648438, "range_z": 0.09581446647644043}, {"start_time": 64079895, "end_time": 64175396, "duration": 95.501, "gyro_count": 936, "mean_x": 0.6641220790453446, "mean_y": -1.3722464963156953, "mean_z": -0.4954352643754747, "std_x": 0.02971715712446361, "std_y": 0.02143731897943654, "std_z": 0.016183147470741267, "range_x": 0.2003014087677002, "range_y": 0.13065338134765625, "range_z": 0.09581446647644043}, {"start_time": 64246396, "end_time": 64261195, "duration": 14.799, "gyro_count": 127, "mean_x": 0.6609181512997845, "mean_y": -1.3741637975212158, "mean_z": -0.49730199153029075, "std_x": 0.029751186777931312, "std_y": 0.021151821877939025, "std_z": 0.018548698906118388, "range_x": 0.13935565948486328, "range_y": 0.09581434726715088, "range_z": 0.09578466415405273}, {"start_time": 64265996, "end_time": 64341996, "duration": 76.0, "gyro_count": 740, "mean_x": 0.6626207117293332, "mean_y": -1.3726377996238501, "mean_z": -0.49543515947219485, "std_x": 0.03286974499528503, "std_y": 0.0223185900410883, "std_z": 0.016351917578850902, "range_x": 0.1915692687034607, "range_y": 0.16546249389648438, "range_z": 0.13062354922294617}, {"start_time": 64460197, "end_time": 64520495, "duration": 60.298, "gyro_count": 582, "mean_x": 0.6652017867647085, "mean_y": -1.3725594310826044, "mean_z": -0.497145160571816, "std_x": 0.023584273657078728, "std_y": 0.02133595517873384, "std_z": 0.016660473757027644, "range_x": 0.16546255350112915, "range_y": 0.10451662540435791, "range_z": 0.10451671481132507}, {"start_time": 64576995, "end_time": 64611195, "duration": 34.2, "gyro_count": 322, "mean_x": 0.6642327049504155, "mean_y": -1.3710662663353155, "mean_z": -0.495303606949978, "std_x": 0.0251412244660055, "std_y": 0.02178649829932347, "std_z": 0.015807376498767078, "range_x": 0.1741647720336914, "range_y": 0.14805805683135986, "range_z": 0.1132189929485321}, {"start_time": 65068795, "end_time": 65103695, "duration": 34.9, "gyro_count": 329, "mean_x": 0.6647131968414167, "mean_y": -1.364818608507197, "mean_z": -0.49027177501232067, "std_x": 0.027022727082940314, "std_y": 0.03165306190378429, "std_z": 0.01781464645668464, "range_x": 0.1915692687034607, "range_y": 0.4354417324066162, "range_z": 0.1132189929485321}, {"start_time": 65302196, "end_time": 65351396, "duration": 49.2, "gyro_count": 473, "mean_x": 0.6617871832898001, "mean_y": -1.3683375248697294, "mean_z": -0.4920667344999616, "std_x": 0.031093049520705407, "std_y": 0.040152515656302484, "std_z": 0.019727894688815684, "range_x": 0.26997923851013184, "range_y": 0.6096363067626953, "range_z": 0.13062354922294617}], "failed_intervals": [], "all_passed": true}]}