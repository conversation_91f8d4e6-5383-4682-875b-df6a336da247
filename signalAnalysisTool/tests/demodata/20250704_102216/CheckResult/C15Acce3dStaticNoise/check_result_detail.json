{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查详情", "version": "1.0", "date": "2025-07-07 18:25:55", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "加速度器静态噪声过大", "static_intervals": [{"start_time": 59477072, "end_time": 59565795, "duration": 88.723, "start_time_adj": 59478072, "end_time_adj": 59564795}, {"start_time": 60081496, "end_time": 60104798, "duration": 23.302, "start_time_adj": 60082496, "end_time_adj": 60103798}, {"start_time": 60172396, "end_time": 60219896, "duration": 47.5, "start_time_adj": 60173396, "end_time_adj": 60218896}, {"start_time": 60308896, "end_time": 60329096, "duration": 20.2, "start_time_adj": 60309896, "end_time_adj": 60328096}, {"start_time": 60821596, "end_time": 60827495, "duration": 5.899, "start_time_adj": 60822596, "end_time_adj": 60826495}, {"start_time": 61260995, "end_time": 61331996, "duration": 71.001, "start_time_adj": 61261995, "end_time_adj": 61330996}, {"start_time": 61364996, "end_time": 61381195, "duration": 16.199, "start_time_adj": 61365996, "end_time_adj": 61380195}, {"start_time": 61421098, "end_time": 61463695, "duration": 42.597, "start_time_adj": 61422098, "end_time_adj": 61462695}, {"start_time": 61497695, "end_time": 61503798, "duration": 6.103, "start_time_adj": 61498695, "end_time_adj": 61502798}, {"start_time": 61608296, "end_time": 61633996, "duration": 25.7, "start_time_adj": 61609296, "end_time_adj": 61632996}, {"start_time": 61761495, "end_time": 61833896, "duration": 72.401, "start_time_adj": 61762495, "end_time_adj": 61832896}, {"start_time": 62141396, "end_time": 62161096, "duration": 19.7, "start_time_adj": 62142396, "end_time_adj": 62160096}, {"start_time": 62164396, "end_time": 62174996, "duration": 10.6, "start_time_adj": 62165396, "end_time_adj": 62173996}, {"start_time": 62223196, "end_time": 62229895, "duration": 6.699, "start_time_adj": 62224196, "end_time_adj": 62228895}, {"start_time": 62305696, "end_time": 62329896, "duration": 24.2, "start_time_adj": 62306696, "end_time_adj": 62328896}, {"start_time": 62411296, "end_time": 62473595, "duration": 62.299, "start_time_adj": 62412296, "end_time_adj": 62472595}, {"start_time": 62501595, "end_time": 62567896, "duration": 66.301, "start_time_adj": 62502595, "end_time_adj": 62566896}, {"start_time": 62599196, "end_time": 62608595, "duration": 9.399, "start_time_adj": 62600196, "end_time_adj": 62607595}, {"start_time": 62614096, "end_time": 62716996, "duration": 102.9, "start_time_adj": 62615096, "end_time_adj": 62715996}, {"start_time": 62723195, "end_time": 62730995, "duration": 7.8, "start_time_adj": 62724195, "end_time_adj": 62729995}, {"start_time": 62735799, "end_time": 62748096, "duration": 12.297, "start_time_adj": 62736799, "end_time_adj": 62747096}, {"start_time": 62752796, "end_time": 62778496, "duration": 25.7, "start_time_adj": 62753796, "end_time_adj": 62777496}, {"start_time": 62780595, "end_time": 62790997, "duration": 10.402, "start_time_adj": 62781595, "end_time_adj": 62789997}, {"start_time": 62800496, "end_time": 62805896, "duration": 5.4, "start_time_adj": 62801496, "end_time_adj": 62804896}, {"start_time": 62808897, "end_time": 62812996, "duration": 4.099, "start_time_adj": 62809897, "end_time_adj": 62811996}, {"start_time": 62819596, "end_time": 62840295, "duration": 20.699, "start_time_adj": 62820596, "end_time_adj": 62839295}, {"start_time": 63243295, "end_time": 63247995, "duration": 4.7, "start_time_adj": 63244295, "end_time_adj": 63246995}, {"start_time": 63489596, "end_time": 63509295, "duration": 19.699, "start_time_adj": 63490596, "end_time_adj": 63508295}, {"start_time": 63539095, "end_time": 63548095, "duration": 9.0, "start_time_adj": 63540095, "end_time_adj": 63547095}, {"start_time": 63680097, "end_time": 63773396, "duration": 93.299, "start_time_adj": 63681097, "end_time_adj": 63772396}, {"start_time": 63830096, "end_time": 63882095, "duration": 51.999, "start_time_adj": 63831096, "end_time_adj": 63881095}, {"start_time": 64039496, "end_time": 64045798, "duration": 6.302, "start_time_adj": 64040496, "end_time_adj": 64044798}, {"start_time": 64050696, "end_time": 64069997, "duration": 19.301, "start_time_adj": 64051696, "end_time_adj": 64068997}, {"start_time": 64079895, "end_time": 64175396, "duration": 95.501, "start_time_adj": 64080895, "end_time_adj": 64174396}, {"start_time": 64246396, "end_time": 64261195, "duration": 14.799, "start_time_adj": 64247396, "end_time_adj": 64260195}, {"start_time": 64265996, "end_time": 64341996, "duration": 76.0, "start_time_adj": 64266996, "end_time_adj": 64340996}, {"start_time": 64460197, "end_time": 64520495, "duration": 60.298, "start_time_adj": 64461197, "end_time_adj": 64519495}, {"start_time": 64576995, "end_time": 64611195, "duration": 34.2, "start_time_adj": 64577995, "end_time_adj": 64610195}, {"start_time": 65068795, "end_time": 65103695, "duration": 34.9, "start_time_adj": 65069795, "end_time_adj": 65102695}, {"start_time": 65302196, "end_time": 65351396, "duration": 49.2, "start_time_adj": 65303196, "end_time_adj": 65350396}], "long_static_intervals": [{"start_time": 59477072, "end_time": 59565795, "duration": 88.723, "gyro_count": 868, "mean_x": -0.022135181237690253, "mean_y": -0.025411826815919667, "mean_z": 1.02230160357216, "std_x": 0.0009098964103163863, "std_y": 0.0010304601215216456, "std_z": 0.0009199395636720323, "range_x": 0.014204120263457298, "range_y": 0.010652972385287285, "range_z": 0.011632561683654785}, {"start_time": 60081496, "end_time": 60104798, "duration": 23.302, "gyro_count": 213, "mean_x": -0.016499562162748525, "mean_y": -0.023338894346650218, "mean_z": 1.0226594181687618, "std_x": 0.0006667895994636909, "std_y": 0.0015595021305297573, "std_z": 0.0007649238181599752, "range_x": 0.004285738803446293, "range_y": 0.013959217816591263, "range_z": 0.0062448978424072266}, {"start_time": 60172396, "end_time": 60219896, "duration": 47.5, "gyro_count": 455, "mean_x": -0.022640136887247746, "mean_y": -0.009779487196151371, "mean_z": 1.0229387995960948, "std_x": 0.0006984017834928677, "std_y": 0.0006608804966368555, "std_z": 0.0005706267181548759, "range_x": 0.005265349522233009, "range_y": 0.005265349056571722, "range_z": 0.005510210990905762}, {"start_time": 60308896, "end_time": 60329096, "duration": 20.2, "gyro_count": 182, "mean_x": -0.005745692086498161, "mean_y": -0.01786141295067884, "mean_z": 1.0229919579002884, "std_x": 0.0009534874279662909, "std_y": 0.00044316031084498485, "std_z": 0.0006193412311210671, "range_x": 0.009306065505370498, "range_y": 0.0029387138783931732, "range_z": 0.0039185285568237305}, {"start_time": 60821596, "end_time": 60827495, "duration": 5.899, "gyro_count": 39, "mean_x": -0.021372062416794974, "mean_y": -0.037554152214374296, "mean_z": 1.0223422784071703, "std_x": 0.0007854703112907389, "std_y": 0.000874995723384165, "std_z": 0.0010341123333828224, "range_x": 0.003061225637793541, "range_y": 0.0035510286688804626, "range_z": 0.004897952079772949}, {"start_time": 61260995, "end_time": 61331996, "duration": 71.001, "gyro_count": 691, "mean_x": -0.014489921781621258, "mean_y": -0.01738155671218801, "mean_z": 1.0229815111836544, "std_x": 0.0009638004915991933, "std_y": 0.0008091988320474827, "std_z": 0.000743891939582655, "range_x": 0.010040774010121822, "range_y": 0.006857157684862614, "range_z": 0.009673476219177246}, {"start_time": 61364996, "end_time": 61381195, "duration": 16.199, "gyro_count": 143, "mean_x": -0.014469533624661553, "mean_y": -0.028194941202556337, "mean_z": 1.0229345268302865, "std_x": 0.0008051818940714204, "std_y": 0.0007522597161353499, "std_z": 0.0007069102277446515, "range_x": 0.003673423081636429, "range_y": 0.004040837287902832, "range_z": 0.004163265228271484}, {"start_time": 61421098, "end_time": 61463695, "duration": 42.597, "gyro_count": 406, "mean_x": -0.04238967227708236, "mean_y": -0.03551713969062995, "mean_z": 1.021886539870295, "std_x": 0.0013543335402430173, "std_y": 0.000878324716369586, "std_z": 0.0007578629157800965, "range_x": 0.012734703719615936, "range_y": 0.006122451275587082, "range_z": 0.008326530456542969}, {"start_time": 61497695, "end_time": 61503798, "duration": 6.103, "gyro_count": 41, "mean_x": -0.013986069595486653, "mean_y": -0.029716292850491478, "mean_z": 1.0231269481705456, "std_x": 0.0007585505419288525, "std_y": 0.0007225716601892659, "std_z": 0.0007836322161970836, "range_x": 0.0029387129470705986, "range_y": 0.003306126222014427, "range_z": 0.0034285783767700195}, {"start_time": 61608296, "end_time": 61633996, "duration": 25.7, "gyro_count": 237, "mean_x": -0.008688204807822715, "mean_y": -0.03348438090444365, "mean_z": 1.0231718121701654, "std_x": 0.0005964994640108046, "std_y": 0.0006312832314568617, "std_z": 0.0006255182529156682, "range_x": 0.0046531520783901215, "range_y": 0.004897935315966606, "range_z": 0.005510210990905762}, {"start_time": 61761495, "end_time": 61833896, "duration": 72.401, "gyro_count": 705, "mean_x": -0.01274249719072741, "mean_y": -0.0006959526321779712, "mean_z": 1.0231845537821451, "std_x": 0.00045405393057230167, "std_y": 0.0005327154835137856, "std_z": 0.0004687874168171825, "range_x": 0.005020446144044399, "range_y": 0.003918444039300084, "range_z": 0.004163265228271484}, {"start_time": 62141396, "end_time": 62161096, "duration": 19.7, "gyro_count": 177, "mean_x": -0.02247664796998777, "mean_y": -0.026140438108626058, "mean_z": 1.023125591924635, "std_x": 0.0029101608277408994, "std_y": 0.0016087169007006482, "std_z": 0.0011307290499970543, "range_x": 0.027918314561247826, "range_y": 0.010653089731931686, "range_z": 0.008081674575805664}, {"start_time": 62164396, "end_time": 62174996, "duration": 10.6, "gyro_count": 86, "mean_x": -0.02129188656460407, "mean_y": -0.02611296188606079, "mean_z": 1.022990063179371, "std_x": 0.0006881814401757507, "std_y": 0.0008183498731742158, "std_z": 0.0005760040711354915, "range_x": 0.003551030531525612, "range_y": 0.004530522972345352, "range_z": 0.0026938915252685547}, {"start_time": 62223196, "end_time": 62229895, "duration": 6.699, "gyro_count": 47, "mean_x": -0.01618671557925483, "mean_y": 0.0033816786908960725, "mean_z": 1.023298329495369, "std_x": 0.0009582745774044223, "std_y": 0.0011142563251987855, "std_z": 0.0007375300314222791, "range_x": 0.0056326426565647125, "range_y": 0.003918324131518602, "range_z": 0.0030612945556640625}, {"start_time": 62305696, "end_time": 62329896, "duration": 24.2, "gyro_count": 222, "mean_x": -0.025012129565348495, "mean_y": -0.021202979047220568, "mean_z": 1.0229569925918236, "std_x": 0.0011362990461654034, "std_y": 0.000920582560137855, "std_z": 0.0007039430530917254, "range_x": 0.00783664919435978, "range_y": 0.006000056862831116, "range_z": 0.0048980712890625}, {"start_time": 62411296, "end_time": 62473595, "duration": 62.299, "gyro_count": 603, "mean_x": -0.011694190543373525, "mean_y": -0.020144792276729596, "mean_z": 1.023240959664087, "std_x": 0.0008084935108427336, "std_y": 0.0007710660876645773, "std_z": 0.0007749167934492528, "range_x": 0.00771425710991025, "range_y": 0.005999939516186714, "range_z": 0.007591962814331055}, {"start_time": 62501595, "end_time": 62567896, "duration": 66.301, "gyro_count": 644, "mean_x": -0.018701227624779163, "mean_y": -0.023115669385825068, "mean_z": 1.0228410803753396, "std_x": 0.0007678674572924133, "std_y": 0.0007978594718921122, "std_z": 0.0005980227261735975, "range_x": 0.007346963509917259, "range_y": 0.009306065738201141, "range_z": 0.006489753723144531}, {"start_time": 62599196, "end_time": 62608595, "duration": 9.399, "gyro_count": 74, "mean_x": -0.058606723474489676, "mean_y": -0.06725262880728052, "mean_z": 1.019872602578756, "std_x": 0.0005229214179334608, "std_y": 0.0009273617021366505, "std_z": 0.0005451289224011911, "range_x": 0.0022040121257305145, "range_y": 0.003918446600437164, "range_z": 0.002693772315979004}, {"start_time": 62614096, "end_time": 62716996, "duration": 102.9, "gyro_count": 1009, "mean_x": -0.041100136396669894, "mean_y": -0.06040642627609969, "mean_z": 1.0211377479394435, "std_x": 0.0008163975510027122, "std_y": 0.0009672211728626338, "std_z": 0.0006917649902245151, "range_x": 0.007591869682073593, "range_y": 0.008449085056781769, "range_z": 0.005877494812011719}, {"start_time": 62723195, "end_time": 62730995, "duration": 7.8, "gyro_count": 59, "mean_x": -0.03127429258646602, "mean_y": -0.05803460223694979, "mean_z": 1.0215566097679785, "std_x": 0.0006367154345947872, "std_y": 0.0005014843569684126, "std_z": 0.0005694039642278955, "range_x": 0.0031837373971939087, "range_y": 0.0026939287781715393, "range_z": 0.0036734342575073242}, {"start_time": 62735799, "end_time": 62748096, "duration": 12.297, "gyro_count": 103, "mean_x": -0.023764629947763044, "mean_y": -0.05594490616790299, "mean_z": 1.021625172744677, "std_x": 0.0005059440970620574, "std_y": 0.0005782469243346852, "std_z": 0.000626535221400421, "range_x": 0.003795931115746498, "range_y": 0.002938833087682724, "range_z": 0.003918290138244629}, {"start_time": 62752796, "end_time": 62778496, "duration": 25.7, "gyro_count": 238, "mean_x": -0.019586688870204098, "mean_y": -0.047030693358358217, "mean_z": 1.0220966038583708, "std_x": 0.0004275717750486769, "std_y": 0.00061313685714676, "std_z": 0.00043445670625358996, "range_x": 0.002449028193950653, "range_y": 0.003795929253101349, "range_z": 0.0025714635848999023}, {"start_time": 62780595, "end_time": 62790997, "duration": 10.402, "gyro_count": 85, "mean_x": -0.020742857215159078, "mean_y": -0.0478069659541635, "mean_z": 1.0218987086239983, "std_x": 0.0008102925601334299, "std_y": 0.0005193054858327204, "std_z": 0.0007921235344035436, "range_x": 0.0034285206347703934, "range_y": 0.002326633781194687, "range_z": 0.004163265228271484}, {"start_time": 62800496, "end_time": 62805896, "duration": 5.4, "gyro_count": 35, "mean_x": -0.01727230492979288, "mean_y": -0.04013177348034722, "mean_z": 1.0221201624189105, "std_x": 0.0008834665569282221, "std_y": 0.0012743713633579586, "std_z": 0.000682253357611646, "range_x": 0.003918326459825039, "range_y": 0.005265232175588608, "range_z": 0.0030611753463745117}, {"start_time": 62808897, "end_time": 62812996, "duration": 4.099, "gyro_count": 21, "mean_x": -0.0169271118051949, "mean_y": -0.039469377448161445, "mean_z": 1.02237906342461, "std_x": 0.000638519925382523, "std_y": 0.0009828882605217534, "std_z": 0.0005866979700734288, "range_x": 0.0026939306408166885, "range_y": 0.0037959329783916473, "range_z": 0.0030611753463745117}, {"start_time": 62819596, "end_time": 62840295, "duration": 20.699, "gyro_count": 188, "mean_x": -0.015284631733207944, "mean_y": -0.03759443321998449, "mean_z": 1.0224861540692918, "std_x": 0.0012584318233093484, "std_y": 0.0008505206667249628, "std_z": 0.000831633718464621, "range_x": 0.01151018962264061, "range_y": 0.005877546966075897, "range_z": 0.006612300872802734}, {"start_time": 63243295, "end_time": 63247995, "duration": 4.7, "gyro_count": 27, "mean_x": -0.016861675859049515, "mean_y": 0.002730159024294052, "mean_z": 1.0219365490807428, "std_x": 0.0007577473226717432, "std_y": 0.0006874893170171309, "std_z": 0.0008360392119994248, "range_x": 0.0028163213282823563, "range_y": 0.0023265162017196417, "range_z": 0.0028162002563476562}, {"start_time": 63489596, "end_time": 63509295, "duration": 19.699, "gyro_count": 177, "mean_x": -0.02155517041683197, "mean_y": -0.021263230255263, "mean_z": 1.0221432534988317, "std_x": 0.0020370272742843087, "std_y": 0.0010514168070252102, "std_z": 0.0008520686779265458, "range_x": 0.01567341946065426, "range_y": 0.005999937653541565, "range_z": 0.005755066871643066}, {"start_time": 63539095, "end_time": 63548095, "duration": 9.0, "gyro_count": 71, "mean_x": -0.02086979717435971, "mean_y": -0.01834147856374022, "mean_z": 1.0221334376805264, "std_x": 0.0015247214767806219, "std_y": 0.0008756633084185052, "std_z": 0.0008525458556424338, "range_x": 0.009795989841222763, "range_y": 0.005020445212721825, "range_z": 0.005510210990905762}, {"start_time": 63680097, "end_time": 63773396, "duration": 93.299, "gyro_count": 913, "mean_x": -0.010854372385938255, "mean_y": -0.01260566740191714, "mean_z": 1.0221479354265095, "std_x": 0.00100436874893929, "std_y": 0.0008509628820908316, "std_z": 0.0006453801171481323, "range_x": 0.014326631091535091, "range_y": 0.006857157684862614, "range_z": 0.008448958396911621}, {"start_time": 63830096, "end_time": 63882095, "duration": 51.999, "gyro_count": 501, "mean_x": -0.01224001009991307, "mean_y": -0.022322116854661952, "mean_z": 1.0220895013409461, "std_x": 0.0004813602168276373, "std_y": 0.00045013651723381813, "std_z": 0.0004580276717031177, "range_x": 0.005142956972122192, "range_y": 0.004897935315966606, "range_z": 0.004040837287902832}, {"start_time": 64039496, "end_time": 64045798, "duration": 6.302, "gyro_count": 43, "mean_x": -0.020460377165744472, "mean_y": -0.017971523539271464, "mean_z": 1.0220247091248977, "std_x": 0.0012483979644359862, "std_y": 0.0010807195226725107, "std_z": 0.0013719781545435726, "range_x": 0.005020447075366974, "range_y": 0.0041632279753685, "range_z": 0.006367206573486328}, {"start_time": 64050696, "end_time": 64069997, "duration": 19.301, "gyro_count": 173, "mean_x": -0.015107234751514962, "mean_y": -0.021212697109864283, "mean_z": 1.0220802608942021, "std_x": 0.0013434559422668996, "std_y": 0.00160831436698521, "std_z": 0.0008356154105458153, "range_x": 0.01518373191356659, "range_y": 0.012612191960215569, "range_z": 0.005387783050537109}, {"start_time": 64079895, "end_time": 64175396, "duration": 95.501, "gyro_count": 936, "mean_x": -0.012836074965018937, "mean_y": -0.028709963971000705, "mean_z": 1.021933324698709, "std_x": 0.0007569194177271946, "std_y": 0.0009620266731809722, "std_z": 0.0007565936212025467, "range_x": 0.00624484196305275, "range_y": 0.006857156753540039, "range_z": 0.005510210990905762}, {"start_time": 64246396, "end_time": 64261195, "duration": 14.799, "gyro_count": 128, "mean_x": -0.017770418773579877, "mean_y": -0.04961766107589938, "mean_z": 1.0212675770744681, "std_x": 0.0009614588055732412, "std_y": 0.0008464944343803048, "std_z": 0.0009606862274400604, "range_x": 0.004775424487888813, "range_y": 0.0036735348403453827, "range_z": 0.00440824031829834}, {"start_time": 64265996, "end_time": 64341996, "duration": 76.0, "gyro_count": 740, "mean_x": -0.0171163836003256, "mean_y": -0.032236187776701675, "mean_z": 1.0216544233463907, "std_x": 0.0008997214401246615, "std_y": 0.0008239356083344353, "std_z": 0.001147113397444571, "range_x": 0.007714255712926388, "range_y": 0.006244843825697899, "range_z": 0.007591843605041504}, {"start_time": 64460197, "end_time": 64520495, "duration": 60.298, "gyro_count": 582, "mean_x": -0.016973704451701484, "mean_y": -0.022923625144736257, "mean_z": 1.0218536061109955, "std_x": 0.0007261022609384343, "std_y": 0.000680439091457258, "std_z": 0.0005911894932082464, "range_x": 0.005755034275352955, "range_y": 0.004897935315966606, "range_z": 0.005265355110168457}, {"start_time": 64576995, "end_time": 64611195, "duration": 34.2, "gyro_count": 322, "mean_x": -0.028853845695808806, "mean_y": -0.029537593315338125, "mean_z": 1.0212872716951074, "std_x": 0.0005814678525482141, "std_y": 0.0007535250889704793, "std_z": 0.0005249069401032811, "range_x": 0.003918323665857315, "range_y": 0.0067346468567848206, "range_z": 0.004285693168640137}, {"start_time": 65068795, "end_time": 65103695, "duration": 34.9, "gyro_count": 330, "mean_x": -0.012113544432389916, "mean_y": -0.07116251982974284, "mean_z": 1.0201636675632362, "std_x": 0.0011454612635230853, "std_y": 0.000920851219516792, "std_z": 0.0009954192958419364, "range_x": 0.011387798003852367, "range_y": 0.006244838237762451, "range_z": 0.00893867015838623}, {"start_time": 65302196, "end_time": 65351396, "duration": 49.2, "gyro_count": 472, "mean_x": -0.03082159819018285, "mean_y": -0.04753199735088116, "mean_z": 1.0205956732822676, "std_x": 0.001881816250317933, "std_y": 0.001143830620735362, "std_z": 0.0011493054106095956, "range_x": 0.03342856839299202, "range_y": 0.012612313032150269, "range_z": 0.018979549407958984}], "failed_intervals": [], "all_passed": true}]}