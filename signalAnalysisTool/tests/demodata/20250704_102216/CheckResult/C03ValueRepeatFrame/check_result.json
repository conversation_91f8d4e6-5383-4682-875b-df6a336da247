{"checker": "C03ValueRepeatFrame", "title": "数值重帧检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250704_102216/AutoSdkDemo/online/vlane/20250704_102216/data.txt", "version": "1.0", "date": "2025-07-07 18:20:55", "type": "CheckResult", "checkResult": [{"checkItem": "acce3d数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.23445399608477316, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro_xyz数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.11798450931994213, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro_temperature数值重帧率", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "数值重帧率<0", "description": "不通过需手动排查"}, {"checkItem": "pulse数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.12360200868158992, "checkRules": "数值重帧率<0.01", "description": "不通过需手动排查"}, {"checkItem": "gnss数值重帧率", "checkResult": "Pass", "checkResultStatistic": 0.00017155601303825698, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}]}