<html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>                <div id="e1da625e-2598-444c-a605-18a060101201" class="plotly-graph-div" style="height:1000px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("e1da625e-2598-444c-a605-18a060101201")) {                    Plotly.newPlot(                        "e1da625e-2598-444c-a605-18a060101201",                        [{"cells":{"align":["left","center","left","center","center"],"fill":{"color":[["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"]]},"format":[null,null,"html",null,null],"values":[["原始轮速误差检查","轮速尺度因子检查","标定轮速误差检查（全场景）","标定轮速误差检查(转弯或掉头)","误差超限积分检查"],["\u003cbr\u003e轮速误差均值:0.41\u003cbr\u003e轮速误差中位数:0.26","\u003cbr\u003e轮速尺度因子均值:1.02                    \u003cbr\u003e轮速尺度因子中位数:1.02                    \u003cbr\u003e16%分位轮速尺度因子:0.98                    \u003cbr\u003e84%分位轮速尺度因子:1.04                    \u003cbr\u003e轮速尺度因子均值检查结果:True                    \u003cbr\u003e轮速尺度因子中位数检查结果:True                    \u003cbr\u003e16%分位轮速尺度因子检查结果:True                    \u003cbr\u003e84%分位轮速尺度因子检查结果:True","\u003cbr\u003e轮速误差均值:0.03\u003cbr\u003e轮速误差中位数:0.00","\u003cbr\u003e轮速误差均值:-0.37\u003cbr\u003e轮速误差中位数:-0.19","\u003cbr\u003e超限区间误差积分最大累计值:7.59"],["误差均值或中位数在正负1km\u002fh之内","误均值或中位数在0.95~1.05范围\u003cbr\u003e尺度因子的16%或84%分位数在0.95~1.05范围之内","误差均值或中位数在正负1km\u002fh之内","误差均值或中位数在正负1km\u002fh之内","误差超限区间内轮速误差积分小于3.5米（全场景）"],["Pass","Pass","Pass","Pass","Fail"],["","","","",""]]},"header":{"align":"center","fill":{"color":"paleturquoise"},"values":["检查项","统计数值","检查规则","检查结果","描述"]},"type":"table","domain":{"x":[0.0,1.0],"y":[0.525,1.0]}},{"cells":{"align":"center","fill":{"color":[["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"]]},"values":[["原始轮速误差检查","轮速尺度因子检查","标定轮速误差检查（全场景）","标定轮速误差检查(转弯或掉头)","误差超限积分检查"],["[\n  {\n    \"pulse_sourceTickTime\": 59574895,\n    \"pulse_speed\": 8.550000190734863,\n    \"gt_speed\": 4.819746186617703,\n    \"speed_diff\": -3.7302540041171603,\n    \"angular_velocity\": 2.925109999999904\n  },\n  {\n    \"pulse_sourceTickTime\": 59574996,\n    \"pulse_speed\": 9.056249618530273,\n    \"gt_speed\": 5.168875933995559,\n    \"speed_diff\": -3.8873736845347144,\n    \"angular_velocity\": 3.7378000000001066\n  },\n  {\n    \"pulse_sourceTickTime\": 59575095,\n    \"pulse_speed\": 9.449999809265137,\n    \"gt_speed\": 5.52944525409409,\n    \"speed_diff\": -3.920554555171047,\n    \"angular_velocity\": 4.777829999999881\n  },\n  {\n    \"pulse_sourceTickTime\": 59575197,\n    \"pulse_speed\": 9.84375,\n    \"gt_speed\": 5.962527954172518,\n    \"speed_diff\": -3.8812220458274824,\n    \"angular_velocity\": 5.8682199999998375\n  },\n  {\n    \"pulse_sourceTickTime\": 59575296,\n    \"pulse_speed\": 10.181249618530273,\n    \"gt_speed\": 6.4139842327784855,\n    \"speed_diff\": -3.767265385751788,\n    \"angular_velocity\": 7.144170000000258\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 59565795,\n    \"pulse_speed\": 0.3374999761581421,\n    \"gt_speed\": 0.011262102597509614,\n    \"scale\": 0.033369195238794745\n  },\n  {\n    \"pulse_sourceTickTime\": 59565896,\n    \"pulse_speed\": 0.6187499761581421,\n    \"gt_speed\": 0.009202784116146731,\n    \"scale\": 0.014873187023436192\n  },\n  {\n    \"pulse_sourceTickTime\": 59565996,\n    \"pulse_speed\": 0.84375,\n    \"gt_speed\": 0.018760759142035795,\n    \"scale\": 0.02223497379796835\n  },\n  {\n    \"pulse_sourceTickTime\": 59566097,\n    \"pulse_speed\": 1.0124999284744263,\n    \"gt_speed\": 0.04948816005572371,\n    \"scale\": 0.048877198569573704\n  },\n  {\n    \"pulse_sourceTickTime\": 59566195,\n    \"pulse_speed\": 1.2374999523162842,\n    \"gt_speed\": 0.09778279023018044,\n    \"scale\": 0.07901639919028361\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 65067995,\n    \"pulse_speed\": 1.2523771767430305,\n    \"gt_speed\": 4.878706774307132,\n    \"speed_diff\": 3.626329597564102,\n    \"angular_velocity\": 1.2596899999999778\n  },\n  {\n    \"pulse_sourceTickTime\": 65118496,\n    \"pulse_speed\": 17.24864880433273,\n    \"gt_speed\": 21.00265209410304,\n    \"speed_diff\": 3.75400328977031,\n    \"angular_velocity\": -6.554400000000005\n  },\n  {\n    \"pulse_sourceTickTime\": 65118808,\n    \"pulse_speed\": 16.280903538944244,\n    \"gt_speed\": 20.042590932407066,\n    \"speed_diff\": 3.7616873934628217,\n    \"angular_velocity\": -6.6225999999999985\n  },\n  {\n    \"pulse_sourceTickTime\": 65120697,\n    \"pulse_speed\": 19.468772452888487,\n    \"gt_speed\": 15.868458992054926,\n    \"speed_diff\": -3.6003134608335614,\n    \"angular_velocity\": -11.070250000000215\n  },\n  {\n    \"pulse_sourceTickTime\": 65120795,\n    \"pulse_speed\": 19.924183125,\n    \"gt_speed\": 16.08623283432674,\n    \"speed_diff\": -3.837950290673259,\n    \"angular_velocity\": -11.070250000000215\n  }\n]","[]","[\n  {\n    \"start_time\": 65281196,\n    \"end_time\": 65283997\n  }\n]"]]},"header":{"align":"center","fill":{"color":"paleturquoise"},"values":["检查项","检查详情"]},"type":"table","domain":{"x":[0.0,1.0],"y":[0.0,0.475]}}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"annotations":[{"font":{"size":16},"showarrow":false,"text":"轮速精度检查结果","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"轮速精度检查详情","x":0.5,"xanchor":"center","xref":"paper","y":0.475,"yanchor":"bottom","yref":"paper"}],"title":{"text":"\u003cb\u003e轮速精度检查结果汇总\u003c\u002fb\u003e","x":0.5},"margin":{"t":100,"b":50,"l":50,"r":50},"height":1000,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html>