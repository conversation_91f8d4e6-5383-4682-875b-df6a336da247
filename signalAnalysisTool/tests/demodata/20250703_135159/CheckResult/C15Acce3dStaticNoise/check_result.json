{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_135159/AutoSdkDemo/online/vlane/20250703_135159/data.txt", "version": "1.0", "date": "2025-07-07 16:37:43", "type": "CheckResult", "checkResult": [{"checkItem": "加速度器静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 29<br>时长>2秒区间数: 29<br>通过区间数: 29<br>失败区间数: 0<br><br>区间1:<br>  时长: 51.46秒<br>  加速度器数据个数: 495<br>  标准差 - X:0.0031, Y:0.0009, Z:0.0010<br>  极值差 - X:0.0568, Y:0.0110, Z:0.0114<br>区间2:<br>  时长: 19.40秒<br>  加速度器数据个数: 174<br>  标准差 - X:0.0014, Y:0.0013, Z:0.0010<br>  极值差 - X:0.0059, Y:0.0084, Z:0.0060<br>区间3:<br>  时长: 4.40秒<br>  加速度器数据个数: 24<br>  标准差 - X:0.0015, Y:0.0023, Z:0.0009<br>  极值差 - X:0.0055, Y:0.0088, Z:0.0039<br>区间4:<br>  时长: 36.08秒<br>  加速度器数据个数: 340<br>  标准差 - X:0.0009, Y:0.0012, Z:0.0008<br>  极值差 - X:0.0044, Y:0.0045, Z:0.0039<br>区间5:<br>  时长: 35.80秒<br>  加速度器数据个数: 339<br>  标准差 - X:0.0012, Y:0.0008, Z:0.0008<br>  极值差 - X:0.0065, Y:0.0045, Z:0.0047<br>区间6:<br>  时长: 26.50秒<br>  加速度器数据个数: 245<br>  标准差 - X:0.0008, Y:0.0006, Z:0.0006<br>  极值差 - X:0.0065, Y:0.0039, Z:0.0042<br>区间7:<br>  时长: 21.10秒<br>  加速度器数据个数: 192<br>  标准差 - X:0.0012, Y:0.0013, Z:0.0010<br>  极值差 - X:0.0072, Y:0.0104, Z:0.0067<br>区间8:<br>  时长: 135.70秒<br>  加速度器数据个数: 1338<br>  标准差 - X:0.0007, Y:0.0008, Z:0.0008<br>  极值差 - X:0.0066, Y:0.0077, Z:0.0093<br>区间9:<br>  时长: 45.00秒<br>  加速度器数据个数: 430<br>  标准差 - X:0.0007, Y:0.0011, Z:0.0008<br>  极值差 - X:0.0056, Y:0.0071, Z:0.0070<br>区间10:<br>  时长: 16.20秒<br>  加速度器数据个数: 143<br>  标准差 - X:0.0011, Y:0.0014, Z:0.0014<br>  极值差 - X:0.0077, Y:0.0100, Z:0.0107<br>区间11:<br>  时长: 39.70秒<br>  加速度器数据个数: 377<br>  标准差 - X:0.0015, Y:0.0032, Z:0.0022<br>  极值差 - X:0.0143, Y:0.0299, Z:0.0282<br>区间12:<br>  时长: 27.20秒<br>  加速度器数据个数: 252<br>  标准差 - X:0.0006, Y:0.0007, Z:0.0008<br>  极值差 - X:0.0034, Y:0.0053, Z:0.0075<br>区间13:<br>  时长: 45.80秒<br>  加速度器数据个数: 438<br>  标准差 - X:0.0009, Y:0.0011, Z:0.0007<br>  极值差 - X:0.0078, Y:0.0113, Z:0.0075<br>区间14:<br>  时长: 97.60秒<br>  加速度器数据个数: 957<br>  标准差 - X:0.0007, Y:0.0008, Z:0.0007<br>  极值差 - X:0.0092, Y:0.0086, Z:0.0082<br>区间15:<br>  时长: 14.50秒<br>  加速度器数据个数: 125<br>  标准差 - X:0.0015, Y:0.0009, Z:0.0012<br>  极值差 - X:0.0110, Y:0.0054, Z:0.0104<br>区间16:<br>  时长: 55.00秒<br>  加速度器数据个数: 531<br>  标准差 - X:0.0010, Y:0.0008, Z:0.0009<br>  极值差 - X:0.0096, Y:0.0082, Z:0.0076<br>区间17:<br>  时长: 17.30秒<br>  加速度器数据个数: 153<br>  标准差 - X:0.0014, Y:0.0012, Z:0.0012<br>  极值差 - X:0.0086, Y:0.0093, Z:0.0109<br>区间18:<br>  时长: 26.80秒<br>  加速度器数据个数: 248<br>  标准差 - X:0.0008, Y:0.0008, Z:0.0007<br>  极值差 - X:0.0064, Y:0.0054, Z:0.0066<br>区间19:<br>  时长: 109.40秒<br>  加速度器数据个数: 1074<br>  标准差 - X:0.0008, Y:0.0007, Z:0.0007<br>  极值差 - X:0.0096, Y:0.0075, Z:0.0067<br>区间20:<br>  时长: 70.90秒<br>  加速度器数据个数: 689<br>  标准差 - X:0.0007, Y:0.0006, Z:0.0005<br>  极值差 - X:0.0083, Y:0.0061, Z:0.0062<br>区间21:<br>  时长: 8.40秒<br>  加速度器数据个数: 65<br>  标准差 - X:0.0007, Y:0.0007, Z:0.0011<br>  极值差 - X:0.0037, Y:0.0039, Z:0.0072<br>区间22:<br>  时长: 76.40秒<br>  加速度器数据个数: 745<br>  标准差 - X:0.0009, Y:0.0009, Z:0.0008<br>  极值差 - X:0.0083, Y:0.0076, Z:0.0067<br>区间23:<br>  时长: 49.80秒<br>  加速度器数据个数: 478<br>  标准差 - X:0.0006, Y:0.0011, Z:0.0007<br>  极值差 - X:0.0036, Y:0.0092, Z:0.0050<br>区间24:<br>  时长: 32.40秒<br>  加速度器数据个数: 305<br>  标准差 - X:0.0005, Y:0.0008, Z:0.0006<br>  极值差 - X:0.0039, Y:0.0094, Z:0.0055<br>区间25:<br>  时长: 62.80秒<br>  加速度器数据个数: 609<br>  标准差 - X:0.0006, Y:0.0006, Z:0.0006<br>  极值差 - X:0.0058, Y:0.0055, Z:0.0058<br>区间26:<br>  时长: 44.60秒<br>  加速度器数据个数: 427<br>  标准差 - X:0.0011, Y:0.0014, Z:0.0010<br>  极值差 - X:0.0096, Y:0.0119, Z:0.0086<br>区间27:<br>  时长: 51.20秒<br>  加速度器数据个数: 491<br>  标准差 - X:0.0004, Y:0.0006, Z:0.0004<br>  极值差 - X:0.0029, Y:0.0066, Z:0.0034<br>区间28:<br>  时长: 58.10秒<br>  加速度器数据个数: 562<br>  标准差 - X:0.0006, Y:0.0009, Z:0.0006<br>  极值差 - X:0.0050, Y:0.0092, Z:0.0051<br>区间29:<br>  时长: 8.60秒<br>  加速度器数据个数: 66<br>  标准差 - X:0.0015, Y:0.0005, Z:0.0006<br>  极值差 - X:0.0094, Y:0.0023, Z:0.0024", "checkRules": "所有静态区间(时长>2秒)的标准差<0.1<br>且极值差<0.2dps", "description": "不通过需手动排查"}]}