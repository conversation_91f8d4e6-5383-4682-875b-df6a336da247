{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查详情", "version": "1.0", "date": "2025-07-07 16:37:43", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "加速度器静态噪声过大", "static_intervals": [{"start_time": 130138203, "end_time": 130189665, "duration": 51.462, "start_time_adj": 130139203, "end_time_adj": 130188665}, {"start_time": 130254467, "end_time": 130273865, "duration": 19.398, "start_time_adj": 130255467, "end_time_adj": 130272865}, {"start_time": 130328666, "end_time": 130333067, "duration": 4.401, "start_time_adj": 130329666, "end_time_adj": 130332067}, {"start_time": 130337485, "end_time": 130373565, "duration": 36.08, "start_time_adj": 130338485, "end_time_adj": 130372565}, {"start_time": 130421265, "end_time": 130457066, "duration": 35.801, "start_time_adj": 130422265, "end_time_adj": 130456066}, {"start_time": 130566167, "end_time": 130592671, "duration": 26.504, "start_time_adj": 130567167, "end_time_adj": 130591671}, {"start_time": 130674266, "end_time": 130695365, "duration": 21.099, "start_time_adj": 130675266, "end_time_adj": 130694365}, {"start_time": 130984265, "end_time": 131119966, "duration": 135.701, "start_time_adj": 130985265, "end_time_adj": 131118966}, {"start_time": 132142766, "end_time": 132187768, "duration": 45.002, "start_time_adj": 132143766, "end_time_adj": 132186768}, {"start_time": 132981965, "end_time": 132998166, "duration": 16.201, "start_time_adj": 132982965, "end_time_adj": 132997166}, {"start_time": 133270866, "end_time": 133310566, "duration": 39.7, "start_time_adj": 133271866, "end_time_adj": 133309566}, {"start_time": 133341466, "end_time": 133368666, "duration": 27.2, "start_time_adj": 133342466, "end_time_adj": 133367666}, {"start_time": 133539966, "end_time": 133585766, "duration": 45.8, "start_time_adj": 133540966, "end_time_adj": 133584766}, {"start_time": 133862065, "end_time": 133959666, "duration": 97.601, "start_time_adj": 133863065, "end_time_adj": 133958666}, {"start_time": 134186766, "end_time": 134201268, "duration": 14.502, "start_time_adj": 134187766, "end_time_adj": 134200268}, {"start_time": 134261566, "end_time": 134316565, "duration": 54.999, "start_time_adj": 134262566, "end_time_adj": 134315565}, {"start_time": 134590766, "end_time": 134608066, "duration": 17.3, "start_time_adj": 134591766, "end_time_adj": 134607066}, {"start_time": 134729766, "end_time": 134756565, "duration": 26.799, "start_time_adj": 134730766, "end_time_adj": 134755565}, {"start_time": 135192966, "end_time": 135302366, "duration": 109.4, "start_time_adj": 135193966, "end_time_adj": 135301366}, {"start_time": 135392065, "end_time": 135462965, "duration": 70.9, "start_time_adj": 135393065, "end_time_adj": 135461965}, {"start_time": 135467665, "end_time": 135476066, "duration": 8.401, "start_time_adj": 135468665, "end_time_adj": 135475066}, {"start_time": 135732065, "end_time": 135808466, "duration": 76.401, "start_time_adj": 135733065, "end_time_adj": 135807466}, {"start_time": 135880866, "end_time": 135930666, "duration": 49.8, "start_time_adj": 135881866, "end_time_adj": 135929666}, {"start_time": 136135465, "end_time": 136167866, "duration": 32.401, "start_time_adj": 136136465, "end_time_adj": 136166866}, {"start_time": 136470765, "end_time": 136533566, "duration": 62.801, "start_time_adj": 136471765, "end_time_adj": 136532566}, {"start_time": 137145765, "end_time": 137190367, "duration": 44.602, "start_time_adj": 137146765, "end_time_adj": 137189367}, {"start_time": 137304769, "end_time": 137355965, "duration": 51.196, "start_time_adj": 137305769, "end_time_adj": 137354965}, {"start_time": 137490465, "end_time": 137548566, "duration": 58.101, "start_time_adj": 137491465, "end_time_adj": 137547566}, {"start_time": 137574165, "end_time": 137582765, "duration": 8.6, "start_time_adj": 137575165, "end_time_adj": 137581765}], "long_static_intervals": [{"start_time": 130138203, "end_time": 130189665, "duration": 51.462, "gyro_count": 495, "mean_x": -0.021542602173532502, "mean_y": -0.05400173535401171, "mean_z": 1.022877455239344, "std_x": 0.003095031828939589, "std_y": 0.0009264534529612937, "std_z": 0.0009818988780852628, "range_x": 0.05681636370718479, "range_y": 0.011020384728908539, "range_z": 0.011387825012207031}, {"start_time": 130254467, "end_time": 130273865, "duration": 19.398, "gyro_count": 174, "mean_x": -0.02075510593024136, "mean_y": -0.04072906550062799, "mean_z": 1.0234834969728843, "std_x": 0.0013910772787975468, "std_y": 0.001285030661248835, "std_z": 0.0010320218204579068, "range_x": 0.005877546966075897, "range_y": 0.008448965847492218, "range_z": 0.006000041961669922}, {"start_time": 130328666, "end_time": 130333067, "duration": 4.401, "gyro_count": 24, "mean_x": -0.017280624092866976, "mean_y": -0.035954095578442015, "mean_z": 1.0232704381148021, "std_x": 0.0014673279033259525, "std_y": 0.002271025114430728, "std_z": 0.0009467342536561494, "range_x": 0.005510253831744194, "range_y": 0.008816257119178772, "range_z": 0.003918290138244629}, {"start_time": 130337485, "end_time": 130373565, "duration": 36.08, "gyro_count": 340, "mean_x": -0.01568175600567723, "mean_y": -0.03676386788925704, "mean_z": 1.023683572516722, "std_x": 0.0009441636552840352, "std_y": 0.0011598584223077037, "std_z": 0.0007914311967700973, "range_x": 0.004408129490911961, "range_y": 0.004530642181634903, "range_z": 0.00391840934753418}, {"start_time": 130421265, "end_time": 130457066, "duration": 35.801, "gyro_count": 339, "mean_x": -0.0159693005272245, "mean_y": -0.019319850369013163, "mean_z": 1.023812555633815, "std_x": 0.0011943038053435508, "std_y": 0.0008341283267262811, "std_z": 0.0007972888715420008, "range_x": 0.006489744409918785, "range_y": 0.004530640318989754, "range_z": 0.004652857780456543}, {"start_time": 130566167, "end_time": 130592671, "duration": 26.504, "gyro_count": 245, "mean_x": -0.014013672593448843, "mean_y": -0.017189846491935303, "mean_z": 1.0242532506281017, "std_x": 0.0007525685757383018, "std_y": 0.0006427035878815796, "std_z": 0.0005611290798548904, "range_x": 0.006489863619208336, "range_y": 0.003918326459825039, "range_z": 0.004163146018981934}, {"start_time": 130674266, "end_time": 130695365, "duration": 21.099, "gyro_count": 192, "mean_x": -0.01222959073978321, "mean_y": -0.01968558504692434, "mean_z": 1.0241652000695467, "std_x": 0.001192369231267494, "std_y": 0.0013390384569530005, "std_z": 0.0009815560158386828, "range_x": 0.007224450819194317, "range_y": 0.010408186353743076, "range_z": 0.006734728813171387}, {"start_time": 130984265, "end_time": 131119966, "duration": 135.701, "gyro_count": 1338, "mean_x": -0.019784388402462987, "mean_y": -0.017495748657254315, "mean_z": 1.0241140498708716, "std_x": 0.0006928228182789051, "std_y": 0.0008391249803051465, "std_z": 0.0007541159631571188, "range_x": 0.006612256169319153, "range_y": 0.007714255712926388, "range_z": 0.009306192398071289}, {"start_time": 132142766, "end_time": 132187768, "duration": 45.002, "gyro_count": 430, "mean_x": -0.04526199455004792, "mean_y": -0.02709853245855071, "mean_z": 1.0225988052612127, "std_x": 0.0007344315864497914, "std_y": 0.001070931924429051, "std_z": 0.0008342268610847813, "range_x": 0.0056326426565647125, "range_y": 0.0071020592004060745, "range_z": 0.006979584693908691}, {"start_time": 132981965, "end_time": 132998166, "duration": 16.201, "gyro_count": 143, "mean_x": -0.032512351193211296, "mean_y": -0.03563008353218332, "mean_z": 1.0221904182767534, "std_x": 0.0011090990058352677, "std_y": 0.0014256529770168627, "std_z": 0.001355001883050394, "range_x": 0.007714258506894112, "range_y": 0.010040774941444397, "range_z": 0.010653018951416016}, {"start_time": 133270866, "end_time": 133310566, "duration": 39.7, "gyro_count": 377, "mean_x": -0.02049120985692866, "mean_y": -0.020224872155149592, "mean_z": 1.022343450262945, "std_x": 0.0014858659558797515, "std_y": 0.003164647576138923, "std_z": 0.002205124609628109, "range_x": 0.01432663295418024, "range_y": 0.029877536464482546, "range_z": 0.02816331386566162}, {"start_time": 133341466, "end_time": 133368666, "duration": 27.2, "gyro_count": 252, "mean_x": -0.016551988454358207, "mean_y": -0.03590671635336346, "mean_z": 1.022488875048501, "std_x": 0.0006103653902156018, "std_y": 0.0006910218891853155, "std_z": 0.0007749335183266412, "range_x": 0.0034286389127373695, "range_y": 0.005265228450298309, "range_z": 0.007469296455383301}, {"start_time": 133539966, "end_time": 133585766, "duration": 45.8, "gyro_count": 438, "mean_x": -0.017562492204124116, "mean_y": -0.04049957160025699, "mean_z": 1.022387521452011, "std_x": 0.0008541001410969502, "std_y": 0.0010753766153038253, "std_z": 0.000677988466820339, "range_x": 0.007836769334971905, "range_y": 0.011265285313129425, "range_z": 0.0074694156646728516}, {"start_time": 133862065, "end_time": 133959666, "duration": 97.601, "gyro_count": 957, "mean_x": -0.0014800107375982138, "mean_y": -0.04780308833363288, "mean_z": 1.0220084912849194, "std_x": 0.0007385738140737032, "std_y": 0.0007722120621723226, "std_z": 0.0007359993632245911, "range_x": 0.009183673653751612, "range_y": 0.008571475744247437, "range_z": 0.008204102516174316}, {"start_time": 134186766, "end_time": 134201268, "duration": 14.502, "gyro_count": 125, "mean_x": -0.016304323486983775, "mean_y": -0.03400946888327599, "mean_z": 1.0225244522094727, "std_x": 0.0015423775032125727, "std_y": 0.0008763716641694109, "std_z": 0.0011712661037151235, "range_x": 0.011020385660231113, "range_y": 0.005387742072343826, "range_z": 0.010408163070678711}, {"start_time": 134261566, "end_time": 134316565, "duration": 54.999, "gyro_count": 531, "mean_x": -0.02478404125994994, "mean_y": -0.02170667130142282, "mean_z": 1.0224125820813654, "std_x": 0.0009913526596405753, "std_y": 0.0007929277508225726, "std_z": 0.00093658178584134, "range_x": 0.009550966322422028, "range_y": 0.008204061537981033, "range_z": 0.007591962814331055}, {"start_time": 134590766, "end_time": 134608066, "duration": 17.3, "gyro_count": 153, "mean_x": -0.01827131543503283, "mean_y": -0.02337654985584854, "mean_z": 1.0224266075620465, "std_x": 0.0014116567809246243, "std_y": 0.0012138430621307734, "std_z": 0.0012131046120409646, "range_x": 0.00857135746628046, "range_y": 0.009306184947490692, "range_z": 0.010897994041442871}, {"start_time": 134729766, "end_time": 134756565, "duration": 26.799, "gyro_count": 248, "mean_x": -0.022014640812431614, "mean_y": -0.024217750065990033, "mean_z": 1.022464822376928, "std_x": 0.0008080995240153073, "std_y": 0.0008253096987946061, "std_z": 0.0007033605067970996, "range_x": 0.006367353722453117, "range_y": 0.005387740209698677, "range_z": 0.006612181663513184}, {"start_time": 135192966, "end_time": 135302366, "duration": 109.4, "gyro_count": 1074, "mean_x": -0.007575990789564712, "mean_y": -0.0526676457652777, "mean_z": 1.0217546890567801, "std_x": 0.0007554130772874319, "std_y": 0.000699165747578678, "std_z": 0.0006909181822506012, "range_x": 0.009551088325679302, "range_y": 0.007469475269317627, "range_z": 0.006734728813171387}, {"start_time": 135392065, "end_time": 135462965, "duration": 70.9, "gyro_count": 689, "mean_x": -0.022748495031726722, "mean_y": -0.06335020858486262, "mean_z": 1.0209922370094, "std_x": 0.0007257834585905794, "std_y": 0.0006089786821016105, "std_z": 0.000545972056019872, "range_x": 0.008326455950737, "range_y": 0.0061224475502967834, "range_z": 0.0062448978424072266}, {"start_time": 135467665, "end_time": 135476066, "duration": 8.401, "gyro_count": 65, "mean_x": -0.020222926741609207, "mean_y": -0.05825934937367072, "mean_z": 1.0212753809415378, "std_x": 0.0007179996256461072, "std_y": 0.000732286804747167, "std_z": 0.001088338781999152, "range_x": 0.0036734212189912796, "range_y": 0.003918442875146866, "range_z": 0.007224559783935547}, {"start_time": 135732065, "end_time": 135808466, "duration": 76.401, "gyro_count": 745, "mean_x": -0.009236267524702637, "mean_y": -0.07774523933661864, "mean_z": 1.0205284478680399, "std_x": 0.0008710644857617431, "std_y": 0.0008695790629663243, "std_z": 0.0007510115344302956, "range_x": 0.008326455019414425, "range_y": 0.007591865956783295, "range_z": 0.006734728813171387}, {"start_time": 135880866, "end_time": 135930666, "duration": 49.8, "gyro_count": 478, "mean_x": -0.015314065670072409, "mean_y": -0.04370121562767727, "mean_z": 1.0220063633001, "std_x": 0.0005701211499910602, "std_y": 0.001051518338422145, "std_z": 0.0006512600816499272, "range_x": 0.003551030531525612, "range_y": 0.009183671325445175, "range_z": 0.0050203800201416016}, {"start_time": 136135465, "end_time": 136167866, "duration": 32.401, "gyro_count": 305, "mean_x": -0.009926384740860248, "mean_y": -0.02070352361216897, "mean_z": 1.022391216090468, "std_x": 0.0005455645497187948, "std_y": 0.0008272806847301498, "std_z": 0.0005659434768907307, "range_x": 0.003918444272130728, "range_y": 0.00942857563495636, "range_z": 0.0055103302001953125}, {"start_time": 136470765, "end_time": 136533566, "duration": 62.801, "gyro_count": 609, "mean_x": -0.024425661017008014, "mean_y": -0.027491912569954674, "mean_z": 1.021986372365153, "std_x": 0.0006272726807319716, "std_y": 0.0006317784017708009, "std_z": 0.0006369409028378194, "range_x": 0.00575515441596508, "range_y": 0.005510134622454643, "range_z": 0.005754947662353516}, {"start_time": 137145765, "end_time": 137190367, "duration": 44.602, "gyro_count": 427, "mean_x": -0.009921519412927382, "mean_y": -0.021132631425852658, "mean_z": 1.022420915563436, "std_x": 0.0010661115037944179, "std_y": 0.0013624973452145861, "std_z": 0.001005252452316821, "range_x": 0.009551087860018015, "range_y": 0.011877604760229588, "range_z": 0.008571386337280273}, {"start_time": 137304769, "end_time": 137355965, "duration": 51.196, "gyro_count": 491, "mean_x": 0.004475992599733614, "mean_y": -0.017033630244191092, "mean_z": 1.0229300673284745, "std_x": 0.000391597645503258, "std_y": 0.0005824695313588783, "std_z": 0.0004291867126201272, "range_x": 0.0029388326220214367, "range_y": 0.006612134166061878, "range_z": 0.0034285783767700195}, {"start_time": 137490465, "end_time": 137548566, "duration": 58.101, "gyro_count": 562, "mean_x": -0.011227185365634563, "mean_y": -0.023941832853090084, "mean_z": 1.022808073890591, "std_x": 0.0006161547455495755, "std_y": 0.0009066177695828653, "std_z": 0.0005946988736607234, "range_x": 0.005020327866077423, "range_y": 0.009183675050735474, "range_z": 0.005142807960510254}, {"start_time": 137574165, "end_time": 137582765, "duration": 8.6, "gyro_count": 66, "mean_x": -0.011437851519352107, "mean_y": -0.09455104081919699, "mean_z": 1.019619685230833, "std_x": 0.0015117942157267376, "std_y": 0.0005431917000113009, "std_z": 0.0006490333729090148, "range_x": 0.009428576100617647, "range_y": 0.0023265182971954346, "range_z": 0.0024487972259521484}], "failed_intervals": [], "all_passed": true}]}