2025-07-07 16:28:36,357 - C09GnssPulseSpeed - INFO - init
2025-07-07 16:28:36,357 - C09GnssPulseSpeed - INFO - check
2025-07-07 16:28:39,333 - C09GnssPulseSpeed - INFO - cross_correlation_function, gnss length : 365 , pulse length : 3639
2025-07-07 16:28:39,337 - C09GnssPulseSpeed - INFO - 最大延迟 max_lag = 0.0 s
2025-07-07 16:28:39,338 - C09GnssPulseSpeed - INFO - check_result: {'checker': 'gnssPulseSpeedChecker', 'title': 'GNSS速度和轮速不同步', 'datatxt': '/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_135159/AutoSdkDemo/online/vlane/20250703_135159/data.txt', 'version': '1.0', 'date': '2025-07-07 16:28:36', 'type': 'CheckResult', 'checkResult': {'checkItem': 'GNSS速度和轮速不同步相关性函数', 'checkList': [{'checkDataTime': '131256410 - 131620546', 'checkResultStatistic': '最大延迟0.0s', 'checkResult': 'Pass'}], 'checkRules': '最大延迟<=0.2s', 'description': '不通过需手动排查'}}
2025-07-07 16:28:39,461 - C09GnssPulseSpeed - INFO - cross_correlation_function, gnss length : 478 , pulse length : 4767
2025-07-07 16:28:39,462 - C09GnssPulseSpeed - INFO - 最大延迟 max_lag = 0.7000000000000001 s
2025-07-07 16:28:39,462 - C09GnssPulseSpeed - INFO - check_result: {'checker': 'gnssPulseSpeedChecker', 'title': 'GNSS速度和轮速不同步', 'datatxt': '/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_135159/AutoSdkDemo/online/vlane/20250703_135159/data.txt', 'version': '1.0', 'date': '2025-07-07 16:28:36', 'type': 'CheckResult', 'checkResult': {'checkItem': 'GNSS速度和轮速不同步相关性函数', 'checkList': [{'checkDataTime': '131256410 - 131620546', 'checkResultStatistic': '最大延迟0.0s', 'checkResult': 'Pass'}, {'checkDataTime': '131633551 - 132110726', 'checkResultStatistic': '最大延迟0.7000000000000001s', 'checkResult': 'Fail'}], 'checkRules': '最大延迟<=0.2s', 'description': '不通过需手动排查'}}
2025-07-07 16:28:39,485 - C09GnssPulseSpeed - INFO - cross_correlation_function, gnss length : 773 , pulse length : 7713
2025-07-07 16:28:39,487 - C09GnssPulseSpeed - INFO - 最大延迟 max_lag = 0.0 s
2025-07-07 16:28:39,487 - C09GnssPulseSpeed - INFO - check_result: {'checker': 'gnssPulseSpeedChecker', 'title': 'GNSS速度和轮速不同步', 'datatxt': '/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_135159/AutoSdkDemo/online/vlane/20250703_135159/data.txt', 'version': '1.0', 'date': '2025-07-07 16:28:36', 'type': 'CheckResult', 'checkResult': {'checkItem': 'GNSS速度和轮速不同步相关性函数', 'checkList': [{'checkDataTime': '131256410 - 131620546', 'checkResultStatistic': '最大延迟0.0s', 'checkResult': 'Pass'}, {'checkDataTime': '131633551 - 132110726', 'checkResultStatistic': '最大延迟0.7000000000000001s', 'checkResult': 'Fail'}, {'checkDataTime': '132197754 - 132970046', 'checkResultStatistic': '最大延迟0.0s', 'checkResult': 'Pass'}], 'checkRules': '最大延迟<=0.2s', 'description': '不通过需手动排查'}}
