<html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>                <div id="16d18ade-787e-4055-864c-8ed8fccaa24c" class="plotly-graph-div" style="height:1000px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("16d18ade-787e-4055-864c-8ed8fccaa24c")) {                    Plotly.newPlot(                        "16d18ade-787e-4055-864c-8ed8fccaa24c",                        [{"cells":{"align":["left","center","left","center","center"],"fill":{"color":[["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"]]},"format":[null,null,"html",null,null],"values":[["原始轮速误差检查","轮速尺度因子检查","标定轮速误差检查（全场景）","标定轮速误差检查(转弯或掉头)","误差超限积分检查"],["\u003cbr\u003e轮速误差均值:0.49\u003cbr\u003e轮速误差中位数:0.49","\u003cbr\u003e轮速尺度因子均值:1.02                    \u003cbr\u003e轮速尺度因子中位数:1.02                    \u003cbr\u003e16%分位轮速尺度因子:0.99                    \u003cbr\u003e84%分位轮速尺度因子:1.04                    \u003cbr\u003e轮速尺度因子均值检查结果:True                    \u003cbr\u003e轮速尺度因子中位数检查结果:True                    \u003cbr\u003e16%分位轮速尺度因子检查结果:True                    \u003cbr\u003e84%分位轮速尺度因子检查结果:True","\u003cbr\u003e轮速误差均值:0.00\u003cbr\u003e轮速误差中位数:0.00","\u003cbr\u003e轮速误差均值:-0.15\u003cbr\u003e轮速误差中位数:-0.15","\u003cbr\u003e超限区间误差积分最大累计值:15.65"],["误差均值或中位数在正负1km\u002fh之内","误均值或中位数在0.95~1.05范围\u003cbr\u003e尺度因子的16%或84%分位数在0.95~1.05范围之内","误差均值或中位数在正负1km\u002fh之内","误差均值或中位数在正负1km\u002fh之内","误差超限区间内轮速误差积分小于3.5米（全场景）"],["Pass","Pass","Pass","Pass","Fail"],["","","","",""]]},"header":{"align":"center","fill":{"color":"paleturquoise"},"values":["检查项","统计数值","检查规则","检查结果","描述"]},"type":"table","domain":{"x":[0.0,1.0],"y":[0.525,1.0]}},{"cells":{"align":"center","fill":{"color":[["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"]]},"values":[["原始轮速误差检查","轮速尺度因子检查","标定轮速误差检查（全场景）","标定轮速误差检查(转弯或掉头)","误差超限积分检查"],["[\n  {\n    \"pulse_sourceTickTime\": 130210665,\n    \"pulse_speed\": 8.71875,\n    \"gt_speed\": 4.930048287269078,\n    \"speed_diff\": -3.788701712730922,\n    \"angular_velocity\": -18.121950000000027\n  },\n  {\n    \"pulse_sourceTickTime\": 130210766,\n    \"pulse_speed\": 8.94374942779541,\n    \"gt_speed\": 5.115330420468349,\n    \"speed_diff\": -3.828419007327061,\n    \"angular_velocity\": -18.879090000000076\n  },\n  {\n    \"pulse_sourceTickTime\": 130210866,\n    \"pulse_speed\": 9.112500190734863,\n    \"gt_speed\": 5.390092464167914,\n    \"speed_diff\": -3.7224077265669493,\n    \"angular_velocity\": -19.865880000000118\n  },\n  {\n    \"pulse_sourceTickTime\": 130210965,\n    \"pulse_speed\": 9.449999809265137,\n    \"gt_speed\": 5.714026193140192,\n    \"speed_diff\": -3.7359736161249444,\n    \"angular_velocity\": -20.581359999999904\n  },\n  {\n    \"pulse_sourceTickTime\": 130211066,\n    \"pulse_speed\": 9.731249809265137,\n    \"gt_speed\": 6.0236616850868,\n    \"speed_diff\": -3.7075881241783364,\n    \"angular_velocity\": -20.101150943396146\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 130189665,\n    \"pulse_speed\": 0.22499999403953552,\n    \"gt_speed\": 0.01866329507247944,\n    \"scale\": 0.08294798029727969\n  },\n  {\n    \"pulse_sourceTickTime\": 130189766,\n    \"pulse_speed\": 0.22499999403953552,\n    \"gt_speed\": 0.015767827570013538,\n    \"scale\": 0.07007923550097037\n  },\n  {\n    \"pulse_sourceTickTime\": 130189866,\n    \"pulse_speed\": 0.3374999761581421,\n    \"gt_speed\": 0.017204706420431405,\n    \"scale\": 0.050976911513527956\n  },\n  {\n    \"pulse_sourceTickTime\": 130189966,\n    \"pulse_speed\": 0.44999998807907104,\n    \"gt_speed\": 0.036503123650921294,\n    \"scale\": 0.08111805470649747\n  },\n  {\n    \"pulse_sourceTickTime\": 130190069,\n    \"pulse_speed\": 0.44999998807907104,\n    \"gt_speed\": 0.10544250204648428,\n    \"scale\": 0.23431667742168166\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 134626865,\n    \"pulse_speed\": 18.749218942728042,\n    \"gt_speed\": 14.5427076545225,\n    \"speed_diff\": -4.206511288205542,\n    \"angular_velocity\": 1.7561299999999846\n  },\n  {\n    \"pulse_sourceTickTime\": 134626965,\n    \"pulse_speed\": 19.54705648431587,\n    \"gt_speed\": 14.585997750067127,\n    \"speed_diff\": -4.961058734248743,\n    \"angular_velocity\": 1.714939999999956\n  },\n  {\n    \"pulse_sourceTickTime\": 134627065,\n    \"pulse_speed\": 20.401884078065873,\n    \"gt_speed\": 14.681291235390063,\n    \"speed_diff\": -5.72059284267581,\n    \"angular_velocity\": 1.5614299999998593\n  },\n  {\n    \"pulse_sourceTickTime\": 134627165,\n    \"pulse_speed\": 20.971769140565872,\n    \"gt_speed\": 14.851798880220615,\n    \"speed_diff\": -6.119970260345257,\n    \"angular_velocity\": 1.1106800000001726\n  },\n  {\n    \"pulse_sourceTickTime\": 134627266,\n    \"pulse_speed\": 21.88358485408783,\n    \"gt_speed\": 15.148763897474078,\n    \"speed_diff\": -6.734820956613751,\n    \"angular_velocity\": 0.6491099999999506\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 134685065,\n    \"pulse_speed\": 39.493035604206085,\n    \"gt_speed\": 32.82113386109512,\n    \"speed_diff\": -6.671901743110965,\n    \"angular_velocity\": 10.489809999999693\n  },\n  {\n    \"pulse_sourceTickTime\": 134685166,\n    \"pulse_speed\": 39.493035604206085,\n    \"gt_speed\": 33.58419935627071,\n    \"speed_diff\": -5.908836247935376,\n    \"angular_velocity\": 10.117490000000089\n  },\n  {\n    \"pulse_sourceTickTime\": 134685266,\n    \"pulse_speed\": 40.34786319795608,\n    \"gt_speed\": 34.26570888296183,\n    \"speed_diff\": -6.0821543149942485,\n    \"angular_velocity\": 10.008540000000039\n  }\n]","[\n  {\n    \"start_time\": 137583465,\n    \"end_time\": 137585166\n  }\n]"]]},"header":{"align":"center","fill":{"color":"paleturquoise"},"values":["检查项","检查详情"]},"type":"table","domain":{"x":[0.0,1.0],"y":[0.0,0.475]}}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"annotations":[{"font":{"size":16},"showarrow":false,"text":"轮速精度检查结果","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"轮速精度检查详情","x":0.5,"xanchor":"center","xref":"paper","y":0.475,"yanchor":"bottom","yref":"paper"}],"title":{"text":"\u003cb\u003e轮速精度检查结果汇总\u003c\u002fb\u003e","x":0.5},"margin":{"t":100,"b":50,"l":50,"r":50},"height":1000,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html>