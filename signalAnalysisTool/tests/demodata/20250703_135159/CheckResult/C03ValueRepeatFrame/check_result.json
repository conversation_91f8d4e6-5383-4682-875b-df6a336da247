{"checker": "C03ValueRepeatFrame", "title": "数值重帧检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250703_135159/AutoSdkDemo/online/vlane/20250703_135159/data.txt", "version": "1.0", "date": "2025-07-07 16:28:02", "type": "CheckResult", "checkResult": [{"checkItem": "acce3d数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.23301528757750695, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro_xyz数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.11480382723968356, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro_temperature数值重帧率", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "数值重帧率<0", "description": "不通过需手动排查"}, {"checkItem": "pulse数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.1315480008552491, "checkRules": "数值重帧率<0.01", "description": "不通过需手动排查"}, {"checkItem": "gnss数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.0010698047606311847, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}]}