{"checker": "C14GryoStaticNoise", "title": "陀螺仪静态噪声检查详情", "version": "1.0", "date": "2025-07-07 16:37:37", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "陀螺静态噪声过大", "static_intervals": [{"start_time": 130138203, "end_time": 130189665, "duration": 51.462, "start_time_adj": 130139203, "end_time_adj": 130188665}, {"start_time": 130254467, "end_time": 130273865, "duration": 19.398, "start_time_adj": 130255467, "end_time_adj": 130272865}, {"start_time": 130328666, "end_time": 130333067, "duration": 4.401, "start_time_adj": 130329666, "end_time_adj": 130332067}, {"start_time": 130337485, "end_time": 130373565, "duration": 36.08, "start_time_adj": 130338485, "end_time_adj": 130372565}, {"start_time": 130421265, "end_time": 130457066, "duration": 35.801, "start_time_adj": 130422265, "end_time_adj": 130456066}, {"start_time": 130566167, "end_time": 130592671, "duration": 26.504, "start_time_adj": 130567167, "end_time_adj": 130591671}, {"start_time": 130674266, "end_time": 130695365, "duration": 21.099, "start_time_adj": 130675266, "end_time_adj": 130694365}, {"start_time": 130984265, "end_time": 131119966, "duration": 135.701, "start_time_adj": 130985265, "end_time_adj": 131118966}, {"start_time": 132142766, "end_time": 132187768, "duration": 45.002, "start_time_adj": 132143766, "end_time_adj": 132186768}, {"start_time": 132981965, "end_time": 132998166, "duration": 16.201, "start_time_adj": 132982965, "end_time_adj": 132997166}, {"start_time": 133270866, "end_time": 133310566, "duration": 39.7, "start_time_adj": 133271866, "end_time_adj": 133309566}, {"start_time": 133341466, "end_time": 133368666, "duration": 27.2, "start_time_adj": 133342466, "end_time_adj": 133367666}, {"start_time": 133539966, "end_time": 133585766, "duration": 45.8, "start_time_adj": 133540966, "end_time_adj": 133584766}, {"start_time": 133862065, "end_time": 133959666, "duration": 97.601, "start_time_adj": 133863065, "end_time_adj": 133958666}, {"start_time": 134186766, "end_time": 134201268, "duration": 14.502, "start_time_adj": 134187766, "end_time_adj": 134200268}, {"start_time": 134261566, "end_time": 134316565, "duration": 54.999, "start_time_adj": 134262566, "end_time_adj": 134315565}, {"start_time": 134590766, "end_time": 134608066, "duration": 17.3, "start_time_adj": 134591766, "end_time_adj": 134607066}, {"start_time": 134729766, "end_time": 134756565, "duration": 26.799, "start_time_adj": 134730766, "end_time_adj": 134755565}, {"start_time": 135192966, "end_time": 135302366, "duration": 109.4, "start_time_adj": 135193966, "end_time_adj": 135301366}, {"start_time": 135392065, "end_time": 135462965, "duration": 70.9, "start_time_adj": 135393065, "end_time_adj": 135461965}, {"start_time": 135467665, "end_time": 135476066, "duration": 8.401, "start_time_adj": 135468665, "end_time_adj": 135475066}, {"start_time": 135732065, "end_time": 135808466, "duration": 76.401, "start_time_adj": 135733065, "end_time_adj": 135807466}, {"start_time": 135880866, "end_time": 135930666, "duration": 49.8, "start_time_adj": 135881866, "end_time_adj": 135929666}, {"start_time": 136135465, "end_time": 136167866, "duration": 32.401, "start_time_adj": 136136465, "end_time_adj": 136166866}, {"start_time": 136470765, "end_time": 136533566, "duration": 62.801, "start_time_adj": 136471765, "end_time_adj": 136532566}, {"start_time": 137145765, "end_time": 137190367, "duration": 44.602, "start_time_adj": 137146765, "end_time_adj": 137189367}, {"start_time": 137304769, "end_time": 137355965, "duration": 51.196, "start_time_adj": 137305769, "end_time_adj": 137354965}, {"start_time": 137490465, "end_time": 137548566, "duration": 58.101, "start_time_adj": 137491465, "end_time_adj": 137547566}, {"start_time": 137574165, "end_time": 137582765, "duration": 8.6, "start_time_adj": 137575165, "end_time_adj": 137581765}], "long_static_intervals": [{"start_time": 130138203, "end_time": 130189665, "duration": 51.462, "gyro_count": 495, "mean_x": 0.6546841407063031, "mean_y": -1.3949990864956017, "mean_z": -0.4987350260368501, "std_x": 0.02756947465699522, "std_y": 0.040458196717098306, "std_z": 0.01768209107186074, "range_x": 0.2873838543891907, "range_y": 0.4877150058746338, "range_z": 0.11321902275085449}, {"start_time": 130254467, "end_time": 130273865, "duration": 19.398, "gyro_count": 174, "mean_x": 0.6547212504792488, "mean_y": -1.3878784549647365, "mean_z": -0.49856444371157677, "std_x": 0.029412334229817358, "std_y": 0.021681643120866685, "std_z": 0.01630187872929547, "range_x": 0.20900368690490723, "range_y": 0.16549229621887207, "range_z": 0.09578463435173035}, {"start_time": 130328666, "end_time": 130333067, "duration": 4.401, "gyro_count": 25, "mean_x": 0.6535208511352539, "mean_y": -1.3955236101150512, "mean_z": -0.5012870025634766, "std_x": 0.047466994265709835, "std_y": 0.023888897145856885, "std_z": 0.020434608558281067, "range_x": 0.1741647720336914, "range_y": 0.08708250522613525, "range_z": 0.07838010787963867}, {"start_time": 130337485, "end_time": 130373565, "duration": 36.08, "gyro_count": 340, "mean_x": 0.6589354210040148, "mean_y": -1.3914085128728082, "mean_z": -0.5045309105340172, "std_x": 0.02129999682210583, "std_y": 0.022251363975057593, "std_z": 0.015580779814825191, "range_x": 0.11321908235549927, "range_y": 0.11321902275085449, "range_z": 0.09578463435173035}, {"start_time": 130421265, "end_time": 130457066, "duration": 35.801, "gyro_count": 339, "mean_x": 0.6627541806845538, "mean_y": -1.397338464900104, "mean_z": -0.5020369413503855, "std_x": 0.023800739676673528, "std_y": 0.02033082152404687, "std_z": 0.015474269060252602, "range_x": 0.14805787801742554, "range_y": 0.1306236982345581, "range_z": 0.11321902275085449}, {"start_time": 130566167, "end_time": 130592671, "duration": 26.504, "gyro_count": 245, "mean_x": 0.6593921019106496, "mean_y": -1.3985892305568772, "mean_z": -0.5016015803327366, "std_x": 0.02294623062203282, "std_y": 0.02073480041968096, "std_z": 0.013687273724550742, "range_x": 0.1654624342918396, "range_y": 0.11318910121917725, "range_z": 0.06967779994010925}, {"start_time": 130674266, "end_time": 130695365, "duration": 21.099, "gyro_count": 191, "mean_x": 0.6574110135982174, "mean_y": -1.3963978533969499, "mean_z": -0.5031147241904473, "std_x": 0.037674823212742216, "std_y": 0.02137935317098363, "std_z": 0.015315354808005634, "range_x": 0.2525746822357178, "range_y": 0.11318910121917725, "range_z": 0.07838010787963867}, {"start_time": 130984265, "end_time": 131119966, "duration": 135.701, "gyro_count": 1337, "mean_x": 0.6519534009646014, "mean_y": -1.4030418284543877, "mean_z": -0.5011860175237962, "std_x": 0.024512746881387916, "std_y": 0.020285524519250073, "std_z": 0.015524809465605128, "range_x": 0.20900368690490723, "range_y": 0.130623459815979, "range_z": 0.09578463435173035}, {"start_time": 132142766, "end_time": 132187768, "duration": 45.002, "gyro_count": 431, "mean_x": 0.654060741173419, "mean_y": -1.3940399910070504, "mean_z": -0.4944924634736541, "std_x": 0.027652919139285366, "std_y": 0.021711070449935473, "std_z": 0.016924277263051103, "range_x": 0.16549235582351685, "range_y": 0.13932585716247559, "range_z": 0.10451674461364746}, {"start_time": 132981965, "end_time": 132998166, "duration": 16.201, "gyro_count": 143, "mean_x": 0.6452534461354876, "mean_y": -1.3780250482625895, "mean_z": -0.49555887396519, "std_x": 0.03887745278631804, "std_y": 0.04369933497119514, "std_z": 0.030333415929689158, "range_x": 0.2873837947845459, "range_y": 0.3831683397293091, "range_z": 0.20903348922729492}, {"start_time": 133270866, "end_time": 133310566, "duration": 39.7, "gyro_count": 378, "mean_x": 0.6495308405350125, "mean_y": -1.3879514487962874, "mean_z": -0.4897993747834806, "std_x": 0.07314453013104544, "std_y": 0.037095757950075706, "std_z": 0.025903130973614335, "range_x": 0.5225241482257843, "range_y": 0.40933477878570557, "range_z": 0.209003746509552}, {"start_time": 133341466, "end_time": 133368666, "duration": 27.2, "gyro_count": 252, "mean_x": 0.6507188363207711, "mean_y": -1.3846208060544634, "mean_z": -0.4897764733149892, "std_x": 0.023571662794791916, "std_y": 0.020867734305336467, "std_z": 0.01527115407438123, "range_x": 0.1741647720336914, "range_y": 0.11321902275085449, "range_z": 0.08711224794387817}, {"start_time": 133539966, "end_time": 133585766, "duration": 45.8, "gyro_count": 439, "mean_x": 0.6549966904188343, "mean_y": -1.3847839810582119, "mean_z": -0.49161207051374917, "std_x": 0.027232114269344257, "std_y": 0.023307103366168102, "std_z": 0.01518994631258097, "range_x": 0.26997923851013184, "range_y": 0.23514044284820557, "range_z": 0.09578463435173035}, {"start_time": 133862065, "end_time": 133959666, "duration": 97.601, "gyro_count": 957, "mean_x": 0.6502222996146702, "mean_y": -1.379728038233774, "mean_z": -0.4897417394967911, "std_x": 0.025716408147073176, "std_y": 0.02064500090504859, "std_z": 0.016021832440251346, "range_x": 0.20030146837234497, "range_y": 0.14802813529968262, "range_z": 0.08711224794387817}, {"start_time": 134186766, "end_time": 134201268, "duration": 14.502, "gyro_count": 126, "mean_x": 0.6496454808447096, "mean_y": -1.3762916468438648, "mean_z": -0.4915737188051617, "std_x": 0.02948051822747026, "std_y": 0.02468000161251293, "std_z": 0.019722302738051926, "range_x": 0.20030134916305542, "range_y": 0.14805781841278076, "range_z": 0.12192127108573914}, {"start_time": 134261566, "end_time": 134316565, "duration": 54.999, "gyro_count": 530, "mean_x": 0.6556854173822223, "mean_y": -1.3831801605674456, "mean_z": -0.4931420316111367, "std_x": 0.026343630039226483, "std_y": 0.022818443519100246, "std_z": 0.017704237227334694, "range_x": 0.18286699056625366, "range_y": 0.13932597637176514, "range_z": 0.11321902275085449}, {"start_time": 134590766, "end_time": 134608066, "duration": 17.3, "gyro_count": 154, "mean_x": 0.6520400070524836, "mean_y": -1.3816697829729552, "mean_z": -0.4966950130153012, "std_x": 0.03795049333015583, "std_y": 0.023192147897216422, "std_z": 0.015469857990058915, "range_x": 0.23514026403427124, "range_y": 0.16546249389648438, "range_z": 0.09581446647644043}, {"start_time": 134729766, "end_time": 134756565, "duration": 26.799, "gyro_count": 248, "mean_x": 0.6597021866229272, "mean_y": -1.3797733149220865, "mean_z": -0.49384841899718007, "std_x": 0.025647875607500467, "std_y": 0.02190707679779279, "std_z": 0.01564741329764386, "range_x": 0.15676021575927734, "range_y": 0.11321902275085449, "range_z": 0.07838013768196106}, {"start_time": 135192966, "end_time": 135302366, "duration": 109.4, "gyro_count": 1075, "mean_x": 0.6595471275684446, "mean_y": -1.3744112511568292, "mean_z": -0.49292827190354815, "std_x": 0.02597216603409549, "std_y": 0.021427399432556327, "std_z": 0.015917131453834412, "range_x": 0.21770590543746948, "range_y": 0.13932597637176514, "range_z": 0.08711224794387817}, {"start_time": 135392065, "end_time": 135462965, "duration": 70.9, "gyro_count": 689, "mean_x": 0.6602367834878764, "mean_y": -1.3818554149829771, "mean_z": -0.4925571476340467, "std_x": 0.02271117633149002, "std_y": 0.020789178208068818, "std_z": 0.016094889368364527, "range_x": 0.13935565948486328, "range_y": 0.130623459815979, "range_z": 0.12192127108573914}, {"start_time": 135467665, "end_time": 135476066, "duration": 8.401, "gyro_count": 65, "mean_x": 0.6629513098643376, "mean_y": -1.3811061675731953, "mean_z": -0.49346412603671735, "std_x": 0.02845085094398321, "std_y": 0.0283004127153436, "std_z": 0.016092753795542383, "range_x": 0.13935565948486328, "range_y": 0.15676021575927734, "range_z": 0.07838010787963867}, {"start_time": 135732065, "end_time": 135808466, "duration": 76.401, "gyro_count": 744, "mean_x": 0.6630381668607394, "mean_y": -1.3803117669397784, "mean_z": -0.49266641879434225, "std_x": 0.02698817829739304, "std_y": 0.022655125037699318, "std_z": 0.01550203558026003, "range_x": 0.18286705017089844, "range_y": 0.17416465282440186, "range_z": 0.08711224794387817}, {"start_time": 135880866, "end_time": 135930666, "duration": 49.8, "gyro_count": 479, "mean_x": 0.6638801030674658, "mean_y": -1.3816695101325844, "mean_z": -0.49284867970331225, "std_x": 0.027309512952764854, "std_y": 0.021366037302248668, "std_z": 0.016315398363241366, "range_x": 0.21770596504211426, "range_y": 0.1306236982345581, "range_z": 0.08711224794387817}, {"start_time": 136135465, "end_time": 136167866, "duration": 32.401, "gyro_count": 305, "mean_x": 0.662279740122498, "mean_y": -1.3833833233254855, "mean_z": -0.49472648902017563, "std_x": 0.025630557050888056, "std_y": 0.019266412810911078, "std_z": 0.017298273194736036, "range_x": 0.19159913063049316, "range_y": 0.11321902275085449, "range_z": 0.10448691248893738}, {"start_time": 136470765, "end_time": 136533566, "duration": 62.801, "gyro_count": 609, "mean_x": 0.6653548293121538, "mean_y": -1.3786752141755203, "mean_z": -0.49539683399529294, "std_x": 0.02432602923762772, "std_y": 0.021747329231690236, "std_z": 0.01614093177401737, "range_x": 0.1741945743560791, "range_y": 0.1306236982345581, "range_z": 0.10451674461364746}, {"start_time": 137145765, "end_time": 137190367, "duration": 44.602, "gyro_count": 427, "mean_x": 0.6652453713450555, "mean_y": -1.3741400518238684, "mean_z": -0.4952900531141205, "std_x": 0.03405926405620291, "std_y": 0.02494158483959668, "std_z": 0.01811830840776552, "range_x": 0.27871131896972656, "range_y": 0.14805805683135986, "range_z": 0.1393258273601532}, {"start_time": 137304769, "end_time": 137355965, "duration": 51.196, "gyro_count": 491, "mean_x": 0.6696651244114955, "mean_y": -1.3780903978891625, "mean_z": -0.49763477279552376, "std_x": 0.020390909743420435, "std_y": 0.020432196492163136, "std_z": 0.0154343668563821, "range_x": 0.11321902275085449, "range_y": 0.11321902275085449, "range_z": 0.08708235621452332}, {"start_time": 137490465, "end_time": 137548566, "duration": 58.101, "gyro_count": 562, "mean_x": 0.6690382455803746, "mean_y": -1.3808659026631256, "mean_z": -0.5000214810579273, "std_x": 0.02720346930051651, "std_y": 0.021743405914120124, "std_z": 0.015372376032724722, "range_x": 0.2264081835746765, "range_y": 0.12192130088806152, "range_z": 0.09578463435173035}, {"start_time": 137574165, "end_time": 137582765, "duration": 8.6, "gyro_count": 66, "mean_x": 0.662275364001592, "mean_y": -1.3906603943217883, "mean_z": -0.5020851709625938, "std_x": 0.02178714382201942, "std_y": 0.022821393145895714, "std_z": 0.013617098995581297, "range_x": 0.11321908235549927, "range_y": 0.12192118167877197, "range_z": 0.060975581407547}], "failed_intervals": [], "all_passed": true}]}