{"110112": [{"city": [], "province": "北京市", "adcode": "110112", "district": "通州区", "towncode": "110112109000", "streetNumber": {"number": "36号", "location": "116.544372,39.724564", "direction": "东北", "distance": "522.09", "street": "柴房路"}, "country": "中国", "township": "马驹桥镇", "businessAreas": [[]], "building": {"name": [], "type": []}, "neighborhood": {"name": [], "type": []}, "citycode": "010"}, {"city": [], "province": "北京市", "adcode": "110112", "district": "通州区", "towncode": "110112109000", "streetNumber": {"number": "36号", "location": "116.544372,39.724564", "direction": "东北", "distance": "175.436", "street": "柴房路"}, "country": "中国", "township": "马驹桥镇", "businessAreas": [[]], "building": {"name": [], "type": []}, "neighborhood": {"name": [], "type": []}, "citycode": "010"}, {"city": [], "province": "北京市", "adcode": "110112", "district": "通州区", "towncode": "110112109000", "streetNumber": {"number": "36号", "location": "116.544372,39.724564", "direction": "东北", "distance": "498.025", "street": "柴房路"}, "country": "中国", "township": "马驹桥镇", "businessAreas": [[]], "building": {"name": [], "type": []}, "neighborhood": {"name": [], "type": []}, "citycode": "010"}], "city": "北京市", "lnds_version": "8_25_04_03_02", "channel_number": "C04010307015", "lnds_path": "plaintext_lnds/lnds_for_gt/plaintext_20250401/"}