{"checker": "C10GnssDistanceSpeedInstantSpeed", "title": "GNSS距离速度和GNSS瞬时速度一致性检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250624_164200/AutoSdkDemo/online/vlane/20250624_164200/data.txt", "version": "1.0", "date": "2025-07-04 12:53:02", "type": "CheckResult", "checkResult": [{"checkItem": "median", "checkResult": "Pass", "checkResultStatistic": -0.046170320268338116, "checkRules": "阈值:[-1,1]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_16", "checkResult": "Pass", "checkResultStatistic": -1.131313110405006, "checkRules": "阈值:[-3.6,3.6]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_84", "checkResult": "Pass", "checkResultStatistic": 0.8787477400227677, "checkRules": "阈值:[-3.6,3.6]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_2_5", "checkResult": "Pass", "checkResultStatistic": -2.3994309513380787, "checkRules": "阈值:[-7.2,7.2]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_97_5", "checkResult": "Pass", "checkResultStatistic": 2.6878927625469657, "checkRules": "阈值:[-7.2,7.2]", "description": "不通过需手动排查"}]}