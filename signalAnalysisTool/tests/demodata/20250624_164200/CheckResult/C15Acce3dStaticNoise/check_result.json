{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250624_164200/AutoSdkDemo/online/vlane/20250624_164200/data.txt", "version": "1.0", "date": "2025-07-04 13:09:38", "type": "CheckResult", "checkResult": [{"checkItem": "加速度器静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 8<br>时长>2秒区间数: 8<br>通过区间数: 8<br>失败区间数: 0<br><br>区间1:<br>  时长: 10.40秒<br>  加速度器数据个数: 84<br>  标准差 - X:0.0007, Y:0.0008, Z:0.0009<br>  极值差 - X:0.0035, Y:0.0037, Z:0.0043<br>区间2:<br>  时长: 16.90秒<br>  加速度器数据个数: 149<br>  标准差 - X:0.0009, Y:0.0011, Z:0.0010<br>  极值差 - X:0.0051, Y:0.0065, Z:0.0065<br>区间3:<br>  时长: 24.40秒<br>  加速度器数据个数: 224<br>  标准差 - X:0.0009, Y:0.0011, Z:0.0012<br>  极值差 - X:0.0057, Y:0.0073, Z:0.0065<br>区间4:<br>  时长: 43.10秒<br>  加速度器数据个数: 411<br>  标准差 - X:0.0010, Y:0.0012, Z:0.0016<br>  极值差 - X:0.0053, Y:0.0084, Z:0.0096<br>区间5:<br>  时长: 6.40秒<br>  加速度器数据个数: 44<br>  标准差 - X:0.0007, Y:0.0009, Z:0.0010<br>  极值差 - X:0.0039, Y:0.0042, Z:0.0041<br>区间6:<br>  时长: 26.20秒<br>  加速度器数据个数: 242<br>  标准差 - X:0.0008, Y:0.0009, Z:0.0008<br>  极值差 - X:0.0047, Y:0.0064, Z:0.0064<br>区间7:<br>  时长: 22.70秒<br>  加速度器数据个数: 207<br>  标准差 - X:0.0009, Y:0.0009, Z:0.0010<br>  极值差 - X:0.0047, Y:0.0055, Z:0.0061<br>区间8:<br>  时长: 254.70秒<br>  加速度器数据个数: 2526<br>  标准差 - X:0.0019, Y:0.0011, Z:0.0022<br>  极值差 - X:0.0793, Y:0.0279, Z:0.1001", "checkRules": "所有静态区间(时长>2秒)的标准差<0.1<br>且极值差<0.2dps", "description": "不通过需手动排查"}]}