{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查详情", "version": "1.0", "date": "2025-07-04 13:09:38", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "加速度器静态噪声过大", "static_intervals": [{"start_time": 532369549, "end_time": 532379950, "duration": 10.401, "start_time_adj": 532370549, "end_time_adj": 532378950}, {"start_time": 533857549, "end_time": 533874449, "duration": 16.9, "start_time_adj": 533858549, "end_time_adj": 533873449}, {"start_time": 533937249, "end_time": 533961649, "duration": 24.4, "start_time_adj": 533938249, "end_time_adj": 533960649}, {"start_time": 534069149, "end_time": 534112249, "duration": 43.1, "start_time_adj": 534070149, "end_time_adj": 534111249}, {"start_time": 534198549, "end_time": 534204949, "duration": 6.4, "start_time_adj": 534199549, "end_time_adj": 534203949}, {"start_time": 534209050, "end_time": 534235249, "duration": 26.199, "start_time_adj": 534210050, "end_time_adj": 534234249}, {"start_time": 534506049, "end_time": 534528749, "duration": 22.7, "start_time_adj": 534507049, "end_time_adj": 534527749}, {"start_time": 534548849, "end_time": 534803549, "duration": 254.7, "start_time_adj": 534549849, "end_time_adj": 534802549}], "long_static_intervals": [{"start_time": 532369549, "end_time": 532379950, "duration": 10.401, "gyro_count": 84, "mean_x": -0.024976941690381085, "mean_y": -0.015220133388148887, "mean_z": 1.0006856315192723, "std_x": 0.0007185402675048658, "std_y": 0.0008009302681154712, "std_z": 0.0009361976912357809, "range_x": 0.003465898334980011, "range_y": 0.0036696773022413254, "range_z": 0.004281461238861084}, {"start_time": 533857549, "end_time": 533874449, "duration": 16.9, "gyro_count": 149, "mean_x": -0.017614538681337097, "mean_y": -0.013000703741790064, "mean_z": 1.0008743473347401, "std_x": 0.000946729675848903, "std_y": 0.001101504798298872, "std_z": 0.0010342178224365325, "range_x": 0.0050968388095498085, "range_y": 0.006524002179503441, "range_z": 0.006524026393890381}, {"start_time": 533937249, "end_time": 533961649, "duration": 24.4, "gyro_count": 224, "mean_x": -0.020612169314907596, "mean_y": -0.01238031659574647, "mean_z": 1.0007681790739298, "std_x": 0.0009315861705957836, "std_y": 0.0010913704693774051, "std_z": 0.0011698120786666386, "range_x": 0.005708534270524979, "range_y": 0.007339472882449627, "range_z": 0.00652390718460083}, {"start_time": 534069149, "end_time": 534112249, "duration": 43.1, "gyro_count": 411, "mean_x": -0.02051062697494842, "mean_y": -0.005635050260813096, "mean_z": 1.0008618760573023, "std_x": 0.0009575234568750319, "std_y": 0.0011527421086584934, "std_z": 0.0016309726198698341, "range_x": 0.0053007397800683975, "range_y": 0.008358841412700713, "range_z": 0.009582161903381348}, {"start_time": 534198549, "end_time": 534204949, "duration": 6.4, "gyro_count": 44, "mean_x": -0.014528318562290886, "mean_y": -0.013580765765668317, "mean_z": 1.0008618438785726, "std_x": 0.0007316098904694581, "std_y": 0.0008864507494652674, "std_z": 0.0009601585939127417, "range_x": 0.003873574547469616, "range_y": 0.004179481416940689, "range_z": 0.004077494144439697}, {"start_time": 534209050, "end_time": 534235249, "duration": 26.199, "gyro_count": 242, "mean_x": -0.01361825367174119, "mean_y": -0.015609386534822628, "mean_z": 1.0009128150861126, "std_x": 0.0007576728765101462, "std_y": 0.000863590062842746, "std_z": 0.0008281476972354253, "range_x": 0.004689163528382778, "range_y": 0.006421996280550957, "range_z": 0.0064220428466796875}, {"start_time": 534506049, "end_time": 534528749, "duration": 22.7, "gyro_count": 207, "mean_x": -0.013493578354625599, "mean_y": -0.011546432279086343, "mean_z": 1.0008386266980194, "std_x": 0.0008685841807477213, "std_y": 0.0009363933992915044, "std_z": 0.001001556683944848, "range_x": 0.004689163528382778, "range_y": 0.005504635162651539, "range_z": 0.006116211414337158}, {"start_time": 534548849, "end_time": 534803549, "duration": 254.7, "gyro_count": 2526, "mean_x": -0.026500824902476544, "mean_y": -0.006164472096774584, "mean_z": 1.0007226744154947, "std_x": 0.001944663610578198, "std_y": 0.0010575491385785376, "std_z": 0.002204931465677145, "range_x": 0.07930681109428406, "range_y": 0.027930612675845623, "range_z": 0.1001020073890686}], "failed_intervals": [], "all_passed": true}]}