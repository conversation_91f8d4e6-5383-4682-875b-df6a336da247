{"checker": "C10GnssDistanceSpeedInstantSpeed", "title": "GNSS距离速度和GNSS瞬时速度一致性检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_154816/AutoSdkDemo/online/vlane/20250618_154816/data.txt", "version": "1.0", "date": "2025-07-04 11:10:52", "type": "CheckResult", "checkResult": [{"checkItem": "median", "checkResult": "Pass", "checkResultStatistic": 0.05684138700858199, "checkRules": "阈值:[-1,1]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_16", "checkResult": "Pass", "checkResultStatistic": -1.308983381572503, "checkRules": "阈值:[-3.6,3.6]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_84", "checkResult": "Pass", "checkResultStatistic": 0.9817671386346234, "checkRules": "阈值:[-3.6,3.6]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_2_5", "checkResult": "Pass", "checkResultStatistic": -2.3916216128313064, "checkRules": "阈值:[-7.2,7.2]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_97_5", "checkResult": "Pass", "checkResultStatistic": 2.439812373146955, "checkRules": "阈值:[-7.2,7.2]", "description": "不通过需手动排查"}]}