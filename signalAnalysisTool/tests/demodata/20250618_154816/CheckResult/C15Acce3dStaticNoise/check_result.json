{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_154816/AutoSdkDemo/online/vlane/20250618_154816/data.txt", "version": "1.0", "date": "2025-07-04 11:22:39", "type": "CheckResult", "checkResult": [{"checkItem": "加速度器静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 2<br>时长>2秒区间数: 2<br>通过区间数: 2<br>失败区间数: 0<br><br>区间1:<br>  时长: 12.70秒<br>  加速度器数据个数: 107<br>  标准差 - X:0.0011, Y:0.0010, Z:0.0014<br>  极值差 - X:0.0055, Y:0.0049, Z:0.0084<br>区间2:<br>  时长: 20.90秒<br>  加速度器数据个数: 189<br>  标准差 - X:0.0009, Y:0.0009, Z:0.0012<br>  极值差 - X:0.0051, Y:0.0064, Z:0.0066", "checkRules": "所有静态区间(时长>2秒)的标准差<0.1<br>且极值差<0.2dps", "description": "不通过需手动排查"}]}