{"checker": "C14GryoStaticNoise", "title": "陀螺仪静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_154816/AutoSdkDemo/online/vlane/20250618_154816/data.txt", "version": "1.0", "date": "2025-07-04 11:22:37", "type": "CheckResult", "checkResult": [{"checkItem": "陀螺静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 2<br>时长>2秒区间数: 2<br>通过区间数: 2<br>失败区间数: 0<br><br>区间1:<br>  时长: 12.70秒<br>  陀螺仪数据个数: 107<br>  标准差 - X:0.0209, Y:0.0258, Z:0.0156<br>  极值差 - X:0.1003, Y:0.1501, Z:0.0877<br>区间2:<br>  时长: 20.90秒<br>  陀螺仪数据个数: 189<br>  标准差 - X:0.0207, Y:0.0199, Z:0.0162<br>  极值差 - X:0.1123, Y:0.1129, Z:0.0756", "checkRules": "所有静态区间(时长>2秒)的标准差<0.5dps<br>且极值差<1.5dps", "description": "不通过需手动排查"}]}