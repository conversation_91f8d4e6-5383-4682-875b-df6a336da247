{"checker": "C10GnssDistanceSpeedInstantSpeed", "title": "GNSS距离速度和GNSS瞬时速度一致性检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250610_041423/AutoSdkDemo/online/vlane/20250610_041423/data.txt", "version": "1.0", "date": "2025-07-10 18:05:22", "type": "CheckResult", "checkResult": [{"checkItem": "median", "checkResult": "Pass", "checkResultStatistic": -0.034824953419587246, "checkRules": "阈值:[-1,1]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_16", "checkResult": "Pass", "checkResultStatistic": -0.9969396064950417, "checkRules": "阈值:[-3.6,3.6]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_84", "checkResult": "Pass", "checkResultStatistic": 1.3439310521180285, "checkRules": "阈值:[-3.6,3.6]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_2_5", "checkResult": "Pass", "checkResultStatistic": -5.586007373973745, "checkRules": "阈值:[-7.2,7.2]", "description": "不通过需手动排查"}, {"checkItem": "quantiles_97_5", "checkResult": "Fail", "checkResultStatistic": 8.017205834415426, "checkRules": "阈值:[-7.2,7.2]", "description": "不通过需手动排查"}]}