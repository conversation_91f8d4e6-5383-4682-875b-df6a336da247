2025-07-10 18:05:21,403 - C09GnssPulseSpeed - INFO - init
2025-07-10 18:05:21,403 - C09GnssPulseSpeed - INFO - check
2025-07-10 18:05:21,978 - C09GnssPulseSpeed - INFO - cross_correlation_function, gnss length : 1483 , pulse length : 14979
2025-07-10 18:05:21,981 - C09GnssPulseSpeed - INFO - 最大延迟 max_lag = 0.1 s
2025-07-10 18:05:21,981 - C09GnssPulseSpeed - INFO - check_result: {'checker': 'gnssPulseSpeedChecker', 'title': 'GNSS速度和轮速不同步', 'datatxt': '/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250610_041423/AutoSdkDemo/online/vlane/20250610_041423/data.txt', 'version': '1.0', 'date': '2025-07-10 18:05:21', 'type': 'CheckResult', 'checkResult': {'checkItem': 'GNSS速度和轮速不同步相关性函数', 'checkList': [{'checkDataTime': '22256915455 - 22258398815', 'checkResultStatistic': '最大延迟0.1s', 'checkResult': 'Pass'}], 'checkRules': '最大延迟<=0.2s', 'description': '不通过需手动排查'}}
