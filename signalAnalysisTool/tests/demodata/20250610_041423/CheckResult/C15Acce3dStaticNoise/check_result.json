{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250610_041423/AutoSdkDemo/online/vlane/20250610_041423/data.txt", "version": "1.0", "date": "2025-07-10 18:05:27", "type": "CheckResult", "checkResult": [{"checkItem": "加速度器静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 9<br>时长>2秒区间数: 9<br>通过区间数: 9<br>失败区间数: 0<br><br>区间1:<br>  时长: 20.01秒<br>  加速度器数据个数: 182<br>  标准差 - X:0.0013, Y:0.0007, Z:0.0009<br>  极值差 - X:0.0093, Y:0.0046, Z:0.0059<br>区间2:<br>  时长: 61.36秒<br>  加速度器数据个数: 600<br>  标准差 - X:0.0006, Y:0.0007, Z:0.0011<br>  极值差 - X:0.0049, Y:0.0063, Z:0.0207<br>区间3:<br>  时长: 13.25秒<br>  加速度器数据个数: 114<br>  标准差 - X:0.0006, Y:0.0007, Z:0.0017<br>  极值差 - X:0.0037, Y:0.0049, Z:0.0141<br>区间4:<br>  时长: 4.13秒<br>  加速度器数据个数: 22<br>  标准差 - X:0.0005, Y:0.0015, Z:0.0038<br>  极值差 - X:0.0020, Y:0.0054, Z:0.0139<br>区间5:<br>  时长: 80.79秒<br>  加速度器数据个数: 795<br>  标准差 - X:0.0004, Y:0.0006, Z:0.0011<br>  极值差 - X:0.0032, Y:0.0066, Z:0.0186<br>区间6:<br>  时长: 21.40秒<br>  加速度器数据个数: 195<br>  标准差 - X:0.0004, Y:0.0007, Z:0.0018<br>  极值差 - X:0.0029, Y:0.0069, Z:0.0193<br>区间7:<br>  时长: 45.97秒<br>  加速度器数据个数: 441<br>  标准差 - X:0.0004, Y:0.0007, Z:0.0016<br>  极值差 - X:0.0039, Y:0.0073, Z:0.0315<br>区间8:<br>  时长: 31.34秒<br>  加速度器数据个数: 295<br>  标准差 - X:0.0004, Y:0.0007, Z:0.0022<br>  极值差 - X:0.0022, Y:0.0080, Z:0.0315<br>区间9:<br>  时长: 4.37秒<br>  加速度器数据个数: 23<br>  标准差 - X:0.0007, Y:0.0020, Z:0.0046<br>  极值差 - X:0.0034, Y:0.0083, Z:0.0161", "checkRules": "所有静态区间(时长>2秒)的标准差<0.1<br>且极值差<0.2dps", "description": "不通过需手动排查"}]}