{"checker": "C04TimestampRepeatFrame", "title": "时间戳重帧检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250610_041423/AutoSdkDemo/online/vlane/20250610_041423/data.txt", "version": "1.0", "date": "2025-07-10 18:05:15", "type": "CheckResult", "checkResult": [{"checkItem": "acce3d时间重帧率", "checkResult": "Pass", "checkResultStatistic": 0.00013352026169971293, "checkRules": "时间重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro时间重帧率", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "时间重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "pulse时间重帧率", "checkResult": "Pass", "checkResultStatistic": 0.00013352026169971293, "checkRules": "时间重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gnss时间重帧率", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "时间重帧率<0.0005", "description": "不通过需手动排查"}]}