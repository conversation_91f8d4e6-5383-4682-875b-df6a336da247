{"checker": "C07InterfaceDelay", "title": "接口延时检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250702_141903/AutoSdkDemo/online/vlane/20250702_141903/data.txt", "version": "1.0", "date": "2025-07-03 16:17:03", "type": "CheckResult", "checkResult": [{"checkItem": "acce3d接口延时检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "<br>卡顿率<0.0001\n            <br>最大卡顿时间<200ms\n\n            <br>接口延时的均值和中位数<100ms", "description": ""}, {"checkItem": "gyro接口延时检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "<br>卡顿率<0.0001\n            <br>最大卡顿时间<200ms\n\n            <br>接口延时的均值和中位数<100ms", "description": ""}, {"checkItem": "pulse接口延时检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "<br>卡顿率<0.0001\n            <br>最大卡顿时间<200ms\n\n            <br>接口延时的均值和中位数<100ms", "description": ""}, {"checkItem": "gnss接口延时检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "<br>卡顿率<0.0001\n            <br>最大卡顿时间<1500ms\n\n            <br>接口延时的均值和中位数<100ms", "description": ""}, {"checkItem": "image接口延时检查", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "<br>卡顿率<0.0001\n            <br>最大卡顿时间<1500ms\n\n            <br>接口延时的均值和中位数<100ms", "description": ""}]}