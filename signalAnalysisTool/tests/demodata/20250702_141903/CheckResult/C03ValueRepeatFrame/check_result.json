{"checker": "C03ValueRepeatFrame", "title": "数值重帧检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250702_141903/AutoSdkDemo/online/vlane/20250702_141903/data.txt", "version": "1.0", "date": "2025-07-03 16:16:45", "type": "CheckResult", "checkResult": [{"checkItem": "acce3d数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.12712571159215344, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro_xyz数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.08466586895826535, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro_temperature数值重帧率", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "数值重帧率<0", "description": "不通过需手动排查"}, {"checkItem": "pulse数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.1806084339533703, "checkRules": "数值重帧率<0.01", "description": "不通过需手动排查"}, {"checkItem": "gnss数值重帧率", "checkResult": "Fail", "checkResultStatistic": 0.0009069472156720479, "checkRules": "数值重帧率<0.0005", "description": "不通过需手动排查"}]}