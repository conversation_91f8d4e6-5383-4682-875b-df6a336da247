<html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>                <div id="4bb648a2-9a5b-4bd5-9987-51c40e2d2b53" class="plotly-graph-div" style="height:1000px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("4bb648a2-9a5b-4bd5-9987-51c40e2d2b53")) {                    Plotly.newPlot(                        "4bb648a2-9a5b-4bd5-9987-51c40e2d2b53",                        [{"cells":{"align":["left","center","left","center","center"],"fill":{"color":[["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"]]},"format":[null,null,"html",null,null],"values":[["原始轮速误差检查","轮速尺度因子检查","标定轮速误差检查（全场景）","标定轮速误差检查(转弯或掉头)","误差超限积分检查"],["\u003cbr\u003e轮速误差均值:0.78\u003cbr\u003e轮速误差中位数:0.83","\u003cbr\u003e轮速尺度因子均值:1.03                    \u003cbr\u003e轮速尺度因子中位数:1.03                    \u003cbr\u003e16%分位轮速尺度因子:1.00                    \u003cbr\u003e84%分位轮速尺度因子:1.02                    \u003cbr\u003e轮速尺度因子均值检查结果:True                    \u003cbr\u003e轮速尺度因子中位数检查结果:True                    \u003cbr\u003e16%分位轮速尺度因子检查结果:True                    \u003cbr\u003e84%分位轮速尺度因子检查结果:True","\u003cbr\u003e轮速误差均值:0.01\u003cbr\u003e轮速误差中位数:0.00","\u003cbr\u003e轮速误差均值:-0.37\u003cbr\u003e轮速误差中位数:-0.18","\u003cbr\u003e超限区间误差积分最大累计值:14.82"],["误差均值或中位数在正负1km\u002fh之内","误均值或中位数在0.95~1.05范围\u003cbr\u003e尺度因子的16%或84%分位数在0.95~1.05范围之内","误差均值或中位数在正负1km\u002fh之内","误差均值或中位数在正负1km\u002fh之内","误差超限区间内轮速误差积分小于3.5米（全场景）"],["Pass","Pass","Pass","Pass","Fail"],["","","","",""]]},"header":{"align":"center","fill":{"color":"paleturquoise"},"values":["检查项","统计数值","检查规则","检查结果","描述"]},"type":"table","domain":{"x":[0.0,1.0],"y":[0.525,1.0]}},{"cells":{"align":"center","fill":{"color":[["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"],["lavender","lavender","lavender","lavender","lightcoral"]]},"values":[["原始轮速误差检查","轮速尺度因子检查","标定轮速误差检查（全场景）","标定轮速误差检查(转弯或掉头)","误差超限积分检查"],["[\n  {\n    \"pulse_sourceTickTime\": 45416068,\n    \"pulse_speed\": 6.131249904632568,\n    \"gt_speed\": 2.1998161639137908,\n    \"speed_diff\": -3.9314337407187776,\n    \"angular_velocity\": -7.794949999999972\n  },\n  {\n    \"pulse_sourceTickTime\": 45416167,\n    \"pulse_speed\": 6.412499904632568,\n    \"gt_speed\": 2.453915135158687,\n    \"speed_diff\": -3.958584769473881,\n    \"angular_velocity\": -8.768230000000017\n  },\n  {\n    \"pulse_sourceTickTime\": 45416268,\n    \"pulse_speed\": 6.862499713897705,\n    \"gt_speed\": 2.7904039649989625,\n    \"speed_diff\": -4.072095748898743,\n    \"angular_velocity\": -9.556579999999997\n  },\n  {\n    \"pulse_sourceTickTime\": 45416367,\n    \"pulse_speed\": 7.368749618530273,\n    \"gt_speed\": 3.176075069487668,\n    \"speed_diff\": -4.192674549042605,\n    \"angular_velocity\": -10.173489999999958\n  },\n  {\n    \"pulse_sourceTickTime\": 45416467,\n    \"pulse_speed\": 7.818749904632568,\n    \"gt_speed\": 3.5862445023630354,\n    \"speed_diff\": -4.232505402269533,\n    \"angular_velocity\": -10.615769999999998\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 45412068,\n    \"pulse_speed\": 0.28125,\n    \"gt_speed\": 0.007971852605528686,\n    \"scale\": 0.02834436481965755\n  },\n  {\n    \"pulse_sourceTickTime\": 45412168,\n    \"pulse_speed\": 0.39374998211860657,\n    \"gt_speed\": 0.0099075073578666,\n    \"scale\": 0.025161924591230154\n  },\n  {\n    \"pulse_sourceTickTime\": 45412268,\n    \"pulse_speed\": 0.6187499761581421,\n    \"gt_speed\": 0.016831499500692158,\n    \"scale\": 0.027202424483633934\n  },\n  {\n    \"pulse_sourceTickTime\": 45412368,\n    \"pulse_speed\": 0.731249988079071,\n    \"gt_speed\": 0.038337903201909815,\n    \"scale\": 0.052427902669263754\n  },\n  {\n    \"pulse_sourceTickTime\": 45412467,\n    \"pulse_speed\": 0.7874999642372131,\n    \"gt_speed\": 0.072031626910126,\n    \"scale\": 0.09146873673816247\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 45549767,\n    \"pulse_speed\": 34.13988156984329,\n    \"gt_speed\": 37.790818401432205,\n    \"speed_diff\": 3.650936831588915,\n    \"angular_velocity\": 0.3765799999999331\n  },\n  {\n    \"pulse_sourceTickTime\": 45549883,\n    \"pulse_speed\": 33.6286369295311,\n    \"gt_speed\": 37.3315234463295,\n    \"speed_diff\": 3.702886516798401,\n    \"angular_velocity\": 0.41167999999998983\n  },\n  {\n    \"pulse_sourceTickTime\": 45549973,\n    \"pulse_speed\": 33.1173922892189,\n    \"gt_speed\": 37.01556656080046,\n    \"speed_diff\": 3.89817427158156,\n    \"angular_velocity\": 0.3959700000001476\n  },\n  {\n    \"pulse_sourceTickTime\": 45550068,\n    \"pulse_speed\": 32.8333666329689,\n    \"gt_speed\": 36.68594702325117,\n    \"speed_diff\": 3.85258039028227,\n    \"angular_velocity\": 0.43685999999979686\n  },\n  {\n    \"pulse_sourceTickTime\": 45550169,\n    \"pulse_speed\": 32.43572763234329,\n    \"gt_speed\": 36.384675105567624,\n    \"speed_diff\": 3.9489474732243366,\n    \"angular_velocity\": 0.18356000000011363\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 48147968,\n    \"pulse_speed\": 37.46779763899993,\n    \"gt_speed\": 33.85591961658407,\n    \"speed_diff\": -3.6118780224158655,\n    \"angular_velocity\": 10.67935999999996\n  },\n  {\n    \"pulse_sourceTickTime\": 48148067,\n    \"pulse_speed\": 37.86699715299987,\n    \"gt_speed\": 34.17687282094111,\n    \"speed_diff\": -3.6901243320587653,\n    \"angular_velocity\": 11.0154\n  },\n  {\n    \"pulse_sourceTickTime\": 48148167,\n    \"pulse_speed\": 38.26619666699981,\n    \"gt_speed\": 34.50680123653352,\n    \"speed_diff\": -3.7593954304662915,\n    \"angular_velocity\": 10.869980000000012\n  },\n  {\n    \"pulse_sourceTickTime\": 48148269,\n    \"pulse_speed\": 38.4943134375,\n    \"gt_speed\": 34.85360433254936,\n    \"speed_diff\": -3.6407091049506377,\n    \"angular_velocity\": 10.143699999999995\n  }\n]","[\n  {\n    \"start_time\": 50868568,\n    \"end_time\": 50875067\n  }\n]"]]},"header":{"align":"center","fill":{"color":"paleturquoise"},"values":["检查项","检查详情"]},"type":"table","domain":{"x":[0.0,1.0],"y":[0.0,0.475]}}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"annotations":[{"font":{"size":16},"showarrow":false,"text":"轮速精度检查结果","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"轮速精度检查详情","x":0.5,"xanchor":"center","xref":"paper","y":0.475,"yanchor":"bottom","yref":"paper"}],"title":{"text":"\u003cb\u003e轮速精度检查结果汇总\u003c\u002fb\u003e","x":0.5},"margin":{"t":100,"b":50,"l":50,"r":50},"height":1000,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html>