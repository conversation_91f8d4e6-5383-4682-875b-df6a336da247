{"checker": "C14GryoStaticNoise", "title": "陀螺仪静态噪声检查详情", "version": "1.0", "date": "2025-07-03 16:23:15", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "陀螺静态噪声过大", "static_intervals": [{"start_time": 45360537, "end_time": 45412068, "duration": 51.531, "start_time_adj": 45361537, "end_time_adj": 45411068}, {"start_time": 45467167, "end_time": 45473267, "duration": 6.1, "start_time_adj": 45468167, "end_time_adj": 45472267}, {"start_time": 46161071, "end_time": 46179969, "duration": 18.898, "start_time_adj": 46162071, "end_time_adj": 46178969}, {"start_time": 46282069, "end_time": 46292368, "duration": 10.299, "start_time_adj": 46283069, "end_time_adj": 46291368}, {"start_time": 47176368, "end_time": 47183870, "duration": 7.502, "start_time_adj": 47177368, "end_time_adj": 47182870}, {"start_time": 47188968, "end_time": 47217767, "duration": 28.799, "start_time_adj": 47189968, "end_time_adj": 47216767}, {"start_time": 47528667, "end_time": 47550468, "duration": 21.801, "start_time_adj": 47529667, "end_time_adj": 47549468}, {"start_time": 47648167, "end_time": 47675667, "duration": 27.5, "start_time_adj": 47649167, "end_time_adj": 47674667}, {"start_time": 47719768, "end_time": 47734668, "duration": 14.9, "start_time_adj": 47720768, "end_time_adj": 47733668}, {"start_time": 47785468, "end_time": 47891269, "duration": 105.801, "start_time_adj": 47786468, "end_time_adj": 47890269}, {"start_time": 47930067, "end_time": 48016369, "duration": 86.302, "start_time_adj": 47931067, "end_time_adj": 48015369}, {"start_time": 48386769, "end_time": 48397768, "duration": 10.999, "start_time_adj": 48387769, "end_time_adj": 48396768}, {"start_time": 48404367, "end_time": 48411169, "duration": 6.802, "start_time_adj": 48405367, "end_time_adj": 48410169}, {"start_time": 48429067, "end_time": 48442268, "duration": 13.201, "start_time_adj": 48430067, "end_time_adj": 48441268}, {"start_time": 48674268, "end_time": 48692099, "duration": 17.831, "start_time_adj": 48675268, "end_time_adj": 48691099}, {"start_time": 49031568, "end_time": 49072467, "duration": 40.899, "start_time_adj": 49032568, "end_time_adj": 49071467}, {"start_time": 49141368, "end_time": 49167468, "duration": 26.1, "start_time_adj": 49142368, "end_time_adj": 49166468}, {"start_time": 49425268, "end_time": 49440968, "duration": 15.7, "start_time_adj": 49426268, "end_time_adj": 49439968}, {"start_time": 50594667, "end_time": 50608074, "duration": 13.407, "start_time_adj": 50595667, "end_time_adj": 50607074}, {"start_time": 50642868, "end_time": 50658667, "duration": 15.799, "start_time_adj": 50643868, "end_time_adj": 50657667}, {"start_time": 50669867, "end_time": 50695267, "duration": 25.4, "start_time_adj": 50670867, "end_time_adj": 50694267}], "long_static_intervals": [{"start_time": 45360537, "end_time": 45412068, "duration": 51.531, "gyro_count": 496, "mean_x": 0.6670061605832269, "mean_y": -1.3724689860978434, "mean_z": -0.5093336792121972, "std_x": 0.02787096753907695, "std_y": 0.02234104852505655, "std_z": 0.015507453085296254, "range_x": 0.1741945743560791, "range_y": 0.14805805683135986, "range_z": 0.10451680421829224}, {"start_time": 45467167, "end_time": 45473267, "duration": 6.1, "gyro_count": 41, "mean_x": 0.6701633857517708, "mean_y": -1.3743167097975568, "mean_z": -0.5093675139473706, "std_x": 0.021854163530916487, "std_y": 0.023786754953037215, "std_z": 0.013363008147673138, "range_x": 0.07838010787963867, "range_y": 0.09581434726715088, "range_z": 0.05224347114562988}, {"start_time": 46161071, "end_time": 46179969, "duration": 18.898, "gyro_count": 169, "mean_x": 0.6657438779018334, "mean_y": -1.3841593963860055, "mean_z": -0.5171761339938147, "std_x": 0.025547084475008107, "std_y": 0.021672100694230076, "std_z": 0.01499302748133472, "range_x": 0.1741647720336914, "range_y": 0.10448694229125977, "range_z": 0.0870823860168457}, {"start_time": 46282069, "end_time": 46292368, "duration": 10.299, "gyro_count": 83, "mean_x": 0.6670197973768395, "mean_y": -1.3916492993573109, "mean_z": -0.514560832316617, "std_x": 0.025191191149805822, "std_y": 0.024435199003034012, "std_z": 0.01240432491755854, "range_x": 0.13935565948486328, "range_y": 0.11321902275085449, "range_z": 0.05224347114562988}, {"start_time": 47176368, "end_time": 47183870, "duration": 7.502, "gyro_count": 56, "mean_x": 0.6645226244415555, "mean_y": -1.391722749386515, "mean_z": -0.5043431280979088, "std_x": 0.029284530654751843, "std_y": 0.020771082935735066, "std_z": 0.017436755634470197, "range_x": 0.13935565948486328, "range_y": 0.11321902275085449, "range_z": 0.07838007807731628}, {"start_time": 47188968, "end_time": 47217767, "duration": 28.799, "gyro_count": 268, "mean_x": 0.6611320727796697, "mean_y": -1.3921339218296223, "mean_z": -0.5084325428774108, "std_x": 0.03399857358446292, "std_y": 0.024190994062938304, "std_z": 0.015889292664279804, "range_x": 0.19159913063049316, "range_y": 0.16546249389648438, "range_z": 0.09578466415405273}, {"start_time": 47528667, "end_time": 47550468, "duration": 21.801, "gyro_count": 199, "mean_x": 0.6656865594375073, "mean_y": -1.3950969334223762, "mean_z": -0.5091008259423414, "std_x": 0.027224406575469245, "std_y": 0.020905226019061567, "std_z": 0.01569653060779912, "range_x": 0.13935565948486328, "range_y": 0.12192118167877197, "range_z": 0.06967779994010925}, {"start_time": 47648167, "end_time": 47675667, "duration": 27.5, "gyro_count": 255, "mean_x": 0.6609579193825815, "mean_y": -1.395482816883162, "mean_z": -0.5088756249231451, "std_x": 0.026501036786182684, "std_y": 0.0213748842403202, "std_z": 0.013977404931501736, "range_x": 0.19159913063049316, "range_y": 0.12192118167877197, "range_z": 0.09578466415405273}, {"start_time": 47719768, "end_time": 47734668, "duration": 14.9, "gyro_count": 130, "mean_x": 0.66429012463643, "mean_y": -1.3997983978344843, "mean_z": -0.5095397346294843, "std_x": 0.024893557027221472, "std_y": 0.02221842628825992, "std_z": 0.016559924399340117, "range_x": 0.13065332174301147, "range_y": 0.12192130088806152, "range_z": 0.07838007807731628}, {"start_time": 47785468, "end_time": 47891269, "duration": 105.801, "gyro_count": 1038, "mean_x": 0.6628193224785645, "mean_y": -1.4000706649699421, "mean_z": -0.5087937780428025, "std_x": 0.023309888671499353, "std_y": 0.021784813905585773, "std_z": 0.014910629316333182, "range_x": 0.20900368690490723, "range_y": 0.16546249389648438, "range_z": 0.11321905255317688}, {"start_time": 47930067, "end_time": 48016369, "duration": 86.302, "gyro_count": 844, "mean_x": 0.6607344976816132, "mean_y": -1.3960860676675046, "mean_z": -0.5071727394775192, "std_x": 0.023353208867719775, "std_y": 0.0205618260209861, "std_z": 0.015161712594781133, "range_x": 0.1480579972267151, "range_y": 0.13935554027557373, "range_z": 0.08708235621452332}, {"start_time": 48386769, "end_time": 48397768, "duration": 10.999, "gyro_count": 90, "mean_x": 0.6559776167074839, "mean_y": -1.3969176769256593, "mean_z": -0.5085058010286755, "std_x": 0.039335414542607534, "std_y": 0.030861710738242946, "std_z": 0.018902618350105965, "range_x": 0.22643804550170898, "range_y": 0.15676021575927734, "range_z": 0.10448691248893738}, {"start_time": 48404367, "end_time": 48411169, "duration": 6.802, "gyro_count": 49, "mean_x": 0.6480180827938781, "mean_y": -1.3957462043178326, "mean_z": -0.507074988618189, "std_x": 0.02010680962361557, "std_y": 0.030984627540271995, "std_z": 0.01669690143408048, "range_x": 0.09581446647644043, "range_y": 0.13935554027557373, "range_z": 0.07838007807731628}, {"start_time": 48429067, "end_time": 48442268, "duration": 13.201, "gyro_count": 113, "mean_x": 0.6551737524239363, "mean_y": -1.3919697451380502, "mean_z": -0.5044256936659856, "std_x": 0.059569946488118794, "std_y": 0.023303038080692805, "std_z": 0.01694851187052389, "range_x": 0.41803720593452454, "range_y": 0.130623459815979, "range_z": 0.10451674461364746}, {"start_time": 48674268, "end_time": 48692099, "duration": 17.831, "gyro_count": 159, "mean_x": 0.6554170108441287, "mean_y": -1.3957886358477034, "mean_z": -0.5068721147078388, "std_x": 0.04616859735139446, "std_y": 0.03786832630798868, "std_z": 0.01914351819319176, "range_x": 0.2786814570426941, "range_y": 0.21773576736450195, "range_z": 0.10451680421829224}, {"start_time": 49031568, "end_time": 49072467, "duration": 40.899, "gyro_count": 389, "mean_x": 0.6473957234299275, "mean_y": -1.3948668051501472, "mean_z": -0.506552557712349, "std_x": 0.028450918709964256, "std_y": 0.02105462968098229, "std_z": 0.016666846702879417, "range_x": 0.36579370498657227, "range_y": 0.130623459815979, "range_z": 0.1132189929485321}, {"start_time": 49141368, "end_time": 49167468, "duration": 26.1, "gyro_count": 242, "mean_x": 0.6482415455431978, "mean_y": -1.3947659898395381, "mean_z": -0.5059836141087792, "std_x": 0.033261183933651, "std_y": 0.027307250647009517, "std_z": 0.0165967857211286, "range_x": 0.24384260177612305, "range_y": 0.2612471580505371, "range_z": 0.09578466415405273}, {"start_time": 49425268, "end_time": 49440968, "duration": 15.7, "gyro_count": 137, "mean_x": 0.6512643033570616, "mean_y": -1.3889836290456954, "mean_z": -0.5056290572142079, "std_x": 0.03380667578722291, "std_y": 0.022874719371318866, "std_z": 0.019000385222186143, "range_x": 0.27868151664733887, "range_y": 0.12192118167877197, "range_z": 0.1219213604927063}, {"start_time": 50594667, "end_time": 50608074, "duration": 13.407, "gyro_count": 115, "mean_x": 0.655064917129019, "mean_y": -1.3880551441856053, "mean_z": -0.5030756683453269, "std_x": 0.02871721405363572, "std_y": 0.026344278561507072, "std_z": 0.01828435069101284, "range_x": 0.1828969120979309, "range_y": 0.12192118167877197, "range_z": 0.09578463435173035}, {"start_time": 50642868, "end_time": 50658667, "duration": 15.799, "gyro_count": 138, "mean_x": 0.6602391591970471, "mean_y": -1.384850920110509, "mean_z": -0.5012707710266113, "std_x": 0.037373075078355834, "std_y": 0.02433931873521587, "std_z": 0.020806599859077072, "range_x": 0.27868151664733887, "range_y": 0.15676021575927734, "range_z": 0.10448691248893738}, {"start_time": 50669867, "end_time": 50695267, "duration": 25.4, "gyro_count": 235, "mean_x": 0.659137735468276, "mean_y": -1.384354835875491, "mean_z": -0.5033038030279443, "std_x": 0.033072595210409665, "std_y": 0.023333566740167195, "std_z": 0.01673934076834281, "range_x": 0.20030146837234497, "range_y": 0.1306236982345581, "range_z": 0.09578463435173035}], "failed_intervals": [], "all_passed": true}]}