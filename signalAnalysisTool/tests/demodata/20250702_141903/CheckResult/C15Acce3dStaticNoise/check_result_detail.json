{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查详情", "version": "1.0", "date": "2025-07-03 16:23:18", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "加速度器静态噪声过大", "static_intervals": [{"start_time": 45360537, "end_time": 45412068, "duration": 51.531, "start_time_adj": 45361537, "end_time_adj": 45411068}, {"start_time": 45467167, "end_time": 45473267, "duration": 6.1, "start_time_adj": 45468167, "end_time_adj": 45472267}, {"start_time": 46161071, "end_time": 46179969, "duration": 18.898, "start_time_adj": 46162071, "end_time_adj": 46178969}, {"start_time": 46282069, "end_time": 46292368, "duration": 10.299, "start_time_adj": 46283069, "end_time_adj": 46291368}, {"start_time": 47176368, "end_time": 47183870, "duration": 7.502, "start_time_adj": 47177368, "end_time_adj": 47182870}, {"start_time": 47188968, "end_time": 47217767, "duration": 28.799, "start_time_adj": 47189968, "end_time_adj": 47216767}, {"start_time": 47528667, "end_time": 47550468, "duration": 21.801, "start_time_adj": 47529667, "end_time_adj": 47549468}, {"start_time": 47648167, "end_time": 47675667, "duration": 27.5, "start_time_adj": 47649167, "end_time_adj": 47674667}, {"start_time": 47719768, "end_time": 47734668, "duration": 14.9, "start_time_adj": 47720768, "end_time_adj": 47733668}, {"start_time": 47785468, "end_time": 47891269, "duration": 105.801, "start_time_adj": 47786468, "end_time_adj": 47890269}, {"start_time": 47930067, "end_time": 48016369, "duration": 86.302, "start_time_adj": 47931067, "end_time_adj": 48015369}, {"start_time": 48386769, "end_time": 48397768, "duration": 10.999, "start_time_adj": 48387769, "end_time_adj": 48396768}, {"start_time": 48404367, "end_time": 48411169, "duration": 6.802, "start_time_adj": 48405367, "end_time_adj": 48410169}, {"start_time": 48429067, "end_time": 48442268, "duration": 13.201, "start_time_adj": 48430067, "end_time_adj": 48441268}, {"start_time": 48674268, "end_time": 48692099, "duration": 17.831, "start_time_adj": 48675268, "end_time_adj": 48691099}, {"start_time": 49031568, "end_time": 49072467, "duration": 40.899, "start_time_adj": 49032568, "end_time_adj": 49071467}, {"start_time": 49141368, "end_time": 49167468, "duration": 26.1, "start_time_adj": 49142368, "end_time_adj": 49166468}, {"start_time": 49425268, "end_time": 49440968, "duration": 15.7, "start_time_adj": 49426268, "end_time_adj": 49439968}, {"start_time": 50594667, "end_time": 50608074, "duration": 13.407, "start_time_adj": 50595667, "end_time_adj": 50607074}, {"start_time": 50642868, "end_time": 50658667, "duration": 15.799, "start_time_adj": 50643868, "end_time_adj": 50657667}, {"start_time": 50669867, "end_time": 50695267, "duration": 25.4, "start_time_adj": 50670867, "end_time_adj": 50694267}], "long_static_intervals": [{"start_time": 45360537, "end_time": 45412068, "duration": 51.531, "gyro_count": 496, "mean_x": -0.023800780914062934, "mean_y": -0.02395384494710954, "mean_z": 1.02246704889882, "std_x": 0.0008433776150994203, "std_y": 0.000930501665374236, "std_z": 0.0007167037347676644, "range_x": 0.0071020592004060745, "range_y": 0.00783676840364933, "range_z": 0.0071021318435668945}, {"start_time": 45467167, "end_time": 45473267, "duration": 6.1, "gyro_count": 42, "mean_x": -0.0222798753973274, "mean_y": -0.028177829414960883, "mean_z": 1.022478154727391, "std_x": 0.0013594878605766878, "std_y": 0.0005926324211456997, "std_z": 0.0007035727151490455, "range_x": 0.0067346468567848206, "range_y": 0.003061225637793541, "range_z": 0.002816319465637207}, {"start_time": 46161071, "end_time": 46179969, "duration": 18.898, "gyro_count": 169, "mean_x": -0.018875973255059422, "mean_y": -0.014580864282931096, "mean_z": 1.0232532468773203, "std_x": 0.0004368163767708883, "std_y": 0.0007547119967813802, "std_z": 0.0006562409805951993, "range_x": 0.003061225637793541, "range_y": 0.006489862687885761, "range_z": 0.003183603286743164}, {"start_time": 46282069, "end_time": 46292368, "duration": 10.299, "gyro_count": 83, "mean_x": -0.030358496088399946, "mean_y": -0.01891615046524858, "mean_z": 1.0228724249874253, "std_x": 0.0014469074429609252, "std_y": 0.0007377628594144986, "std_z": 0.0005715381751312406, "range_x": 0.011265290901064873, "range_y": 0.005265349522233009, "range_z": 0.004040718078613281}, {"start_time": 47176368, "end_time": 47183870, "duration": 7.502, "gyro_count": 55, "mean_x": -0.010904645123942331, "mean_y": -0.016201107661155137, "mean_z": 1.0237603144212202, "std_x": 0.0009045640746662228, "std_y": 0.000883581345765689, "std_z": 0.0009122863007010224, "range_x": 0.00391832459717989, "range_y": 0.004653032869100571, "range_z": 0.004775643348693848}, {"start_time": 47188968, "end_time": 47217767, "duration": 28.799, "gyro_count": 268, "mean_x": -0.012305209336718961, "mean_y": -0.013301244582424857, "mean_z": 1.023926160673597, "std_x": 0.0012418527407510656, "std_y": 0.0011655353846237353, "std_z": 0.000824246673107491, "range_x": 0.0124898012727499, "range_y": 0.00844908319413662, "range_z": 0.00636744499206543}, {"start_time": 47528667, "end_time": 47550468, "duration": 21.801, "gyro_count": 199, "mean_x": -0.01234580942396842, "mean_y": -0.022427843443712396, "mean_z": 1.0240666782436658, "std_x": 0.0005590112347496264, "std_y": 0.0006841462766684291, "std_z": 0.0006217755523068183, "range_x": 0.003183736465871334, "range_y": 0.005020326003432274, "range_z": 0.004530668258666992}, {"start_time": 47648167, "end_time": 47675667, "duration": 27.5, "gyro_count": 255, "mean_x": -0.016276097987942835, "mean_y": -0.015970698674665947, "mean_z": 1.024149356168859, "std_x": 0.000796394532140026, "std_y": 0.0005904721166737398, "std_z": 0.0004639117236250247, "range_x": 0.009306066669523716, "range_y": 0.003918323665857315, "range_z": 0.003306150436401367}, {"start_time": 47719768, "end_time": 47734668, "duration": 14.9, "gyro_count": 129, "mean_x": -0.009088755772961664, "mean_y": -0.02359184091405351, "mean_z": 1.0239781880563543, "std_x": 0.000654624484233214, "std_y": 0.0004979415138503864, "std_z": 0.0007528240514210064, "range_x": 0.0037959320470690727, "range_y": 0.0031837336719036102, "range_z": 0.007102012634277344}, {"start_time": 47785468, "end_time": 47891269, "duration": 105.801, "gyro_count": 1038, "mean_x": -0.014679973975527378, "mean_y": -0.02229230905306994, "mean_z": 1.0241215233628231, "std_x": 0.0006217082968884733, "std_y": 0.0006610518772395775, "std_z": 0.0005911541650411203, "range_x": 0.009306185878813267, "range_y": 0.009795989841222763, "range_z": 0.010530710220336914}, {"start_time": 47930067, "end_time": 48016369, "duration": 86.302, "gyro_count": 844, "mean_x": -0.013282807024142824, "mean_y": -0.03905065028853185, "mean_z": 1.023731950335028, "std_x": 0.0004898084894802914, "std_y": 0.000611255572780695, "std_z": 0.00048119983810585135, "range_x": 0.004408129490911961, "range_y": 0.007224570959806442, "range_z": 0.004652976989746094}, {"start_time": 48386769, "end_time": 48397768, "duration": 10.999, "gyro_count": 90, "mean_x": -0.029220402923723063, "mean_y": -0.01146939299069345, "mean_z": 1.0236639764573838, "std_x": 0.0017607737658837336, "std_y": 0.001523874944428733, "std_z": 0.0013061341940434255, "range_x": 0.010653089731931686, "range_y": 0.007959278766065836, "range_z": 0.006245017051696777}, {"start_time": 48404367, "end_time": 48411169, "duration": 6.802, "gyro_count": 49, "mean_x": -0.02539690880447018, "mean_y": -0.014703887818875362, "mean_z": 1.0238334402746083, "std_x": 0.001122244005164866, "std_y": 0.0005324643345090734, "std_z": 0.0017254037794720341, "range_x": 0.005632644519209862, "range_y": 0.0024489080533385277, "range_z": 0.006612300872802734}, {"start_time": 48429067, "end_time": 48442268, "duration": 13.201, "gyro_count": 113, "mean_x": -0.024588407816744484, "mean_y": -0.03323460861513045, "mean_z": 1.0235358729826665, "std_x": 0.0006630131703601903, "std_y": 0.0027126882494920143, "std_z": 0.0011774608143773056, "range_x": 0.0033061280846595764, "range_y": 0.016898050904273987, "range_z": 0.006612300872802734}, {"start_time": 48674268, "end_time": 48692099, "duration": 17.831, "gyro_count": 159, "mean_x": -0.014224105690307211, "mean_y": -0.016990371440894575, "mean_z": 1.0241594509508625, "std_x": 0.001927486308525393, "std_y": 0.0017702109529579855, "std_z": 0.0018580313632114232, "range_x": 0.012367409653961658, "range_y": 0.009428576566278934, "range_z": 0.009673237800598145}, {"start_time": 49031568, "end_time": 49072467, "duration": 40.899, "gyro_count": 388, "mean_x": -0.023426579167471102, "mean_y": -0.010873344160385967, "mean_z": 1.0237258743379534, "std_x": 0.0005079000049561365, "std_y": 0.0008955898542417498, "std_z": 0.000676577366136236, "range_x": 0.004040835425257683, "range_y": 0.009673479478806257, "range_z": 0.00942850112915039}, {"start_time": 49141368, "end_time": 49167468, "duration": 26.1, "gyro_count": 241, "mean_x": -0.022996523081328858, "mean_y": -0.03988178429885524, "mean_z": 1.0231989350061694, "std_x": 0.0010234379068589502, "std_y": 0.0011708242830003547, "std_z": 0.0010856491160695526, "range_x": 0.007959160953760147, "range_y": 0.008816380053758621, "range_z": 0.008816242218017578}, {"start_time": 49425268, "end_time": 49440968, "duration": 15.7, "gyro_count": 138, "mean_x": -0.008500449676606535, "mean_y": -0.010710735170953516, "mean_z": 1.023677044156669, "std_x": 0.0013163875969182464, "std_y": 0.0009236090884219913, "std_z": 0.0009241545427930113, "range_x": 0.010163284372538328, "range_y": 0.005510132759809494, "range_z": 0.006857037544250488}, {"start_time": 50594667, "end_time": 50608074, "duration": 13.407, "gyro_count": 115, "mean_x": -0.007460872614351304, "mean_y": -0.016065298239498035, "mean_z": 1.0233870837999426, "std_x": 0.0008689949012622365, "std_y": 0.0008174318424489551, "std_z": 0.0009747133970349911, "range_x": 0.004653032869100571, "range_y": 0.004285737872123718, "range_z": 0.004163146018981934}, {"start_time": 50642868, "end_time": 50658667, "duration": 15.799, "gyro_count": 138, "mean_x": -0.016881988808998594, "mean_y": -0.016471156931441765, "mean_z": 1.0234081917914792, "std_x": 0.001300839179150499, "std_y": 0.001354617141894713, "std_z": 0.0013172019744059603, "range_x": 0.010530579835176468, "range_y": 0.011755093932151794, "range_z": 0.008448958396911621}, {"start_time": 50669867, "end_time": 50695267, "duration": 25.4, "gyro_count": 235, "mean_x": -0.018589321940027652, "mean_y": -0.012330874190368551, "mean_z": 1.023306144552028, "std_x": 0.001020396100726942, "std_y": 0.0012354592799585827, "std_z": 0.0009695267982957, "range_x": 0.008326453156769276, "range_y": 0.00783676840364933, "range_z": 0.005265355110168457}], "failed_intervals": [], "all_passed": true}]}