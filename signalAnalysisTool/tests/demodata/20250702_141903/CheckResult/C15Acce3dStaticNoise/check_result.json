{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250702_141903/AutoSdkDemo/online/vlane/20250702_141903/data.txt", "version": "1.0", "date": "2025-07-03 16:23:18", "type": "CheckResult", "checkResult": [{"checkItem": "加速度器静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 21<br>时长>2秒区间数: 21<br>通过区间数: 21<br>失败区间数: 0<br><br>区间1:<br>  时长: 51.53秒<br>  加速度器数据个数: 496<br>  标准差 - X:0.0008, Y:0.0009, Z:0.0007<br>  极值差 - X:0.0071, Y:0.0078, Z:0.0071<br>区间2:<br>  时长: 6.10秒<br>  加速度器数据个数: 42<br>  标准差 - X:0.0014, Y:0.0006, Z:0.0007<br>  极值差 - X:0.0067, Y:0.0031, Z:0.0028<br>区间3:<br>  时长: 18.90秒<br>  加速度器数据个数: 169<br>  标准差 - X:0.0004, Y:0.0008, Z:0.0007<br>  极值差 - X:0.0031, Y:0.0065, Z:0.0032<br>区间4:<br>  时长: 10.30秒<br>  加速度器数据个数: 83<br>  标准差 - X:0.0014, Y:0.0007, Z:0.0006<br>  极值差 - X:0.0113, Y:0.0053, Z:0.0040<br>区间5:<br>  时长: 7.50秒<br>  加速度器数据个数: 55<br>  标准差 - X:0.0009, Y:0.0009, Z:0.0009<br>  极值差 - X:0.0039, Y:0.0047, Z:0.0048<br>区间6:<br>  时长: 28.80秒<br>  加速度器数据个数: 268<br>  标准差 - X:0.0012, Y:0.0012, Z:0.0008<br>  极值差 - X:0.0125, Y:0.0084, Z:0.0064<br>区间7:<br>  时长: 21.80秒<br>  加速度器数据个数: 199<br>  标准差 - X:0.0006, Y:0.0007, Z:0.0006<br>  极值差 - X:0.0032, Y:0.0050, Z:0.0045<br>区间8:<br>  时长: 27.50秒<br>  加速度器数据个数: 255<br>  标准差 - X:0.0008, Y:0.0006, Z:0.0005<br>  极值差 - X:0.0093, Y:0.0039, Z:0.0033<br>区间9:<br>  时长: 14.90秒<br>  加速度器数据个数: 129<br>  标准差 - X:0.0007, Y:0.0005, Z:0.0008<br>  极值差 - X:0.0038, Y:0.0032, Z:0.0071<br>区间10:<br>  时长: 105.80秒<br>  加速度器数据个数: 1038<br>  标准差 - X:0.0006, Y:0.0007, Z:0.0006<br>  极值差 - X:0.0093, Y:0.0098, Z:0.0105<br>区间11:<br>  时长: 86.30秒<br>  加速度器数据个数: 844<br>  标准差 - X:0.0005, Y:0.0006, Z:0.0005<br>  极值差 - X:0.0044, Y:0.0072, Z:0.0047<br>区间12:<br>  时长: 11.00秒<br>  加速度器数据个数: 90<br>  标准差 - X:0.0018, Y:0.0015, Z:0.0013<br>  极值差 - X:0.0107, Y:0.0080, Z:0.0062<br>区间13:<br>  时长: 6.80秒<br>  加速度器数据个数: 49<br>  标准差 - X:0.0011, Y:0.0005, Z:0.0017<br>  极值差 - X:0.0056, Y:0.0024, Z:0.0066<br>区间14:<br>  时长: 13.20秒<br>  加速度器数据个数: 113<br>  标准差 - X:0.0007, Y:0.0027, Z:0.0012<br>  极值差 - X:0.0033, Y:0.0169, Z:0.0066<br>区间15:<br>  时长: 17.83秒<br>  加速度器数据个数: 159<br>  标准差 - X:0.0019, Y:0.0018, Z:0.0019<br>  极值差 - X:0.0124, Y:0.0094, Z:0.0097<br>区间16:<br>  时长: 40.90秒<br>  加速度器数据个数: 388<br>  标准差 - X:0.0005, Y:0.0009, Z:0.0007<br>  极值差 - X:0.0040, Y:0.0097, Z:0.0094<br>区间17:<br>  时长: 26.10秒<br>  加速度器数据个数: 241<br>  标准差 - X:0.0010, Y:0.0012, Z:0.0011<br>  极值差 - X:0.0080, Y:0.0088, Z:0.0088<br>区间18:<br>  时长: 15.70秒<br>  加速度器数据个数: 138<br>  标准差 - X:0.0013, Y:0.0009, Z:0.0009<br>  极值差 - X:0.0102, Y:0.0055, Z:0.0069<br>区间19:<br>  时长: 13.41秒<br>  加速度器数据个数: 115<br>  标准差 - X:0.0009, Y:0.0008, Z:0.0010<br>  极值差 - X:0.0047, Y:0.0043, Z:0.0042<br>区间20:<br>  时长: 15.80秒<br>  加速度器数据个数: 138<br>  标准差 - X:0.0013, Y:0.0014, Z:0.0013<br>  极值差 - X:0.0105, Y:0.0118, Z:0.0084<br>区间21:<br>  时长: 25.40秒<br>  加速度器数据个数: 235<br>  标准差 - X:0.0010, Y:0.0012, Z:0.0010<br>  极值差 - X:0.0083, Y:0.0078, Z:0.0053", "checkRules": "所有静态区间(时长>2秒)的标准差<0.1<br>且极值差<0.2dps", "description": "不通过需手动排查"}]}