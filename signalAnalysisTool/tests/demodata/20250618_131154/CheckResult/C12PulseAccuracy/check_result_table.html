<html>
<head><meta charset="utf-8" /></head>
<body>
    <div>                        <script type="text/javascript">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>
        <script charset="utf-8" src="https://cdn.plot.ly/plotly-3.0.1.min.js"></script>                <div id="451c643b-62d0-42a0-ba55-b32193d4d708" class="plotly-graph-div" style="height:1000px; width:100%;"></div>            <script type="text/javascript">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById("451c643b-62d0-42a0-ba55-b32193d4d708")) {                    Plotly.newPlot(                        "451c643b-62d0-42a0-ba55-b32193d4d708",                        [{"cells":{"align":["left","center","left","center","center"],"fill":{"color":[["lavender","lavender","lavender","lightcoral","lightcoral"],["lavender","lavender","lavender","lightcoral","lightcoral"],["lavender","lavender","lavender","lightcoral","lightcoral"],["lavender","lavender","lavender","lightcoral","lightcoral"],["lavender","lavender","lavender","lightcoral","lightcoral"]]},"format":[null,null,"html",null,null],"values":[["原始轮速误差检查","轮速尺度因子检查","标定轮速误差检查（全场景）","标定轮速误差检查(转弯或掉头)","误差超限积分检查"],["\u003cbr\u003e轮速误差均值:0.64\u003cbr\u003e轮速误差中位数:0.67","\u003cbr\u003e轮速尺度因子均值:1.02                    \u003cbr\u003e轮速尺度因子中位数:1.02                    \u003cbr\u003e16%分位轮速尺度因子:1.00                    \u003cbr\u003e84%分位轮速尺度因子:1.02                    \u003cbr\u003e轮速尺度因子均值检查结果:True                    \u003cbr\u003e轮速尺度因子中位数检查结果:True                    \u003cbr\u003e16%分位轮速尺度因子检查结果:True                    \u003cbr\u003e84%分位轮速尺度因子检查结果:True","\u003cbr\u003e轮速误差均值:0.02\u003cbr\u003e轮速误差中位数:0.00","\u003cbr\u003e轮速误差均值:-1.73\u003cbr\u003e轮速误差中位数:-1.39","\u003cbr\u003e超限区间误差积分最大累计值:15.57"],["误差均值或中位数在正负1km\u002fh之内","误均值或中位数在0.95~1.05范围\u003cbr\u003e尺度因子的16%或84%分位数在0.95~1.05范围之内","误差均值或中位数在正负1km\u002fh之内","误差均值或中位数在正负1km\u002fh之内","误差超限区间内轮速误差积分小于3.5米（全场景）"],["Pass","Pass","Pass","Fail","Fail"],["","","","",""]]},"header":{"align":"center","fill":{"color":"paleturquoise"},"values":["检查项","统计数值","检查规则","检查结果","描述"]},"type":"table","domain":{"x":[0.0,1.0],"y":[0.525,1.0]}},{"cells":{"align":"center","fill":{"color":[["lavender","lavender","lavender","lightcoral","lightcoral"],["lavender","lavender","lavender","lightcoral","lightcoral"],["lavender","lavender","lavender","lightcoral","lightcoral"],["lavender","lavender","lavender","lightcoral","lightcoral"],["lavender","lavender","lavender","lightcoral","lightcoral"]]},"values":[["原始轮速误差检查","轮速尺度因子检查","标定轮速误差检查（全场景）","标定轮速误差检查(转弯或掉头)","误差超限积分检查"],["[\n  {\n    \"pulse_sourceTickTime\": 1315339,\n    \"pulse_speed\": 9.899999618530273,\n    \"gt_speed\": 6.055481384767231,\n    \"speed_diff\": -3.8445182337630426,\n    \"angular_velocity\": 3.113189999999997\n  },\n  {\n    \"pulse_sourceTickTime\": 1315439,\n    \"pulse_speed\": 10.34999942779541,\n    \"gt_speed\": 6.309682192047471,\n    \"speed_diff\": -4.040317235747939,\n    \"angular_velocity\": 3.155610000000002\n  },\n  {\n    \"pulse_sourceTickTime\": 1315539,\n    \"pulse_speed\": 10.856249809265137,\n    \"gt_speed\": 6.6103479209799625,\n    \"speed_diff\": -4.245901888285174,\n    \"angular_velocity\": 3.359049999999999\n  },\n  {\n    \"pulse_sourceTickTime\": 1315640,\n    \"pulse_speed\": 11.8125,\n    \"gt_speed\": 6.982154283072356,\n    \"speed_diff\": -4.830345716927644,\n    \"angular_velocity\": 3.5149099999999978\n  },\n  {\n    \"pulse_sourceTickTime\": 1315739,\n    \"pulse_speed\": 12.09375,\n    \"gt_speed\": 7.406195397632179,\n    \"speed_diff\": -4.687554602367821,\n    \"angular_velocity\": 3.7851300000000077\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 1268339,\n    \"pulse_speed\": 0.6187499761581421,\n    \"gt_speed\": 0.009903889342829196,\n    \"scale\": 0.016006286423351602\n  },\n  {\n    \"pulse_sourceTickTime\": 1268439,\n    \"pulse_speed\": 1.068750023841858,\n    \"gt_speed\": 0.011125237213228528,\n    \"scale\": 0.01040957844682558\n  },\n  {\n    \"pulse_sourceTickTime\": 1268539,\n    \"pulse_speed\": 1.2374999523162842,\n    \"gt_speed\": 0.010644620946148054,\n    \"scale\": 0.00860171422732101\n  },\n  {\n    \"pulse_sourceTickTime\": 1268639,\n    \"pulse_speed\": 1.462499976158142,\n    \"gt_speed\": 0.022314848279894703,\n    \"scale\": 0.015258016166614808\n  },\n  {\n    \"pulse_sourceTickTime\": 1268739,\n    \"pulse_speed\": 1.6312499046325684,\n    \"gt_speed\": 0.09917153574713536,\n    \"scale\": 0.06079481474021805\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 1493639,\n    \"pulse_speed\": 38.754832516757965,\n    \"gt_speed\": 35.02278932083555,\n    \"speed_diff\": -3.732043195922415,\n    \"angular_velocity\": -0.42495999999999867\n  },\n  {\n    \"pulse_sourceTickTime\": 1493739,\n    \"pulse_speed\": 39.26625839402389,\n    \"gt_speed\": 35.409411578881624,\n    \"speed_diff\": -3.8568468151422692,\n    \"angular_velocity\": -0.42030000000011114\n  },\n  {\n    \"pulse_sourceTickTime\": 1493839,\n    \"pulse_speed\": 39.49356178125,\n    \"gt_speed\": 35.78251301527908,\n    \"speed_diff\": -3.711048765970922,\n    \"angular_velocity\": -0.39512999999999465\n  },\n  {\n    \"pulse_sourceTickTime\": 1493939,\n    \"pulse_speed\": 39.83451108152389,\n    \"gt_speed\": 36.155414127528964,\n    \"speed_diff\": -3.6790969539949288,\n    \"angular_velocity\": -0.5032299999999168\n  },\n  {\n    \"pulse_sourceTickTime\": 1494139,\n    \"pulse_speed\": 40.516417389492034,\n    \"gt_speed\": 36.89738384752559,\n    \"speed_diff\": -3.619033541966445,\n    \"angular_velocity\": -0.9065999999999974\n  }\n]","[\n  {\n    \"pulse_sourceTickTime\": 1826039,\n    \"pulse_speed\": 20.846367635822293,\n    \"gt_speed\": 16.982316933749722,\n    \"speed_diff\": -3.8640507020725714,\n    \"angular_velocity\": 22.480130000000003\n  },\n  {\n    \"pulse_sourceTickTime\": 1826139,\n    \"pulse_speed\": 21.130378104572294,\n    \"gt_speed\": 17.205834376958098,\n    \"speed_diff\": -3.9245437276141963,\n    \"angular_velocity\": 22.68706999999999\n  },\n  {\n    \"pulse_sourceTickTime\": 1826239,\n    \"pulse_speed\": 21.81200245914459,\n    \"gt_speed\": 17.467918088433066,\n    \"speed_diff\": -4.344084370711524,\n    \"angular_velocity\": 22.121390000000005\n  },\n  {\n    \"pulse_sourceTickTime\": 1826339,\n    \"pulse_speed\": 22.266419979572294,\n    \"gt_speed\": 17.724711608511694,\n    \"speed_diff\": -4.541708371060601,\n    \"angular_velocity\": 21.97417999999999\n  },\n  {\n    \"pulse_sourceTickTime\": 1826439,\n    \"pulse_speed\": 22.60723215685844,\n    \"gt_speed\": 18.044685560364357,\n    \"speed_diff\": -4.562546596494084,\n    \"angular_velocity\": 22.97417000000003\n  }\n]","[\n  {\n    \"start_time\": 6171939,\n    \"end_time\": 6173639\n  }\n]"]]},"header":{"align":"center","fill":{"color":"paleturquoise"},"values":["检查项","检查详情"]},"type":"table","domain":{"x":[0.0,1.0],"y":[0.0,0.475]}}],                        {"template":{"data":{"histogram2dcontour":[{"type":"histogram2dcontour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"choropleth":[{"type":"choropleth","colorbar":{"outlinewidth":0,"ticks":""}}],"histogram2d":[{"type":"histogram2d","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"heatmap":[{"type":"heatmap","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"contourcarpet":[{"type":"contourcarpet","colorbar":{"outlinewidth":0,"ticks":""}}],"contour":[{"type":"contour","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"surface":[{"type":"surface","colorbar":{"outlinewidth":0,"ticks":""},"colorscale":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]]}],"mesh3d":[{"type":"mesh3d","colorbar":{"outlinewidth":0,"ticks":""}}],"scatter":[{"fillpattern":{"fillmode":"overlay","size":10,"solidity":0.2},"type":"scatter"}],"parcoords":[{"type":"parcoords","line":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolargl":[{"type":"scatterpolargl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"bar":[{"error_x":{"color":"#2a3f5f"},"error_y":{"color":"#2a3f5f"},"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"bar"}],"scattergeo":[{"type":"scattergeo","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterpolar":[{"type":"scatterpolar","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"histogram":[{"marker":{"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"histogram"}],"scattergl":[{"type":"scattergl","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatter3d":[{"type":"scatter3d","line":{"colorbar":{"outlinewidth":0,"ticks":""}},"marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermap":[{"type":"scattermap","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattermapbox":[{"type":"scattermapbox","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scatterternary":[{"type":"scatterternary","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"scattercarpet":[{"type":"scattercarpet","marker":{"colorbar":{"outlinewidth":0,"ticks":""}}}],"carpet":[{"aaxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"baxis":{"endlinecolor":"#2a3f5f","gridcolor":"white","linecolor":"white","minorgridcolor":"white","startlinecolor":"#2a3f5f"},"type":"carpet"}],"table":[{"cells":{"fill":{"color":"#EBF0F8"},"line":{"color":"white"}},"header":{"fill":{"color":"#C8D4E3"},"line":{"color":"white"}},"type":"table"}],"barpolar":[{"marker":{"line":{"color":"#E5ECF6","width":0.5},"pattern":{"fillmode":"overlay","size":10,"solidity":0.2}},"type":"barpolar"}],"pie":[{"automargin":true,"type":"pie"}]},"layout":{"autotypenumbers":"strict","colorway":["#636efa","#EF553B","#00cc96","#ab63fa","#FFA15A","#19d3f3","#FF6692","#B6E880","#FF97FF","#FECB52"],"font":{"color":"#2a3f5f"},"hovermode":"closest","hoverlabel":{"align":"left"},"paper_bgcolor":"white","plot_bgcolor":"#E5ECF6","polar":{"bgcolor":"#E5ECF6","angularaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"radialaxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"ternary":{"bgcolor":"#E5ECF6","aaxis":{"gridcolor":"white","linecolor":"white","ticks":""},"baxis":{"gridcolor":"white","linecolor":"white","ticks":""},"caxis":{"gridcolor":"white","linecolor":"white","ticks":""}},"coloraxis":{"colorbar":{"outlinewidth":0,"ticks":""}},"colorscale":{"sequential":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"sequentialminus":[[0.0,"#0d0887"],[0.1111111111111111,"#46039f"],[0.2222222222222222,"#7201a8"],[0.3333333333333333,"#9c179e"],[0.4444444444444444,"#bd3786"],[0.5555555555555556,"#d8576b"],[0.6666666666666666,"#ed7953"],[0.7777777777777778,"#fb9f3a"],[0.8888888888888888,"#fdca26"],[1.0,"#f0f921"]],"diverging":[[0,"#8e0152"],[0.1,"#c51b7d"],[0.2,"#de77ae"],[0.3,"#f1b6da"],[0.4,"#fde0ef"],[0.5,"#f7f7f7"],[0.6,"#e6f5d0"],[0.7,"#b8e186"],[0.8,"#7fbc41"],[0.9,"#4d9221"],[1,"#276419"]]},"xaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"yaxis":{"gridcolor":"white","linecolor":"white","ticks":"","title":{"standoff":15},"zerolinecolor":"white","automargin":true,"zerolinewidth":2},"scene":{"xaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"yaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2},"zaxis":{"backgroundcolor":"#E5ECF6","gridcolor":"white","linecolor":"white","showbackground":true,"ticks":"","zerolinecolor":"white","gridwidth":2}},"shapedefaults":{"line":{"color":"#2a3f5f"}},"annotationdefaults":{"arrowcolor":"#2a3f5f","arrowhead":0,"arrowwidth":1},"geo":{"bgcolor":"white","landcolor":"#E5ECF6","subunitcolor":"white","showland":true,"showlakes":true,"lakecolor":"white"},"title":{"x":0.05},"mapbox":{"style":"light"}}},"annotations":[{"font":{"size":16},"showarrow":false,"text":"轮速精度检查结果","x":0.5,"xanchor":"center","xref":"paper","y":1.0,"yanchor":"bottom","yref":"paper"},{"font":{"size":16},"showarrow":false,"text":"轮速精度检查详情","x":0.5,"xanchor":"center","xref":"paper","y":0.475,"yanchor":"bottom","yref":"paper"}],"title":{"text":"\u003cb\u003e轮速精度检查结果汇总\u003c\u002fb\u003e","x":0.5},"margin":{"t":100,"b":50,"l":50,"r":50},"height":1000,"showlegend":false},                        {"responsive": true}                    )                };            </script>        </div>
</body>
</html>