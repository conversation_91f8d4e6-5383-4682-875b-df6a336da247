{"checker": "C04TimestampRepeatFrame", "title": "时间戳重帧检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_131154/AutoSdkDemo/online/vlane/20250618_131154/data.txt", "version": "1.0", "date": "2025-07-04 11:01:22", "type": "CheckResult", "checkResult": [{"checkItem": "acce3d时间重帧率", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "时间重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro时间重帧率", "checkResult": "Pass", "checkResultStatistic": 1.6604400166044e-05, "checkRules": "时间重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "pulse时间重帧率", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "时间重帧率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gnss时间重帧率", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "时间重帧率<0.0005", "description": "不通过需手动排查"}]}