{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_131154/AutoSdkDemo/online/vlane/20250618_131154/data.txt", "version": "1.0", "date": "2025-07-04 11:25:03", "type": "CheckResult", "checkResult": [{"checkItem": "加速度器静态噪声过大", "checkResult": "Pass", "checkResultStatistic": "静态区间总数: 8<br>时长>2秒区间数: 8<br>通过区间数: 8<br>失败区间数: 0<br><br>区间1:<br>  时长: 19.10秒<br>  加速度器数据个数: 172<br>  标准差 - X:0.0008, Y:0.0009, Z:0.0010<br>  极值差 - X:0.0039, Y:0.0048, Z:0.0047<br>区间2:<br>  时长: 17.90秒<br>  加速度器数据个数: 159<br>  标准差 - X:0.0008, Y:0.0010, Z:0.0011<br>  极值差 - X:0.0038, Y:0.0063, Z:0.0062<br>区间3:<br>  时长: 5.30秒<br>  加速度器数据个数: 33<br>  标准差 - X:0.0006, Y:0.0011, Z:0.0009<br>  极值差 - X:0.0029, Y:0.0055, Z:0.0040<br>区间4:<br>  时长: 31.20秒<br>  加速度器数据个数: 292<br>  标准差 - X:0.0007, Y:0.0008, Z:0.0008<br>  极值差 - X:0.0049, Y:0.0061, Z:0.0059<br>区间5:<br>  时长: 31.40秒<br>  加速度器数据个数: 294<br>  标准差 - X:0.0007, Y:0.0008, Z:0.0007<br>  极值差 - X:0.0035, Y:0.0049, Z:0.0041<br>区间6:<br>  时长: 12.00秒<br>  加速度器数据个数: 100<br>  标准差 - X:0.0010, Y:0.0015, Z:0.0010<br>  极值差 - X:0.0063, Y:0.0105, Z:0.0057<br>区间7:<br>  时长: 378.00秒<br>  加速度器数据个数: 3759<br>  标准差 - X:0.0017, Y:0.0009, Z:0.0012<br>  极值差 - X:0.0864, Y:0.0199, Z:0.0535<br>区间8:<br>  时长: 680.60秒<br>  加速度器数据个数: 6736<br>  标准差 - X:0.0013, Y:0.0008, Z:0.0009<br>  极值差 - X:0.0983, Y:0.0233, Z:0.0381", "checkRules": "所有静态区间(时长>2秒)的标准差<0.1<br>且极值差<0.2dps", "description": "不通过需手动排查"}]}