{"checker": "C02FreqInst", "title": "不稳定性检查检查", "datatxt": "/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_131154/AutoSdkDemo/online/vlane/20250618_131154/data.txt", "version": "1.0", "date": "2025-07-04 11:01:11", "type": "CheckResult", "checkResult": [{"checkItem": "acce3d频率不稳定性", "checkResult": "Pass", "checkResultStatistic": 9.962805526036131e-05, "checkRules": "频率不稳定率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gyro频率不稳定性", "checkResult": "Pass", "checkResultStatistic": 9.962805526036131e-05, "checkRules": "频率不稳定率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "pulse频率不稳定性", "checkResult": "Pass", "checkResultStatistic": 0.0, "checkRules": "频率不稳定率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "image频率不稳定性", "checkResult": "Fail", "checkResultStatistic": 0.22754353803849678, "checkRules": "频率不稳定率<0.0005", "description": "不通过需手动排查"}, {"checkItem": "gnss频率不稳定性", "checkResult": "Fail", "checkResultStatistic": 0.01298251687727194, "checkRules": "频率不稳定率<0.0005", "description": "不通过需手动排查"}]}