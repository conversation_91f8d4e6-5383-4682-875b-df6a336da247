{"checker": "C14GryoStaticNoise", "title": "陀螺仪静态噪声检查详情", "version": "1.0", "date": "2025-07-04 11:25:00", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "陀螺静态噪声过大", "static_intervals": [{"start_time": 1249240, "end_time": 1268339, "duration": 19.099, "start_time_adj": 1250240, "end_time_adj": 1267339}, {"start_time": 1469339, "end_time": 1487239, "duration": 17.9, "start_time_adj": 1470339, "end_time_adj": 1486239}, {"start_time": 1569339, "end_time": 1574639, "duration": 5.3, "start_time_adj": 1570339, "end_time_adj": 1573639}, {"start_time": 1680139, "end_time": 1711339, "duration": 31.2, "start_time_adj": 1681139, "end_time_adj": 1710339}, {"start_time": 1777339, "end_time": 1808739, "duration": 31.4, "start_time_adj": 1778339, "end_time_adj": 1807739}, {"start_time": 1975539, "end_time": 1987539, "duration": 12.0, "start_time_adj": 1976539, "end_time_adj": 1986539}, {"start_time": 6174139, "end_time": 6552139, "duration": 378.0, "start_time_adj": 6175139, "end_time_adj": 6551139}, {"start_time": 6597439, "end_time": 7278039, "duration": 680.6, "start_time_adj": 6598439, "end_time_adj": 7277039}], "long_static_intervals": [{"start_time": 1249240, "end_time": 1268339, "duration": 19.099, "gyro_count": 172, "mean_x": 0.054450231246910126, "mean_y": 0.014503508297250021, "mean_z": 0.009581447191817988, "std_x": 0.020567431502533987, "std_y": 0.019153657828723872, "std_z": 0.015429680658808658, "range_x": 0.12490153312683105, "range_y": 0.11286139860749245, "range_z": 0.08767843618988991}, {"start_time": 1469339, "end_time": 1487239, "duration": 17.9, "gyro_count": 159, "mean_x": 0.05151415946556595, "mean_y": 0.011899186550118264, "mean_z": 0.00704984284597654, "std_x": 0.02105720263541023, "std_y": 0.0214322610440666, "std_z": 0.01711033303310601, "range_x": 0.10085106652695686, "range_y": 0.1627206802368164, "range_z": 0.0756383016705513}, {"start_time": 1569339, "end_time": 1574639, "duration": 5.3, "gyro_count": 33, "mean_x": 0.04918286384958209, "mean_y": 0.011836940747205957, "mean_z": 0.006434592488927372, "std_x": 0.018370885032212285, "std_y": 0.013671497896662884, "std_z": 0.015024131821492905, "range_x": 0.10025501996278763, "range_y": 0.06243586726486683, "range_z": 0.08767843618988991}, {"start_time": 1680139, "end_time": 1711339, "duration": 31.2, "gyro_count": 292, "mean_x": 0.05501927365867855, "mean_y": 0.01618225304792597, "mean_z": 0.010156264452085104, "std_x": 0.017958708910371154, "std_y": 0.013521894272274796, "std_z": 0.016806077636358826, "range_x": 0.08824468404054642, "range_y": 0.07504225336015224, "range_z": 0.08767843618988991}, {"start_time": 1777339, "end_time": 1808739, "duration": 31.4, "gyro_count": 294, "mean_x": 0.05366638176092485, "mean_y": 0.014421789960770988, "mean_z": 0.010962490906280016, "std_x": 0.018141503352304217, "std_y": 0.013451489856666648, "std_z": 0.015543772903985236, "range_x": 0.10028481483459473, "range_y": 0.07504225336015224, "range_z": 0.08767843618988991}, {"start_time": 1975539, "end_time": 1987539, "duration": 12.0, "gyro_count": 100, "mean_x": 0.050806404957547784, "mean_y": 0.012115836194716394, "mean_z": 0.010875166091136635, "std_x": 0.02305471041711564, "std_y": 0.02268435213774499, "std_z": 0.01657814056646466, "range_x": 0.12549758050590754, "range_y": 0.13750791549682617, "range_z": 0.08767843618988991}, {"start_time": 6174139, "end_time": 6552139, "duration": 378.0, "gyro_count": 3759, "mean_x": 0.04924738400234947, "mean_y": 0.015470125480428889, "mean_z": 0.015052171421553677, "std_x": 0.0201898314819591, "std_y": 0.030522747178651485, "std_z": 0.017495116082760543, "range_x": 0.31170250475406647, "range_y": 1.0994971096515656, "range_z": 0.20110607892274857}, {"start_time": 6597439, "end_time": 7278039, "duration": 680.6, "gyro_count": 6736, "mean_x": 0.043644613353155316, "mean_y": 0.016433419953329895, "mean_z": 0.014305323342026173, "std_x": 0.018667325255766226, "std_y": 0.01911182550259648, "std_z": 0.016667928669220237, "range_x": 0.2492368295788765, "range_y": 0.849694013595581, "range_z": 0.16328692436218262}], "failed_intervals": [], "all_passed": true}]}