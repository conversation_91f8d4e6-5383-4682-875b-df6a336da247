2025-07-04 13:52:44,924 - C09GnssPulseSpeed - INFO - init
2025-07-04 13:52:44,924 - C09GnssPulseSpeed - INFO - check
2025-07-04 13:52:46,922 - C09GnssPulseSpeed - INFO - cross_correlation_function, gnss length : 511 , pulse length : 5104
2025-07-04 13:52:46,926 - C09GnssPulseSpeed - INFO - 最大延迟 max_lag = 0.0 s
2025-07-04 13:52:46,926 - C09GnssPulseSpeed - INFO - check_result: {'checker': 'gnssPulseSpeedChecker', 'title': 'GNSS速度和轮速不同步', 'datatxt': '/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_131154/AutoSdkDemo/online/vlane/20250618_131154/data.txt', 'version': '1.0', 'date': '2025-07-04 13:52:44', 'type': 'CheckResult', 'checkResult': {'checkItem': 'GNSS速度和轮速不同步相关性函数', 'checkList': [{'checkDataTime': '1989558 - 2499598', 'checkResultStatistic': '最大延迟0.0s', 'checkResult': 'Pass'}], 'checkRules': '最大延迟<=0.2s', 'description': '不通过需手动排查'}}
