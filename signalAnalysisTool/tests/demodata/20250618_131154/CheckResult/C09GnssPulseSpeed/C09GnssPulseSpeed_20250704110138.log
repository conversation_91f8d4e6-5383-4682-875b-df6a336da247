2025-07-04 11:01:38,662 - C09GnssPulseSpeed - INFO - init
2025-07-04 11:01:38,662 - C09GnssPulseSpeed - INFO - check
2025-07-04 11:01:40,671 - C09GnssPulseSpeed - INFO - cross_correlation_function, gnss length : 693 , pulse length : 6920
2025-07-04 11:01:40,674 - C09GnssPulseSpeed - INFO - 最大延迟 max_lag = 0.0 s
2025-07-04 11:01:40,675 - C09GnssPulseSpeed - INFO - check_result: {'checker': 'gnssPulseSpeedChecker', 'title': 'GNSS速度和轮速不同步', 'datatxt': '/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_131154/AutoSdkDemo/online/vlane/20250618_131154/data.txt', 'version': '1.0', 'date': '2025-07-04 11:01:38', 'type': 'CheckResult', 'checkResult': {'checkItem': 'GNSS速度和轮速不同步相关性函数', 'checkList': [{'checkDataTime': '1992579 - 2684568', 'checkResultStatistic': '最大延迟0.0s', 'checkResult': 'Pass'}], 'checkRules': '最大延迟<=0.2s', 'description': '不通过需手动排查'}}
2025-07-04 11:01:40,766 - C09GnssPulseSpeed - INFO - cross_correlation_function, gnss length : 2967 , pulse length : 29661
2025-07-04 11:01:40,768 - C09GnssPulseSpeed - INFO - 最大延迟 max_lag = 0.0 s
2025-07-04 11:01:40,768 - C09GnssPulseSpeed - INFO - check_result: {'checker': 'gnssPulseSpeedChecker', 'title': 'GNSS速度和轮速不同步', 'datatxt': '/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250618_131154/AutoSdkDemo/online/vlane/20250618_131154/data.txt', 'version': '1.0', 'date': '2025-07-04 11:01:38', 'type': 'CheckResult', 'checkResult': {'checkItem': 'GNSS速度和轮速不同步相关性函数', 'checkList': [{'checkDataTime': '1992579 - 2684568', 'checkResultStatistic': '最大延迟0.0s', 'checkResult': 'Pass'}, {'checkDataTime': '3191590 - 6157718', 'checkResultStatistic': '最大延迟0.0s', 'checkResult': 'Pass'}], 'checkRules': '最大延迟<=0.2s', 'description': '不通过需手动排查'}}
