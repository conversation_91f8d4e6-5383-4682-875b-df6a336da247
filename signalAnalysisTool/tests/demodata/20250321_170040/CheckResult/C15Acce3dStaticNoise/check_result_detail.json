{"checker": "C15Acce3dStaticNoise", "title": "加速度器静态噪声检查详情", "version": "1.0", "date": "2025-07-10 18:42:24", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "加速度器静态噪声过大", "static_intervals": [{"start_time": 8247477, "end_time": 8260777, "duration": 13.3, "start_time_adj": 8248477, "end_time_adj": 8259777}, {"start_time": 8279877, "end_time": 8344877, "duration": 65.0, "start_time_adj": 8280877, "end_time_adj": 8343877}, {"start_time": 8355677, "end_time": 8379277, "duration": 23.6, "start_time_adj": 8356677, "end_time_adj": 8378277}, {"start_time": 8477877, "end_time": 8488077, "duration": 10.2, "start_time_adj": 8478877, "end_time_adj": 8487077}, {"start_time": 8497877, "end_time": 8513977, "duration": 16.1, "start_time_adj": 8498877, "end_time_adj": 8512977}, {"start_time": 8634177, "end_time": 8643277, "duration": 9.1, "start_time_adj": 8635177, "end_time_adj": 8642277}, {"start_time": 8736577, "end_time": 8753177, "duration": 16.6, "start_time_adj": 8737577, "end_time_adj": 8752177}, {"start_time": 9102077, "end_time": 9111877, "duration": 9.8, "start_time_adj": 9103077, "end_time_adj": 9110877}, {"start_time": 9189077, "end_time": 9220377, "duration": 31.3, "start_time_adj": 9190077, "end_time_adj": 9219377}, {"start_time": 9384277, "end_time": 9475077, "duration": 90.8, "start_time_adj": 9385277, "end_time_adj": 9474077}, {"start_time": 9674277, "end_time": 9689377, "duration": 15.1, "start_time_adj": 9675277, "end_time_adj": 9688377}, {"start_time": 9857677, "end_time": 9866577, "duration": 8.9, "start_time_adj": 9858677, "end_time_adj": 9865577}, {"start_time": 9898777, "end_time": 9903077, "duration": 4.3, "start_time_adj": 9899777, "end_time_adj": 9902077}, {"start_time": 9906277, "end_time": 9935977, "duration": 29.7, "start_time_adj": 9907277, "end_time_adj": 9934977}, {"start_time": 10013177, "end_time": 10075180, "duration": 62.003, "start_time_adj": 10014177, "end_time_adj": 10074180}, {"start_time": 10147377, "end_time": 10168178, "duration": 20.801, "start_time_adj": 10148377, "end_time_adj": 10167178}, {"start_time": 10274277, "end_time": 10345277, "duration": 71.0, "start_time_adj": 10275277, "end_time_adj": 10344277}, {"start_time": 10458577, "end_time": 10466277, "duration": 7.7, "start_time_adj": 10459577, "end_time_adj": 10465277}, {"start_time": 10561277, "end_time": 10621977, "duration": 60.7, "start_time_adj": 10562277, "end_time_adj": 10620977}, {"start_time": 10632877, "end_time": 10650477, "duration": 17.6, "start_time_adj": 10633877, "end_time_adj": 10649477}, {"start_time": 10698677, "end_time": 10735380, "duration": 36.703, "start_time_adj": 10699677, "end_time_adj": 10734380}, {"start_time": 10820877, "end_time": 10868077, "duration": 47.2, "start_time_adj": 10821877, "end_time_adj": 10867077}, {"start_time": 10948577, "end_time": 11031777, "duration": 83.2, "start_time_adj": 10949577, "end_time_adj": 11030777}, {"start_time": 11137177, "end_time": 11183077, "duration": 45.9, "start_time_adj": 11138177, "end_time_adj": 11182077}, {"start_time": 11350678, "end_time": 11377277, "duration": 26.599, "start_time_adj": 11351678, "end_time_adj": 11376277}, {"start_time": 11576177, "end_time": 11589677, "duration": 13.5, "start_time_adj": 11577177, "end_time_adj": 11588677}, {"start_time": 11679077, "end_time": 11707877, "duration": 28.8, "start_time_adj": 11680077, "end_time_adj": 11706877}, {"start_time": 11882777, "end_time": 11927377, "duration": 44.6, "start_time_adj": 11883777, "end_time_adj": 11926377}, {"start_time": 11936777, "end_time": 11954677, "duration": 17.9, "start_time_adj": 11937777, "end_time_adj": 11953677}, {"start_time": 12026977, "end_time": 12123578, "duration": 96.601, "start_time_adj": 12027977, "end_time_adj": 12122578}, {"start_time": 12307677, "end_time": 12355477, "duration": 47.8, "start_time_adj": 12308677, "end_time_adj": 12354477}, {"start_time": 12512877, "end_time": 12545678, "duration": 32.801, "start_time_adj": 12513877, "end_time_adj": 12544678}, {"start_time": 12570977, "end_time": 12622177, "duration": 51.2, "start_time_adj": 12571977, "end_time_adj": 12621177}, {"start_time": 12731277, "end_time": 12776877, "duration": 45.6, "start_time_adj": 12732277, "end_time_adj": 12775877}, {"start_time": 13447477, "end_time": 13459377, "duration": 11.9, "start_time_adj": 13448477, "end_time_adj": 13458377}], "long_static_intervals": [{"start_time": 8247477, "end_time": 8260777, "duration": 13.3, "gyro_count": 113, "mean_x": 0.06992895759444322, "mean_y": -9.29063939626238, "mean_z": 3.847008169224832, "std_x": 0.0064744194304297824, "std_y": 0.005294348772485087, "std_z": 0.008481932019134577, "range_x": 0.03528199717402458, "range_y": 0.031095504760742188, "range_z": 0.07056403160095215}, {"start_time": 8279877, "end_time": 8344877, "duration": 65.0, "gyro_count": 630, "mean_x": 0.1747621813936839, "mean_y": -9.265237638685438, "mean_z": 3.901720362617856, "std_x": 0.004377556750792193, "std_y": 0.004014944403966215, "std_z": 0.004888919338047942, "range_x": 0.03528200089931488, "range_y": 0.03827190399169922, "range_z": 0.05561399459838867}, {"start_time": 8355677, "end_time": 8379277, "duration": 23.6, "gyro_count": 216, "mean_x": 0.07888063136488199, "mean_y": -9.273645789534957, "mean_z": 3.882257605040515, "std_x": 0.005300592344631444, "std_y": 0.00360256119925425, "std_z": 0.005239333279544799, "range_x": 0.043056003749370575, "range_y": 0.03289031982421875, "range_z": 0.06458401679992676}, {"start_time": 8477877, "end_time": 8488077, "duration": 10.2, "gyro_count": 82, "mean_x": 0.05889570867506469, "mean_y": -9.316577643882937, "mean_z": 3.784953582577589, "std_x": 0.005664750059857294, "std_y": 0.009393871272761202, "std_z": 0.01177712634784936, "range_x": 0.025714002549648285, "range_y": 0.08312225341796875, "range_z": 0.06996583938598633}, {"start_time": 8497877, "end_time": 8513977, "duration": 16.1, "gyro_count": 141, "mean_x": 0.09974725047747295, "mean_y": -9.310270708503452, "mean_z": 3.799946558390949, "std_x": 0.003291112179091374, "std_y": 0.003993093137117577, "std_z": 0.007043520401662728, "range_x": 0.022724002599716187, "range_y": 0.02750873565673828, "range_z": 0.06518220901489258}, {"start_time": 8634177, "end_time": 8643277, "duration": 9.1, "gyro_count": 71, "mean_x": 0.12005481931944968, "mean_y": -9.25934784848925, "mean_z": 3.9195447606100164, "std_x": 0.0024478689472058226, "std_y": 0.002521629205816002, "std_z": 0.008733760917360785, "range_x": 0.014352001249790192, "range_y": 0.014949798583984375, "range_z": 0.059800148010253906}, {"start_time": 8736577, "end_time": 8753177, "duration": 16.6, "gyro_count": 146, "mean_x": 0.13390694869911834, "mean_y": -9.243081367179139, "mean_z": 3.9580228247054636, "std_x": 0.005025402171671206, "std_y": 0.0035160228580436255, "std_z": 0.005807612362495101, "range_x": 0.028703995048999786, "range_y": 0.02451801300048828, "range_z": 0.047241926193237305}, {"start_time": 9102077, "end_time": 9111877, "duration": 9.8, "gyro_count": 79, "mean_x": 0.2451724351584157, "mean_y": -9.319163986399204, "mean_z": 3.7711167758024193, "std_x": 0.005080062607567566, "std_y": 0.0046320462071599835, "std_z": 0.01069893779244076, "range_x": 0.029302015900611877, "range_y": 0.026311874389648438, "range_z": 0.08551406860351562}, {"start_time": 9189077, "end_time": 9220377, "duration": 31.3, "gyro_count": 294, "mean_x": 0.1978179979182425, "mean_y": -9.271233503510352, "mean_z": 3.892953643182508, "std_x": 0.006603200483386912, "std_y": 0.004822040200104086, "std_z": 0.0069223905794596014, "range_x": 0.05920200049877167, "range_y": 0.03169441223144531, "range_z": 0.0723581314086914}, {"start_time": 9384277, "end_time": 9475077, "duration": 90.8, "gyro_count": 888, "mean_x": 0.1759419745958603, "mean_y": -9.23034164175257, "mean_z": 3.9940266013145447, "std_x": 0.0064798811811215395, "std_y": 0.005705437563055379, "std_z": 0.007163183698593477, "range_x": 0.08431799709796906, "range_y": 0.07474994659423828, "range_z": 0.11959981918334961}, {"start_time": 9674277, "end_time": 9689377, "duration": 15.1, "gyro_count": 131, "mean_x": 0.12088273533882986, "mean_y": -9.263321497968136, "mean_z": 3.9181371790762167, "std_x": 0.010637037682803309, "std_y": 0.006378592222422899, "std_z": 0.006154184122054426, "range_x": 0.07295600324869156, "range_y": 0.04963397979736328, "range_z": 0.053221940994262695}, {"start_time": 9857677, "end_time": 9866577, "duration": 8.9, "gyro_count": 69, "mean_x": 0.037067334073177284, "mean_y": -9.200932267783344, "mean_z": 4.063557445139125, "std_x": 0.01034978764752312, "std_y": 0.004412279745213053, "std_z": 0.008877720721193343, "range_x": 0.0478400019928813, "range_y": 0.02930164337158203, "range_z": 0.048438072204589844}, {"start_time": 9898777, "end_time": 9903077, "duration": 4.3, "gyro_count": 24, "mean_x": 0.09802216943353415, "mean_y": -9.197489301363627, "mean_z": 4.071059505144755, "std_x": 0.002837477313504528, "std_y": 0.005021567601666641, "std_z": 0.013493170553355976, "range_x": 0.010764002799987793, "range_y": 0.019135475158691406, "range_z": 0.05800580978393555}, {"start_time": 9906277, "end_time": 9935977, "duration": 29.7, "gyro_count": 277, "mean_x": 0.07185499079606163, "mean_y": -9.219858379570585, "mean_z": 4.021083771536927, "std_x": 0.0036433664339187363, "std_y": 0.0038650986231984633, "std_z": 0.0060050292190131395, "range_x": 0.02033200114965439, "range_y": 0.026909828186035156, "range_z": 0.07176017761230469}, {"start_time": 10013177, "end_time": 10075180, "duration": 62.003, "gyro_count": 601, "mean_x": 0.05424188480041487, "mean_y": -9.236520140420973, "mean_z": 3.9851089051479907, "std_x": 0.01203374728697492, "std_y": 0.007552567303158278, "std_z": 0.008951804151433209, "range_x": 0.10225799959152937, "range_y": 0.0687704086303711, "range_z": 0.09867000579833984}, {"start_time": 10147377, "end_time": 10168178, "duration": 20.801, "gyro_count": 189, "mean_x": 0.1554578554535669, "mean_y": -9.270952345832946, "mean_z": 3.8988177032067033, "std_x": 0.0065068769256269015, "std_y": 0.004206595359309352, "std_z": 0.007857028328240107, "range_x": 0.05083000659942627, "range_y": 0.023920059204101562, "range_z": 0.07116198539733887}, {"start_time": 10274277, "end_time": 10345277, "duration": 71.0, "gyro_count": 690, "mean_x": 0.18865773718858111, "mean_y": -9.30165367126465, "mean_z": 3.825658276806707, "std_x": 0.00591166241552215, "std_y": 0.005116505826465807, "std_z": 0.008361278634241671, "range_x": 0.05561399459838867, "range_y": 0.05740833282470703, "range_z": 0.12498211860656738}, {"start_time": 10458577, "end_time": 10466277, "duration": 7.7, "gyro_count": 58, "mean_x": 0.08635944988707016, "mean_y": -9.239904551670469, "mean_z": 3.9721841729920486, "std_x": 0.002044199707958659, "std_y": 0.0034040893185853962, "std_z": 0.01117080123922427, "range_x": 0.012557998299598694, "range_y": 0.023322105407714844, "range_z": 0.07175970077514648}, {"start_time": 10561277, "end_time": 10621977, "duration": 60.7, "gyro_count": 587, "mean_x": 0.14476082755769945, "mean_y": -9.24910936891728, "mean_z": 3.951932498502975, "std_x": 0.005969515779291599, "std_y": 0.005057579848733468, "std_z": 0.006767704668986979, "range_x": 0.044252000749111176, "range_y": 0.047242164611816406, "range_z": 0.06817221641540527}, {"start_time": 10632877, "end_time": 10650477, "duration": 17.6, "gyro_count": 157, "mean_x": 0.1542230607218044, "mean_y": -9.275467854396553, "mean_z": 3.889300672871292, "std_x": 0.003977178386903271, "std_y": 0.003260574964685187, "std_z": 0.0079446908362845, "range_x": 0.03109601140022278, "range_y": 0.026909828186035156, "range_z": 0.07415199279785156}, {"start_time": 10698677, "end_time": 10735380, "duration": 36.703, "gyro_count": 348, "mean_x": 0.18610688320350374, "mean_y": -9.263561629700934, "mean_z": 3.9165976520242363, "std_x": 0.003206512028469329, "std_y": 0.003795951601986961, "std_z": 0.0048431957249684705, "range_x": 0.02511601150035858, "range_y": 0.04126167297363281, "range_z": 0.05621218681335449}, {"start_time": 10820877, "end_time": 10868077, "duration": 47.2, "gyro_count": 452, "mean_x": 0.1166285246907346, "mean_y": -9.244139491984274, "mean_z": 3.964875028724164, "std_x": 0.00816659034104063, "std_y": 0.004475384312584473, "std_z": 0.00529121968751333, "range_x": 0.11720799654722214, "range_y": 0.04185962677001953, "range_z": 0.05202603340148926}, {"start_time": 10948577, "end_time": 11031777, "duration": 83.2, "gyro_count": 813, "mean_x": 0.18981314763005222, "mean_y": -9.225191063546607, "mean_z": 4.006503723057727, "std_x": 0.005048579757100094, "std_y": 0.0035346635416656285, "std_z": 0.004791451040376871, "range_x": 0.033487990498542786, "range_y": 0.026909828186035156, "range_z": 0.0532221794128418}, {"start_time": 11137177, "end_time": 11183077, "duration": 45.9, "gyro_count": 439, "mean_x": 0.10993528729283457, "mean_y": -9.323340648399126, "mean_z": 3.7551349572548833, "std_x": 0.004092657315264152, "std_y": 0.003125962902650183, "std_z": 0.005069345001613965, "range_x": 0.042458005249500275, "range_y": 0.029302597045898438, "range_z": 0.07116198539733887}, {"start_time": 11350678, "end_time": 11377277, "duration": 26.599, "gyro_count": 246, "mean_x": -0.03570011458776104, "mean_y": -9.267796857570245, "mean_z": 3.882972092163272, "std_x": 0.0035043920257368877, "std_y": 0.0027694190794509597, "std_z": 0.006057340703632391, "range_x": 0.022126000374555588, "range_y": 0.01973438262939453, "range_z": 0.06458401679992676}, {"start_time": 11576177, "end_time": 11589677, "duration": 13.5, "gyro_count": 115, "mean_x": 0.002766400054299637, "mean_y": -9.312872762265412, "mean_z": 3.7823292939559274, "std_x": 0.005005396425915952, "std_y": 0.002818650449564012, "std_z": 0.008583980261745125, "range_x": 0.04006600007414818, "range_y": 0.022126197814941406, "range_z": 0.08252406120300293}, {"start_time": 11679077, "end_time": 11707877, "duration": 28.8, "gyro_count": 268, "mean_x": 0.07705721085362915, "mean_y": -9.24084963015656, "mean_z": 3.9562765982613635, "std_x": 0.00425826455402801, "std_y": 0.0045215210000802315, "std_z": 0.006339496371505762, "range_x": 0.03827199712395668, "range_y": 0.052623748779296875, "range_z": 0.08013200759887695}, {"start_time": 11882777, "end_time": 11927377, "duration": 44.6, "gyro_count": 427, "mean_x": 0.1374671783743195, "mean_y": -9.322778219361496, "mean_z": 3.76121701289675, "std_x": 0.003208795272710596, "std_y": 0.002858928602153182, "std_z": 0.005454642036203809, "range_x": 0.023919999599456787, "range_y": 0.022125244140625, "range_z": 0.07594609260559082}, {"start_time": 11936777, "end_time": 11954677, "duration": 17.9, "gyro_count": 159, "mean_x": -0.03988171136885319, "mean_y": -9.33357660425534, "mean_z": 3.73638306473786, "std_x": 0.0031123571356629323, "std_y": 0.003747306007612048, "std_z": 0.007082699281982456, "range_x": 0.01793999969959259, "range_y": 0.026910781860351562, "range_z": 0.06219196319580078}, {"start_time": 12026977, "end_time": 12123578, "duration": 96.601, "gyro_count": 946, "mean_x": 0.2179956578868136, "mean_y": -9.2486796348846, "mean_z": 3.9409326012492434, "std_x": 0.0028698782764251664, "std_y": 0.0024337247039333577, "std_z": 0.004016384458236488, "range_x": 0.01853799819946289, "range_y": 0.015547752380371094, "range_z": 0.07355403900146484}, {"start_time": 12307677, "end_time": 12355477, "duration": 47.8, "gyro_count": 458, "mean_x": 0.11189389775413613, "mean_y": -9.237831184437182, "mean_z": 3.965945229259641, "std_x": 0.004575154408456888, "std_y": 0.003602570246598022, "std_z": 0.005366123697383598, "range_x": 0.03588000684976578, "range_y": 0.04126167297363281, "range_z": 0.06219220161437988}, {"start_time": 12512877, "end_time": 12545678, "duration": 32.801, "gyro_count": 308, "mean_x": 0.1650227638227599, "mean_y": -9.245637317756554, "mean_z": 3.9465709793103207, "std_x": 0.0041998142640648625, "std_y": 0.002989364639307011, "std_z": 0.005342882917981234, "range_x": 0.03468400239944458, "range_y": 0.028106689453125, "range_z": 0.07654404640197754}, {"start_time": 12570977, "end_time": 12622177, "duration": 51.2, "gyro_count": 492, "mean_x": 0.17660933699670847, "mean_y": -9.252142037802596, "mean_z": 3.9330716225189892, "std_x": 0.004331394543604116, "std_y": 0.0038005029226195262, "std_z": 0.005131580783331635, "range_x": 0.03468400239944458, "range_y": 0.035880088806152344, "range_z": 0.06518197059631348}, {"start_time": 12731277, "end_time": 12776877, "duration": 45.6, "gyro_count": 436, "mean_x": 0.04170089987867059, "mean_y": -9.221117577421555, "mean_z": 4.007811162996729, "std_x": 0.003907081166796194, "std_y": 0.0037210443531830754, "std_z": 0.004515672113579765, "range_x": 0.04963400401175022, "range_y": 0.04305553436279297, "range_z": 0.0502316951751709}, {"start_time": 13447477, "end_time": 13459377, "duration": 11.9, "gyro_count": 99, "mean_x": 0.16394260962202092, "mean_y": -9.268486697264391, "mean_z": 3.8962721102165454, "std_x": 0.002928796824885252, "std_y": 0.0030142130752964374, "std_z": 0.008354261309004008, "range_x": 0.015547990798950195, "range_y": 0.014950752258300781, "range_z": 0.06458401679992676}], "failed_intervals": [], "all_passed": true}]}