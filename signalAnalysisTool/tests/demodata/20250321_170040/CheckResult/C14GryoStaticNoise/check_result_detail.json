{"checker": "C14GryoStaticNoise", "title": "陀螺仪静态噪声检查详情", "version": "1.0", "date": "2025-07-10 18:42:20", "type": "CheckResultDetail", "checkResultDetail": [{"checkItem": "陀螺静态噪声过大", "static_intervals": [{"start_time": 8247477, "end_time": 8260777, "duration": 13.3, "start_time_adj": 8248477, "end_time_adj": 8259777}, {"start_time": 8279877, "end_time": 8344877, "duration": 65.0, "start_time_adj": 8280877, "end_time_adj": 8343877}, {"start_time": 8355677, "end_time": 8379277, "duration": 23.6, "start_time_adj": 8356677, "end_time_adj": 8378277}, {"start_time": 8477877, "end_time": 8488077, "duration": 10.2, "start_time_adj": 8478877, "end_time_adj": 8487077}, {"start_time": 8497877, "end_time": 8513977, "duration": 16.1, "start_time_adj": 8498877, "end_time_adj": 8512977}, {"start_time": 8634177, "end_time": 8643277, "duration": 9.1, "start_time_adj": 8635177, "end_time_adj": 8642277}, {"start_time": 8736577, "end_time": 8753177, "duration": 16.6, "start_time_adj": 8737577, "end_time_adj": 8752177}, {"start_time": 9102077, "end_time": 9111877, "duration": 9.8, "start_time_adj": 9103077, "end_time_adj": 9110877}, {"start_time": 9189077, "end_time": 9220377, "duration": 31.3, "start_time_adj": 9190077, "end_time_adj": 9219377}, {"start_time": 9384277, "end_time": 9475077, "duration": 90.8, "start_time_adj": 9385277, "end_time_adj": 9474077}, {"start_time": 9674277, "end_time": 9689377, "duration": 15.1, "start_time_adj": 9675277, "end_time_adj": 9688377}, {"start_time": 9857677, "end_time": 9866577, "duration": 8.9, "start_time_adj": 9858677, "end_time_adj": 9865577}, {"start_time": 9898777, "end_time": 9903077, "duration": 4.3, "start_time_adj": 9899777, "end_time_adj": 9902077}, {"start_time": 9906277, "end_time": 9935977, "duration": 29.7, "start_time_adj": 9907277, "end_time_adj": 9934977}, {"start_time": 10013177, "end_time": 10075180, "duration": 62.003, "start_time_adj": 10014177, "end_time_adj": 10074180}, {"start_time": 10147377, "end_time": 10168178, "duration": 20.801, "start_time_adj": 10148377, "end_time_adj": 10167178}, {"start_time": 10274277, "end_time": 10345277, "duration": 71.0, "start_time_adj": 10275277, "end_time_adj": 10344277}, {"start_time": 10458577, "end_time": 10466277, "duration": 7.7, "start_time_adj": 10459577, "end_time_adj": 10465277}, {"start_time": 10561277, "end_time": 10621977, "duration": 60.7, "start_time_adj": 10562277, "end_time_adj": 10620977}, {"start_time": 10632877, "end_time": 10650477, "duration": 17.6, "start_time_adj": 10633877, "end_time_adj": 10649477}, {"start_time": 10698677, "end_time": 10735380, "duration": 36.703, "start_time_adj": 10699677, "end_time_adj": 10734380}, {"start_time": 10820877, "end_time": 10868077, "duration": 47.2, "start_time_adj": 10821877, "end_time_adj": 10867077}, {"start_time": 10948577, "end_time": 11031777, "duration": 83.2, "start_time_adj": 10949577, "end_time_adj": 11030777}, {"start_time": 11137177, "end_time": 11183077, "duration": 45.9, "start_time_adj": 11138177, "end_time_adj": 11182077}, {"start_time": 11350678, "end_time": 11377277, "duration": 26.599, "start_time_adj": 11351678, "end_time_adj": 11376277}, {"start_time": 11576177, "end_time": 11589677, "duration": 13.5, "start_time_adj": 11577177, "end_time_adj": 11588677}, {"start_time": 11679077, "end_time": 11707877, "duration": 28.8, "start_time_adj": 11680077, "end_time_adj": 11706877}, {"start_time": 11882777, "end_time": 11927377, "duration": 44.6, "start_time_adj": 11883777, "end_time_adj": 11926377}, {"start_time": 11936777, "end_time": 11954677, "duration": 17.9, "start_time_adj": 11937777, "end_time_adj": 11953677}, {"start_time": 12026977, "end_time": 12123578, "duration": 96.601, "start_time_adj": 12027977, "end_time_adj": 12122578}, {"start_time": 12307677, "end_time": 12355477, "duration": 47.8, "start_time_adj": 12308677, "end_time_adj": 12354477}, {"start_time": 12512877, "end_time": 12545678, "duration": 32.801, "start_time_adj": 12513877, "end_time_adj": 12544678}, {"start_time": 12570977, "end_time": 12622177, "duration": 51.2, "start_time_adj": 12571977, "end_time_adj": 12621177}, {"start_time": 12731277, "end_time": 12776877, "duration": 45.6, "start_time_adj": 12732277, "end_time_adj": 12775877}, {"start_time": 13447477, "end_time": 13459377, "duration": 11.9, "start_time_adj": 13448477, "end_time_adj": 13458377}], "long_static_intervals": [{"start_time": 8247477, "end_time": 8260777, "duration": 13.3, "gyro_count": 113, "mean_x": 0.6584000160208846, "mean_y": -1.118589600630566, "mean_z": -0.5352069925417943, "std_x": 0.018161579903582577, "std_y": 0.015431899898946198, "std_z": 0.015840042941940892, "range_x": 0.08766257762908936, "range_y": 0.061363816261291504, "range_z": 0.08766263723373413}, {"start_time": 8279877, "end_time": 8344877, "duration": 65.0, "gyro_count": 630, "mean_x": 0.6597510962259202, "mean_y": -1.1225954303665766, "mean_z": -0.5325569211490571, "std_x": 0.019807496952725892, "std_y": 0.017296871279909663, "std_z": 0.01393702067974818, "range_x": 0.13149386644363403, "range_y": 0.11396133899688721, "range_z": 0.1051950752735138}, {"start_time": 8355677, "end_time": 8379277, "duration": 23.6, "gyro_count": 216, "mean_x": 0.6579966884520319, "mean_y": -1.1253273431901578, "mean_z": -0.5330775634006217, "std_x": 0.02009906484366882, "std_y": 0.018137434549962226, "std_z": 0.015718172083729682, "range_x": 0.10519510507583618, "range_y": 0.0964287519454956, "range_z": 0.08766254782676697}, {"start_time": 8477877, "end_time": 8488077, "duration": 10.2, "gyro_count": 82, "mean_x": 0.6589657649761294, "mean_y": -1.1241117875750473, "mean_z": -0.5318550820030817, "std_x": 0.020881715070457496, "std_y": 0.017110683687936088, "std_z": 0.019868969276425043, "range_x": 0.11396133899688721, "range_y": 0.0876624584197998, "range_z": 0.13149377703666687}, {"start_time": 8497877, "end_time": 8513977, "duration": 16.1, "gyro_count": 141, "mean_x": 0.6597694543236536, "mean_y": -1.127427378444807, "mean_z": -0.529208223659096, "std_x": 0.01947961422925358, "std_y": 0.0152115004388075, "std_z": 0.014943505379663082, "range_x": 0.1051950454711914, "range_y": 0.07889628410339355, "range_z": 0.08766254782676697}, {"start_time": 8634177, "end_time": 8643277, "duration": 9.1, "gyro_count": 71, "mean_x": 0.6583333661858465, "mean_y": -1.1271427903376834, "mean_z": -0.530543606465971, "std_x": 0.02085846948894412, "std_y": 0.013308036615383271, "std_z": 0.012905762555310637, "range_x": 0.11396133899688721, "range_y": 0.061363816261291504, "range_z": 0.07013002038002014}, {"start_time": 8736577, "end_time": 8753177, "duration": 16.6, "gyro_count": 146, "mean_x": 0.666895812504912, "mean_y": -1.123341485245587, "mean_z": -0.5281968610743953, "std_x": 0.017504848536557826, "std_y": 0.014595924289685577, "std_z": 0.01407171091928535, "range_x": 0.0964287519454956, "range_y": 0.0701301097869873, "range_z": 0.07889634370803833}, {"start_time": 9102077, "end_time": 9111877, "duration": 9.8, "gyro_count": 79, "mean_x": 0.6773318633248534, "mean_y": -1.1317345598075963, "mean_z": -0.5289713381966458, "std_x": 0.021505355525619246, "std_y": 0.015286729401885878, "std_z": 0.01484805605265477, "range_x": 0.14902633428573608, "range_y": 0.0788964033126831, "range_z": 0.08766254782676697}, {"start_time": 9189077, "end_time": 9220377, "duration": 31.3, "gyro_count": 293, "mean_x": 0.6716506747662411, "mean_y": -1.12997918487002, "mean_z": -0.5275908997441314, "std_x": 0.020527864530509494, "std_y": 0.016703283599329394, "std_z": 0.015845559467877454, "range_x": 0.1051950454711914, "range_y": 0.0876626968383789, "range_z": 0.08766248822212219}, {"start_time": 9384277, "end_time": 9475077, "duration": 90.8, "gyro_count": 888, "mean_x": 0.6753569845412228, "mean_y": -1.128260396890812, "mean_z": -0.5244352596702876, "std_x": 0.01769125743086979, "std_y": 0.016331850766988294, "std_z": 0.01758824279917223, "range_x": 0.12272757291793823, "range_y": 0.1051950454711914, "range_z": 0.2103901505470276}, {"start_time": 9674277, "end_time": 9689377, "duration": 15.1, "gyro_count": 131, "mean_x": 0.6723917959300616, "mean_y": -1.1339250593695023, "mean_z": -0.5208895097252066, "std_x": 0.0173527127665756, "std_y": 0.018767641983953233, "std_z": 0.019988781560382515, "range_x": 0.08766251802444458, "range_y": 0.09642887115478516, "range_z": 0.11396127939224243}, {"start_time": 9857677, "end_time": 9866577, "duration": 8.9, "gyro_count": 69, "mean_x": 0.6736040702764539, "mean_y": -1.1267813288647195, "mean_z": -0.5224179495935855, "std_x": 0.021453008411786125, "std_y": 0.01795160360778286, "std_z": 0.02169896068379329, "range_x": 0.1051950454711914, "range_y": 0.07889628410339355, "range_z": 0.1051950752735138}, {"start_time": 9898777, "end_time": 9903077, "duration": 4.3, "gyro_count": 24, "mean_x": 0.6826720635096232, "mean_y": -1.1290205319722493, "mean_z": -0.5164785037438074, "std_x": 0.029958720213833327, "std_y": 0.012919724953025208, "std_z": 0.01315996320866317, "range_x": 0.11396127939224243, "range_y": 0.06136369705200195, "range_z": 0.052597492933273315}, {"start_time": 9906277, "end_time": 9935977, "duration": 29.7, "gyro_count": 278, "mean_x": 0.6770512590305411, "mean_y": -1.1273466408681527, "mean_z": -0.5195740215855537, "std_x": 0.017735477427556056, "std_y": 0.01479190603301652, "std_z": 0.014527797991031117, "range_x": 0.09642881155014038, "range_y": 0.0788964033126831, "range_z": 0.07889631390571594}, {"start_time": 10013177, "end_time": 10075180, "duration": 62.003, "gyro_count": 601, "mean_x": 0.6710487582322563, "mean_y": -1.131211485719919, "mean_z": -0.5220953672380495, "std_x": 0.02031049435593353, "std_y": 0.02067658426809363, "std_z": 0.024352388633779188, "range_x": 0.18409138917922974, "range_y": 0.19285762310028076, "range_z": 0.22792261838912964}, {"start_time": 10147377, "end_time": 10168178, "duration": 20.801, "gyro_count": 189, "mean_x": 0.6801500310973515, "mean_y": -1.135160386246979, "mean_z": -0.5219864009549378, "std_x": 0.018571944184982144, "std_y": 0.01636569398938873, "std_z": 0.016925954797024852, "range_x": 0.1051950454711914, "range_y": 0.09642887115478516, "range_z": 0.096428781747818}, {"start_time": 10274277, "end_time": 10345277, "duration": 71.0, "gyro_count": 690, "mean_x": 0.6795879993749702, "mean_y": -1.1374405758968298, "mean_z": -0.5237392483846001, "std_x": 0.019468138887283794, "std_y": 0.016958913756153614, "std_z": 0.016131388947783612, "range_x": 0.12272757291793823, "range_y": 0.12272751331329346, "range_z": 0.13149386644363403}, {"start_time": 10458577, "end_time": 10466277, "duration": 7.7, "gyro_count": 57, "mean_x": 0.6776160942880731, "mean_y": -1.1337689140386749, "mean_z": -0.5199773217502394, "std_x": 0.018069438260546335, "std_y": 0.013079612534067971, "std_z": 0.009961775273442912, "range_x": 0.08766251802444458, "range_y": 0.0525975227355957, "range_z": 0.04383128881454468}, {"start_time": 10561277, "end_time": 10621977, "duration": 60.7, "gyro_count": 587, "mean_x": 0.6778987907063616, "mean_y": -1.1349835440491087, "mean_z": -0.5207035769494266, "std_x": 0.01902439064681785, "std_y": 0.015467188191521217, "std_z": 0.01623023501857961, "range_x": 0.13149386644363403, "range_y": 0.0876626968383789, "range_z": 0.15779253840446472}, {"start_time": 10632877, "end_time": 10650477, "duration": 17.6, "gyro_count": 157, "mean_x": 0.6762858219207473, "mean_y": -1.1336944657526198, "mean_z": -0.5175440396852554, "std_x": 0.018564625409963183, "std_y": 0.014662564412664739, "std_z": 0.013088756751177202, "range_x": 0.13149380683898926, "range_y": 0.07012999057769775, "range_z": 0.07889625430107117}, {"start_time": 10698677, "end_time": 10735380, "duration": 36.703, "gyro_count": 347, "mean_x": 0.6754815922003314, "mean_y": -1.1374657270887742, "mean_z": -0.5222868844652039, "std_x": 0.01957903827261096, "std_y": 0.013981733900530166, "std_z": 0.012876903533961983, "range_x": 0.12272757291793823, "range_y": 0.0788964033126831, "range_z": 0.08766257762908936}, {"start_time": 10820877, "end_time": 10868077, "duration": 47.2, "gyro_count": 452, "mean_x": 0.6689117651070113, "mean_y": -1.131021381479449, "mean_z": -0.521010318161112, "std_x": 0.01913064164693252, "std_y": 0.016328926495991793, "std_z": 0.020377663598404927, "range_x": 0.11396133899688721, "range_y": 0.11396121978759766, "range_z": 0.2542213797569275}, {"start_time": 10948577, "end_time": 11031777, "duration": 83.2, "gyro_count": 813, "mean_x": 0.6780854207709853, "mean_y": -1.1324210916204793, "mean_z": -0.5185245014498008, "std_x": 0.017026969181155992, "std_y": 0.015238899457607825, "std_z": 0.01525105456998228, "range_x": 0.09642881155014038, "range_y": 0.09642887115478516, "range_z": 0.1051950454711914}, {"start_time": 11137177, "end_time": 11183077, "duration": 45.9, "gyro_count": 439, "mean_x": 0.6564706530006166, "mean_y": -1.1214815277716566, "mean_z": -0.5320457550686422, "std_x": 0.016170165674874937, "std_y": 0.015458139479017833, "std_z": 0.013465699267409158, "range_x": 0.08766257762908936, "range_y": 0.07889628410339355, "range_z": 0.07889625430107117}, {"start_time": 11350678, "end_time": 11377277, "duration": 26.599, "gyro_count": 246, "mean_x": 0.6462796446753711, "mean_y": -1.1108198742556379, "mean_z": -0.5382694073808871, "std_x": 0.019651505679288802, "std_y": 0.015582046418404, "std_z": 0.014181217753502357, "range_x": 0.11396127939224243, "range_y": 0.0876624584197998, "range_z": 0.07889634370803833}, {"start_time": 11576177, "end_time": 11589677, "duration": 13.5, "gyro_count": 115, "mean_x": 0.6569354881411014, "mean_y": -1.1136954794759335, "mean_z": -0.5396963632625082, "std_x": 0.01707621756522231, "std_y": 0.01452054518368281, "std_z": 0.013040263292507976, "range_x": 0.08766263723373413, "range_y": 0.07889628410339355, "range_z": 0.07013005018234253}, {"start_time": 11679077, "end_time": 11707877, "duration": 28.8, "gyro_count": 268, "mean_x": 0.65576817273204, "mean_y": -1.1197581851660316, "mean_z": -0.5422648010191633, "std_x": 0.01643005506868895, "std_y": 0.020458260774878004, "std_z": 0.01622470372962171, "range_x": 0.08766257762908936, "range_y": 0.1665588617324829, "range_z": 0.12272748351097107}, {"start_time": 11882777, "end_time": 11927377, "duration": 44.6, "gyro_count": 426, "mean_x": 0.6663382295431666, "mean_y": -1.1197346896632736, "mean_z": -0.5372314712671047, "std_x": 0.016186201405110786, "std_y": 0.01498274641091831, "std_z": 0.013584817974284038, "range_x": 0.0964287519454956, "range_y": 0.07889628410339355, "range_z": 0.096428781747818}, {"start_time": 11936777, "end_time": 11954677, "duration": 17.9, "gyro_count": 159, "mean_x": 0.660666841006129, "mean_y": -1.1205368416864168, "mean_z": -0.5365058029972533, "std_x": 0.01910996777670954, "std_y": 0.01686898255899495, "std_z": 0.013622807011891093, "range_x": 0.08766257762908936, "range_y": 0.09642887115478516, "range_z": 0.08766263723373413}, {"start_time": 12026977, "end_time": 12123578, "duration": 96.601, "gyro_count": 946, "mean_x": 0.6626120801959935, "mean_y": -1.1247030499117572, "mean_z": -0.5352326601932719, "std_x": 0.016989285087388315, "std_y": 0.01411020703927918, "std_z": 0.012443278113298126, "range_x": 0.1051950454711914, "range_y": 0.07889628410339355, "range_z": 0.07013005018234253}, {"start_time": 12307677, "end_time": 12355477, "duration": 47.8, "gyro_count": 458, "mean_x": 0.6702739410265044, "mean_y": -1.129870679961542, "mean_z": -0.5308369105299487, "std_x": 0.018069967123609317, "std_y": 0.016378706914624425, "std_z": 0.01478028976653204, "range_x": 0.11396133899688721, "range_y": 0.1314939260482788, "range_z": 0.11396124958992004}, {"start_time": 12512877, "end_time": 12545678, "duration": 32.801, "gyro_count": 308, "mean_x": 0.6683699788598271, "mean_y": -1.1311029861499737, "mean_z": -0.532379191707481, "std_x": 0.016704409325795495, "std_y": 0.015426756299684326, "std_z": 0.012742192658188238, "range_x": 0.1051950454711914, "range_y": 0.0876624584197998, "range_z": 0.08766254782676697}, {"start_time": 12570977, "end_time": 12622177, "duration": 51.2, "gyro_count": 492, "mean_x": 0.6631528996839756, "mean_y": -1.1255193786407873, "mean_z": -0.5263672642470375, "std_x": 0.018824683801286733, "std_y": 0.01598838635711182, "std_z": 0.014324425822586705, "range_x": 0.11396133899688721, "range_y": 0.0876624584197998, "range_z": 0.1051950752735138}, {"start_time": 12731277, "end_time": 12776877, "duration": 45.6, "gyro_count": 436, "mean_x": 0.6716036659861924, "mean_y": -1.125840424124254, "mean_z": -0.5273827038226871, "std_x": 0.017224978376950483, "std_y": 0.015447704135276685, "std_z": 0.014282866755858825, "range_x": 0.0964287519454956, "range_y": 0.09642887115478516, "range_z": 0.08766254782676697}, {"start_time": 13447477, "end_time": 13459377, "duration": 11.9, "gyro_count": 99, "mean_x": 0.6643758295762419, "mean_y": -1.1308468291253755, "mean_z": -0.5298713903234462, "std_x": 0.019411907520989632, "std_y": 0.01342964003239601, "std_z": 0.012906945121336898, "range_x": 0.11396133899688721, "range_y": 0.07889628410339355, "range_z": 0.061363816261291504}], "failed_intervals": [], "all_passed": true}]}