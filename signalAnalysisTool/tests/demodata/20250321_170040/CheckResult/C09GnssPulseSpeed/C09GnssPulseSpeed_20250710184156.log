2025-07-10 18:41:56,591 - C09GnssPulseSpeed - INFO - init
2025-07-10 18:41:56,591 - C09GnssPulseSpeed - INFO - check
2025-07-10 18:41:58,524 - C09GnssPulseSpeed - INFO - cross_correlation_function, gnss length : 5426 , pulse length : 53991
2025-07-10 18:41:58,530 - C09GnssPulseSpeed - INFO - 最大延迟 max_lag = 0.0 s
2025-07-10 18:41:58,530 - C09GnssPulseSpeed - INFO - check_result: {'checker': 'gnssPulseSpeedChecker', 'title': 'GNSS速度和轮速不同步', 'datatxt': '/Users/<USER>/Documents/workspace/python/vae_issue_tools/signalAnalysisTool/tests/demodata/20250321_170040/AutoSdkDemo/online/vlane/20250321_170040/data.txt', 'version': '1.0', 'date': '2025-07-10 18:41:56', 'type': 'CheckResult', 'checkResult': {'checkItem': 'GNSS速度和轮速不同步相关性函数', 'checkList': [{'checkDataTime': '8089920 - 13519128', 'checkResultStatistic': '最大延迟0.0s', 'checkResult': 'Pass'}], 'checkRules': '最大延迟<=0.2s', 'description': '不通过需手动排查'}}
