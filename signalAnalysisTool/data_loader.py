import argparse
import os
import sys
from util.oss import get_oss_folsers, download_directory_from_oss, download_oss2local

# todo 1. 输入的oss路径格式,目前是（oss://vlane/triptracker/record/20250514_161242/AutoSdkDemo/online/vlane/20250514_161242/）
# todo 2. 上传到oss的路径

current_dir = os.path.dirname(os.path.abspath(__file__))
# parent_dir = os.path.dirname(os.path.dirname(current_dir)) 
sys.path.insert(0, current_dir)

class DataLoader(object):
    def __init__(self, oss_path: str, data_save_path: str):
        self._data_save_path = data_save_path
        self._data_txt_path = os.path.join(oss_path , "AutoSdkDemo/online/vlane/" + os.path.basename(os.path.normpath(oss_path)))
        self._replay_record_path = os.path.join(oss_path , "AutoSdkDemo/replay_record/")
        self._result_path = ""
        self._oss_path = oss_path

    
        pass

    def download_data_if_need(self):

        # file_path = self._data_txt_path
        print(f"download_data_if_need， data.txt file path: {self._data_txt_path}")
        
        # 构建基本保存路径
        save_path = os.path.join(self._data_save_path, "tests/demodata")
        print(f"data save_path: {save_path}")

        if self._data_txt_path.startswith('oss://'):
            # 下载数据 data.txt
            # 提取对象键中从 'record' 开始的路径部分
            oss_suffix = self._data_txt_path.partition('record')[-1].lstrip('/')
            uri = os.path.join(save_path, oss_suffix)

            print(f"Downloading OSS files data.txt from {self._data_txt_path} to local path {uri}")
            # download_directory_from_oss(self._data_txt_path, uri)

            # download_oss2local(os.path.join(self._data_txt_path, "data.txt"), os.path.join(uri, "data.txt"))

            # 下载replay_record
            oss_suffix = self._replay_record_path.partition('record')[-1].lstrip('/')
            uri = os.path.join(save_path, oss_suffix)

            print(f"Downloading OSS files replay from {self._replay_record_path} to local path {uri}")
            # download_directory_from_oss(self._replay_record_path, uri)

            # 下载真值数据
            gt_path = ""
            folders = get_oss_folsers(self._oss_path)
            for folder in folders:
                if folder.startswith("GT_") and folder.endswith("_plaintext"):
                    gt_path = os.path.join(self._oss_path, folder)
                    break
            
            if gt_path:
                oss_suffix = gt_path.partition('record')[-1].lstrip('/')
                uri = os.path.join(save_path, oss_suffix)

                print(f"Downloading OSS files gt from {self._oss_path} to local path {uri}")
                # download_directory_from_oss(gt_path, uri)

                # download_oss2local(os.path.join(gt_path, "loc_gt.txt"), os.path.join(uri, "loc_gt.txt"))

            # 下载summary.txt文件，需要手动查找路径
            summary_path = "oss://'"
            if summary_path.startswith('oss://'):
                oss_suffix = summary_path.partition('record')[-1].lstrip('/')
                uri = os.path.join(save_path, oss_suffix)

                print(f"Downloading OSS files summary from {summary_path} to local path {uri}")
                # download_oss2local(summary_path, uri)
           

        else:
            # 如果是本地路径直接返回
            print(f"File is already local at {self._data_txt_path}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", "-i", help="数据根路径")
    args = parser.parse_args()

     # 1. 输入的oss路径格式（oss://vlane/triptracker/record/20250514_161242/AutoSdkDemo/online/vlane/20250514_161242/）
    oss_path = args.input
    if "AutoSdkDemo" in oss_path:
        oss_path = oss_path.split("AutoSdkDemo")[0]
    
    if not oss_path.endswith("/"):
        oss_path = oss_path + '/'


    dataLoader = DataLoader(oss_path, current_dir)

    dataLoader.download_data_if_need()

